<?php

use App\Http\Middleware\SingleUseSignedRouteMiddleware;
use Modules\Core\Http\Middleware\SetLocaleMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Sentry\Laravel\Integration;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'locale' => SetLocaleMiddleware::class,
            'single-use' => SingleUseSignedRouteMiddleware::class
        ]);

    })
    ->withSchedule(function ($schedule) {
        $schedule->command('queue:work --max-time=600')
         ->everyTenMinutes()
         ->runInBackground()
         ->withoutOverlapping(10);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->dontReportDuplicates();
        $exceptions->reportable(function (Throwable $e) {
            Integration::captureUnhandledException($e);
        });
    })->create();
