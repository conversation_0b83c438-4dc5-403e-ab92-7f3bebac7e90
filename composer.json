{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-imagick": "*", "ext-intl": "*", "amidesfahani/filament-tinyeditor": "^2.1", "blade-ui-kit/blade-ui-kit": "^0.6.2", "calebporzio/sushi": "*", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "filament/spatie-laravel-tags-plugin": "^3.2", "filament/spatie-laravel-translatable-plugin": "^3.2", "flowframe/laravel-trend": "^0.2.0", "intervention/image": "^3.11", "laravel/breeze": "^2.0", "laravel/folio": "^1.1", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "livewire/livewire": "^3.4", "livewire/volt": "^1.6", "lorisleiva/laravel-actions": "^2.8", "nwidart/laravel-modules": "^11.0", "sentry/sentry-laravel": "^4.13", "setasign/fpdf": "^1.8", "setasign/fpdi": "^2.6", "spatie/laravel-data": "^4.8", "spatie/laravel-permission": "^6.18", "stichoza/google-translate-php": "*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "fakerphp/faker": "^1.23", "laravel/pint": "^1.22", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^2.0", "pestphp/pest-plugin-laravel": "^2.0", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan vendor:publish --tag=livewire:assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}, "merge-plugin": {"include": ["Modules/*/composer.json", "plugins/*/composer.json"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}