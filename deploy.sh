#!/bin/sh

# stops the execution of a script if a command or pipeline has an error
set -e

DEPLOYMENT_DIRECTORY=$(date '+%Y%m%d%H%M%S')

SITE_PATH=$HOME/htdocs/$1
SITE=${SITE_PATH#"$HOME/htdocs/"}
DEPL="$HOME/htdocs/deployments/${SITE}"

mkdir -p ${DEPL}

echo "Deployment started ..."

cd ${DEPL}

git clone --depth 1 --<NAME_EMAIL>:arabic-for-all/arbicforall.git $DEPLOYMENT_DIRECTORY

cd $DEPLOYMENT_DIRECTORY

echo "removing storage"
rm -Rf storage

echo "link master storage and .env"
cp $HOME/htdocs/.env .env
ln -s -n -f -T $HOME/htdocs/storage $DEPL/$DEPLOYMENT_DIRECTORY/storage

composer install --no-dev --no-interaction --optimize-autoloader
npm install
npm run build
rm -rf node_modules

echo "optimize command"
php artisan clear-compiled
php artisan route:clear
php artisan view:clear
php artisan config:clear
php artisan storage:link

echo "migrations"
php artisan migrate
php artisan vendor:publish --tag=livewire:assets


cd ..

#link to the latest deployment from Live
ln -s -n -f -T $DEPL/$DEPLOYMENT_DIRECTORY $SITE_PATH

#rsync -rtv $DEPL/$DEPLOYMENT_DIRECTORY $SITE_PATH

# Remove previous deploys
cd $DEPL
find . -maxdepth 1 -mindepth 1 -type d -printf "%T+ %f\0" | sort -z | head -z -n -2 | cut -z -d' ' -f 2- | xargs -0 rm -rf &

# Notify deployment

echo "Deployed to $SITE"
