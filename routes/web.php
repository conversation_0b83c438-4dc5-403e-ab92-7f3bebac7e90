<?php

use Illuminate\Support\Facades\Route;
use Modules\Billing\Http\Controllers\InvoiceController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::prefix('billing')->name('billing.')->group(function () {
    // Invoice routes
    Route::get('invoices/{invoice}/pdf', [InvoiceController::class, 'downloadPdf'])->name('invoices.pdf');
    Route::get('invoices/{invoice}/send', [InvoiceController::class, 'sendToCustomer'])->name('invoices.send');

    // Payment callback routes
    Route::get('payment/success', [InvoiceController::class, 'paymentSuccess'])->name('payment.success');
    Route::get('payment/cancel', [InvoiceController::class, 'paymentCancel'])->name('payment.cancel');
});

// Admin routes will be handled by Filament
