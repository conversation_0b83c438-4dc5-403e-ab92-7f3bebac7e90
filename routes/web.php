<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Livewire\Volt\Volt;
use Modules\Course\Models\Lesson;
use Modules\Course\Models\Unit;
use Modules\Institute\Http\Controllers\InstituteInvitationController;
use Modules\MediaLibrary\Models\Audio;
use Modules\MediaLibrary\Models\Video;

// Route::view('/', 'welcome');

Route::get('/test', function () {
    dd(\Modules\Course\Models\Course::find(3)->isPurchasedBy(auth()->user()));
})->name('welcome');

//Route::redirect('home', '/')->name('home');
//Route::redirect('dashboard', '/profile')->name('dashboard');

Volt::route('/', 'index')->name('home');
Volt::route('/courses', 'courses.index')->name('courses.index');
Volt::route('/course/{course:slug}', 'courses.show')->name('course.show');
Volt::route('/p/{page:slug}', 'p.page')->name('page.show');
Volt::route('/course/{course:slug}/modules', 'courses.modules.index')->name('modules.index');

// group all routes that need to be authenticated
Route::middleware(['auth'])->group(function () {
    Volt::route('/course/{course:slug}/module/{module}', 'courses.modules.show')->name('module.show');
    Volt::route('/course/checkout/{course:slug}', 'checkout.course')->name('courses.checkout');
});

// Route::view('/units', 'units')
//    ->middleware(['theme']);
//
// Route::view('/course', 'course')
//    ->middleware(['theme']);

Route::get('locale/{locale}', function ($locale) {
    session()->put('locale', $locale);

    return redirect()->back();
})->name('change.locale');

require __DIR__.'/auth.php';

// Route::get('/storage/{path}', function ($path) {
//    return Storage::get($path);
// })->where('path', '.*');

Route::get('/favicon.png', function () {
    return response()->redirectTo(Storage::url(\Settings::get('favicon')), 302, [
        'Content-Type' => 'image/png',
    ]);
});

Route::get('/invitations/{invitation}', [InstituteInvitationController::class, 'accept'])
    ->middleware(['signed'])
    ->name('invitations.accept');

Route::get('local/temp/{path}', function (Illuminate\Http\Request $request, string $path) {
    if (! $request->hasValidSignature()) {
        abort(401);
    }

    if (! str_starts_with($path, 'public/')) {
        $path = 'public/'.$path;
    }
    //    dd($path);

    return response()->file(Storage::path($path), [
        'Content-Type' => Storage::mimeType($path),
        'Content-Disposition' => 'inline; filename="document.pdf"',
        'Cache-Control' => 'no-store, no-cache, must-revalidate, post-check=0, pre-check=0',
        'Pragma' => 'no-cache',
        'X-Frame-Options' => 'SAMEORIGIN',
        // Disable right-click context menu
        'X-Content-Type-Options' => 'nosniff',
    ]);

})->where('path', '(.*)')->name('local.temp')->middleware(['signed', /* 'single-use', */ 'throttle:60,1']);

Route::get('/import/{book?}', function ($book = null) {

    if ($book == null) {
        for ($i = 1; $i <= 16; $i++) {
            $book = $i;
            $directories = Storage::directories('public/arabic4all/book_'.$book);
            foreach ($directories as $directory) {
                foreach (Storage::directories($directory) as $subDirectory) {
                    // (1001) get id for unit   1 for book and 001 for unit, it's got from folder name
                    $unit_id = $book.sprintf('%03d',
                        str_replace('public/arabic4all/book_'.$book.'/unit_', '', dirname($subDirectory)));
                    // (1) get id for lesson , Also got from folder name inside unit folder
                    $lesson_id = str_replace($directory.'/lesson_', '', $subDirectory);

                    $array = Storage::files($subDirectory);
                    $json_file = end($array);

                    $lesson_name = 'الدرس '.str_replace([$subDirectory.'/lesson_', '.json'], '', $json_file);

                    //            if (Unit::find($unit_id) == null){
                    //                dd($unit_id, ' check why not found');
                    //            }

                    // create the lesson record
                    //            Unit::find($unit_id)->lessons()->updateOrCreate([
                    //                'id' => $unit_id.$lesson_id,
                    //            ],[
                    //                'title' => $lesson_name,
                    //                'slug' => \Str::slug($lesson_name).'-'.random_int(11111, 99999),
                    //                'description' => 'لا يوجد ملاحظات لـ ' . $lesson_name . ' حتى الآن',
                    //                'visible' => 1,
                    //            ]);

                    // create materials for the lesson from json file
                    $materials = Storage::json(end($array));
                    if ($materials != null) {

                        // make sure the array has the same length
                        if (count($materials['images']) !== count($materials['lesson_texts'])) {
                            $bigger_count = max(count($materials['images']), count($materials['lesson_texts']));
                            $materials['images'] = array_pad($materials['images'], $bigger_count, null);
                            $materials['lesson_texts'] = array_pad($materials['lesson_texts'], $bigger_count, null);
                        }

                        // combine text with images
                        $images_with_text = array_combine($materials['images'], $materials['lesson_texts']);
                        $loopCount = 1;

                        $book_pdf = \Modules\MediaLibrary\Models\Pdf::updateOrCreate([
                            'name' => 'Book_'.$book,
                            'path' => 'not found',
                        ]);

                        foreach ($images_with_text as $key => $item) {

                            //                    Lesson::find($unit_id.$lesson_id)->materials()->updateOrCreate([
                            //                        'main' => 1,
                            //                        'type' => 'image',
                            //                        'file' => $subDirectory.'/images/'.$key,
                            //                        'title' => 'صفحة الكتاب ' . $loopCount++,
                            //                        'text' => $item[0] ?? '',
                            //                    ]);

                            // save the pdf_file as a child inside $book_pdf
                            $pdf_file_name = str_replace('.jpg', '', $key).'.pdf';
                            //                  Page number in the file name like: Book 8_Page_8.pdf
                            preg_match('/(\d+)\.pdf$/', $pdf_file_name, $matches);

                            if (empty($matches)) {
                                dd('Error in file name: '.$subDirectory.'/'.$pdf_file_name);
                            }

                            $pdf_page = $book_pdf->pdf_pages()->updateOrCreate([
                                'page_number' => $matches[1],
                                'page_path' => $subDirectory.'/images/'.$pdf_file_name,
                            ]);

                            Lesson::find($unit_id.$lesson_id)->materials()->updateOrCreate([
                                'main' => 1,
                                'type' => 'pdf',
                                'title' => 'صفحة الكتاب '.$loopCount++,
                                'pdf_id' => $book_pdf->id,
                                'pdf_pages' => [$pdf_page->id],
                                'text' => $item[0] ?? '',
                            ]);

                            //                        // convert image to pdf
                            //                        $pdf = new Imagick();
                            //                        $img_path = storage_path('app/'.$subDirectory.'/images/'.$key);
                            //                        // Read the image
                            //                        try {
                            //                            $pdf->readImage($img_path);
                            //                        } catch (ImagickException $e) {
                            //                            dd("Error reading image: " . $e->getMessage(), $img_path);
                            //                        }
                            //
                            //                        $pdf->setImageFormat('pdf');
                            //                        $pdf->writeImages(Storage::path($subDirectory.'/images/'.$key . '.pdf'), true);
                            //                        // Clear the Imagick object
                            //                        $pdf->clear();
                            //                        $pdf->destroy();

                        }

                        foreach ($materials['audio_files'] as $key => $material) {
                            $audio = Audio::updateOrCreate([
                                'name' => $key,
                                'path' => $subDirectory.'/audio_files/'.$material,
                            ]);
                            Lesson::find($unit_id.$lesson_id)->materials()->updateOrCreate([
                                'main' => 0,
                                'type' => 'audio',
                                'title' => 'أصوات الدرس',
                                'audio_id' => $audio->id,
                            ]);
                        }

                        // if there is a video file
                        if (isset($materials['videos'])) {
                            foreach ($materials['videos'] as $key => $material) {

                                //                        $video = Video::updateOrCreate([
                                //                            'name' => $key,
                                //                            'path' => $subDirectory.'/videos/'.$key,
                                //                        ]);
                                Lesson::find($unit_id.$lesson_id)->materials()->updateOrCreate([
                                    'main' => 1,
                                    'type' => 'embed',
                                    'title' => 'المرئيات',
                                    //                            'file' => $video->path,
                                    'embed_code' => '
                                <div style="position: relative; padding-top: 56.25%;">
                                  <iframe
                                    src="'.$material.'?poster=https%3A%2F%2Fcustomer-dbnpnytgltiq5d1y.cloudflarestream.com%2F3d184d91ae27402e2cd6cefa5ded1d80%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                                    loading="lazy"
                                    style="border: none; position: absolute; top: 0; left: 0; height: 100%; width: 100%;"
                                    allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
                                    allowfullscreen="true"
                                  ></iframe>
                                </div>
                            ',
                                ]);
                            }
                        }

                        //                    foreach ($materials['images'] as $key=>$material){
                        //                        Lesson::find($lesson_id)->materials()->updateOrCreate([
                        //                            'main' => 1,
                        //                            'type' => 'image',
                        //                            'title' => 'صفحة الكتاب ' . $key+1,
                        //                            'file' => $subDirectory.'/images/'.$material,
                        //                        ]);
                        //                    }
                        //                    foreach ($materials['lesson_texts'] as $key=>$material){
                        //
                        //                        Lesson::find($lesson_id)->materials()->updateOrCreate([
                        //                            'main' => 1,
                        //                            'type' => 'image',
                        //                            'title' => 'صفحة الكتاب ' . (int) str_replace(['tab', '-1'],'',$key)+1,
                        //                            'text' => $material[0],
                        //                        ]);
                        //                    }

                    }

                }
            }
        }
    }

});



