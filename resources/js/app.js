import './bootstrap';
import 'preline';
import {MarkerArea} from "markerjs2";
import 'pdfjs-viewer-element'

document.addEventListener('livewire:navigated', () => {
    window.HSStaticMethods.autoInit();
})


document.addEventListener('DOMContentLoaded', () => {


    const allowedPaths = [
        '/courses',
        // Add more paths as needed
    ];
    //
    // // Check if current path matches any of the allowed paths
    const currentPath = window.location.pathname;
    const shouldInitialize = allowedPaths.some(path => currentPath.startsWith(path));
    //
    if (shouldInitialize) {
        const tabInstance = window.HSTabs ? window.HSTabs.getInstance('#tabbbbb') : null;
        if (tabInstance) {
            // Register a change event handler
            tabInstance.on('change', ({el, prev, current}) => {
                // Reinitialize components when tab changes
                if (window.HSStaticMethods && typeof window.HSStaticMethods.autoInit === 'function') {
                    window.HSStaticMethods.autoInit();
                }
            });
        }
    }
})



// window.showMarkerArea = function showMarkerArea() {
//     // let target = document.querySelector(".myImage");
//     // console.log(target)
//
//     var images = document.querySelectorAll('.myImage');
//     for (var i = 0, total = images.length; i < total; i++) {
//         let target = images[i];
//         const markerArea = new MarkerArea(target);
//
//         let fileName = target.attributes['data-image'].value.split(/[\\\/]/).pop()
//
//         markerArea.addEventListener("render", (event) => {
//             target.src = event.dataUrl;
//             // forget last value
//             localStorage.removeItem("mstate"+fileName)
//             localStorage.setItem("mstate"+fileName, JSON.stringify(event.state))
//         });
//
//         markerArea.show();
//         if (localStorage.getItem("mstate"+fileName)) {
//             // console.log(JSON.stringify(localStorage.getItem("mstate"+fileName)))
//             markerArea.restoreState(JSON.parse(localStorage.getItem("mstate"+fileName)));
//         }
//
//     }
//
//
// }



document.addEventListener('livewire:init', () => {
    Livewire.on('scrollToDiv', (divId) => {
        const div = document.getElementById(divId);
        if (div) {
            // skip if not on mobile view
            if (window.innerWidth < 768) {
                div.scrollIntoView({ behavior: 'smooth' });
            }
        }
    });


    Livewire.on('lesson-changed-end', (event) => {
        window.HSStaticMethods.autoInit();
    });


});
