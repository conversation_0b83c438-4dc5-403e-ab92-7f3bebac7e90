<?php

use function Livewire\Volt\{layout, state};

layout('components.layouts.page');

state(['page' => fn (\Modules\Page\Models\Page $page) => $page]);

?>
{{-- Blog Article --}}
    <div class="max-w-3xl px-4 pt-6 lg:pt-10 pb-12 sm:px-6 lg:px-8 mx-auto">
        <div class="max-w-2xl">
            @foreach ($page->content as $block)
                @switch($block['type'])
                    @case('markdown')
                        <div class="space-y-5 md:space-y-8 prose prose-slate">
                            {{ \Filament\Support\Markdown::block($block['data']['content']) }}
                        </div>
                        @break
                    @case('figure')
                        <figure>
                            <img
                                style="width: 400px;"
                                src="{{ $block['data']['image'] }}"
                                alt="{{ $block['data']['alt'] }}"
                            >
                            @if ($block['data']['caption'])
                                <figcaption class="text-sm text-gray-500">
                                    {{ $block['data']['caption'] }}
                                </figcaption>
                            @endif
                        </figure>

                        @break
                    @default
                        @dump($block)
                @endswitch
            @endforeach
        </div>
    </div>
{{-- End Blog Article --}}
