<?php

use Livewire\Volt\Component;
use Modules\Payment\Services\PaymentService;

new class extends Component {
    public $data;
    public $token;

    public function mount()
    {
        $this->data = [
            'user' => auth()->user(),
            'amount' => 100,
            'currency' => 'USD',
            'description' => 'Test payment',
            'customer_name' => 'Someone',
            'customer_email' => '<EMAIL>',
        ];
    }

    public function checkoutPay()
    {
        $data = $this->data;
        $token = $this->token;
        $data['ip'] = request()->ip();
        $item = \Modules\Course\Models\Course::first();
        $service = app(PaymentService::class);
        $payment = $service->createPayment($data, $item, 'twocheckout');

        return $service->processPayment($payment, ['token' => $token]);
    }
};

?>
<x-layouts.app>
    @volt
    <div class="">
        <!-- Success Message -->
        <div class="py-20 max-w-lg mx-auto">
            <!-- Icon -->
            <div class="mb-5 sm:mb-7 text-center">
                <span
                    class="shrink-0 size-14 md:size-16 mx-auto flex justify-center items-center border-2 border-purple-500 text-purple-500 rounded-full">
                    <svg class="shrink-0 size-8" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                         stroke-linecap="round" stroke-linejoin="round">
                        <rect width="20" height="14" x="2" y="5" rx="2"/>
                        <line x1="2" x2="22" y1="10" y2="10"/>
                    </svg>
                </span>
            </div>
            <!-- End Icon -->

            {{ $token }}

            <!-- Heading -->
            <div class="mb-5 sm:mb-8 text-center">
                <h1 class="mb-1 md:mb-3 font-semibold text-xl md:text-2xl text-gray-800 dark:text-neutral-200">
                    Your transfer is on the way
                </h1>
            </div>
            <!-- End Heading -->

            <form id="payment-form">
                @csrf
                <div id="card-element">
                    <!-- A TCO IFRAME will be inserted here. -->
                </div>
                <button
                    class="py-2 w-full inline-flex justify-center items-center gap-x-2 text-sm font-semibold rounded-lg border border-transparent bg-green-600 text-white hover:bg-green-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:ring-2 focus:ring-green-500"
                    type="submit">@lang('Place order')</button>
            </form>
        </div>
        <!-- End Success Message -->

        <script type="text/javascript" src="https://2pay-js.2checkout.com/v1/2pay.js"></script>
        <script>
            window.addEventListener('load', function () {
                // Initialize the JS Payments SDK client.
                let jsPaymentClient = new TwoPayClient('203572367');

                // Create the component that will hold the card fields.
                let component = jsPaymentClient.components.create('card');

                // Mount the card fields component in the desired HTML tag. This is where the iframe will be located.
                component.mount('#card-element');

                // Handle form submission.
                document.getElementById('payment-form').addEventListener('submit', (event) => {
                    event.preventDefault();

                    // Extract the Name field value
                    const billingDetails = {
                        name: '{{ $data['user']['name'] }}'
                    };

                    jsPaymentClient.tokens.generate(component, billingDetails).then((response) => {
                        // Update Livewire property with the token
                        @this.set('token', response.token);

                        // init function checkoutPay()
                        @this.call('checkoutPay').then((response) => {
                            // Handle the response from the server
                            console.log('Payment response:', response);
                        }).catch((error) => {
                            console.error('Error processing payment:', error);
                        });


                    }).catch((error) => {
                        console.error('Error generating token:', error);
                    });
                });
            });
        </script>
    </div>
    @endvolt
</x-layouts.app>
