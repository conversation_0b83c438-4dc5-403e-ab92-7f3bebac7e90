<?php

use function Livewire\Volt\{state, mount};

state([
    'paymentMethod' => 'credit_card',
    'cardNumber' => '',
    'expiryDate' => '',
    'cvv' => '',
    'cardholderName' => '',
    'loading' => false,
]);

$processPayment = function() {
    $this->loading = true;

    // Validate card details
    $this->validate([
        'cardNumber' => 'required|string|min:16|max:19',
        'expiryDate' => 'required|string',
        'cvv' => 'required|string|min:3|max:4',
        'cardholderName' => 'required|string',
    ]);

    // Process payment
    try {
        // Payment processing logic would go here
        // This is just a simulation with a delay
        sleep(1);

        // Dispatch success event
        $this->dispatch('payment-success', [
            'message' => 'Payment processed successfully!'
        ]);
    } catch (\Exception $e) {
        $this->dispatch('payment-error', [
            'message' => 'Payment failed: ' . $e->getMessage()
        ]);
    }

    $this->loading = false;
};

$redirectToPaypal = function() {
    // In a real implementation, you would generate a PayPal checkout URL
    // and redirect the user to it
    $this->dispatch('redirect-to-external', [
        'url' => 'https://www.paypal.com/checkout'
    ]);
};

$confirmBankTransfer = function() {
    // Process bank transfer confirmation
    $this->dispatch('bank-transfer-confirmed', [
        'message' => 'Bank transfer confirmation received!'
    ]);
};
?>


<x-layouts.app>
    <div class="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
        {{-- Step Indicator --}}
        <div class="flex justify-center mb-8">
            <ol class="flex items-center whitespace-nowrap">
                <li class="inline-flex items-center">
                    <div class="flex items-center justify-center w-8 h-8 rounded-full border-2 border-blue-600 text-blue-600 font-semibold">1</div>
                    <span class="ms-3 text-sm font-medium text-gray-800">Shipping</span>
                    <svg class="flex-shrink-0 mx-3 overflow-visible h-2.5 w-2.5 text-gray-400" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5 1L10.6869 7.16086C10.8637 7.35239 10.8637 7.64761 10.6869 7.83914L5 14" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path>
                    </svg>
                </li>
                <li class="inline-flex items-center">
                    <div class="flex items-center justify-center w-8 h-8 rounded-full border-2 border-blue-600 text-blue-600 font-semibold">2</div>
                    <span class="ms-3 text-sm font-medium text-gray-800">Payment</span>
                </li>
            </ol>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
            {{-- Checkout Form --}}
            <div class="p-4 sm:p-7 flex flex-col h-full bg-white border border-gray-200 rounded-xl">
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800">Shipping information</h2>
                </div>

                <form>
                    <div class="grid gap-4 lg:gap-6">
                        {{-- Name --}}
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 lg:gap-6">
                            <div>
                                <label class="block text-sm text-gray-700 font-medium">First name</label>
                                <input type="text" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm text-gray-700 font-medium">Last name</label>
                                <input type="text" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>

                        {{-- Email --}}
                        <div>
                            <label class="block text-sm text-gray-700 font-medium">Email</label>
                            <input type="email" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>

                        {{-- Address --}}
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 lg:gap-6">
                            <div>
                                <label class="block text-sm text-gray-700 font-medium">Address</label>
                                <input type="text" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm text-gray-700 font-medium">City</label>
                                <input type="text" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            {{-- Order Summary & Payment Selection --}}
            <div x-data="{ step: 'summary' }" class="p-4 sm:p-7 flex flex-col h-full bg-white border border-gray-200 rounded-xl">
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800">Order summary</h2>
                </div>

                {{-- Total --}}
                <div class="mb-6">
                    <div class="border-t border-gray-200 py-4">
                        <div class="flex items-center justify-between text-sm">
                            <p class="font-medium text-gray-800">Subtotal</p>
                            <p class="text-gray-600">$99.00</p>
                        </div>
                        <div class="flex items-center justify-between text-sm mt-2">
                            <p class="font-medium text-gray-800">Shipping</p>
                            <p class="text-gray-600">$8.00</p>
                        </div>
                        <div class="flex items-center justify-between font-bold text-base mt-4">
                            <p>Total</p>
                            <p>$107.00</p>
                        </div>
                    </div>
                </div>

                {{-- Payment Method Selection --}}
                @volt()
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Payment method</h3>
                    {{ $paymentMethod }}

                    <div x-volt="paymentSelection">
                        <div class="space-y-3">
                            <div class="flex items-center gap-x-3">
                                <input
                                    type="radio"
                                    id="credit_card"
                                    name="payment_method"
                                    value="credit_card"
                                    wire:model="paymentMethod"
                                    class="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                                >
                                <label for="credit_card" class="text-sm text-gray-700">Credit Card</label>
                            </div>
                            <div class="flex items-center gap-x-3">
                                <input
                                    type="radio"
                                    id="paypal"
                                    name="payment_method"
                                    value="paypal"
                                    wire:model="paymentMethod"
                                    class="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                                >
                                <label for="paypal" class="text-sm text-gray-700">PayPal</label>
                            </div>
                            <div class="flex items-center gap-x-3">
                                <input
                                    type="radio"
                                    id="bank_transfer"
                                    name="payment_method"
                                    value="bank_transfer"
                                    wire:model="paymentMethod"
                                    class="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                                >
                                <label for="bank_transfer" class="text-sm text-gray-700">Bank Transfer</label>
                            </div>
                        </div>
                        {{-- Dynamic Payment Form --}}
                        <div class="mt-6">
                            @if($paymentMethod === 'credit_card')
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm text-gray-700 font-medium">Card Number</label>
                                        <input type="text" wire:model="cardNumber" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500">
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm text-gray-700 font-medium">Expiry Date</label>
                                            <input type="text" wire:model="expiryDate" placeholder="MM/YY" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm text-gray-700 font-medium">CVV</label>
                                            <input type="text" wire:model="cvv" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500">
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm text-gray-700 font-medium">Cardholder Name</label>
                                        <input type="text" wire:model="cardholderName" class="py-3 px-4 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500">
                                    </div>
                                    <button type="button" wire:click="processPayment" class="py-3 px-4 w-full inline-flex justify-center items-center text-sm font-semibold rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                                        Pay $107.00
                                    </button>
                                </div>
                            @elseif($paymentMethod === 'paypal')
                                <div class="text-center">
                                    <p class="mb-4 text-sm text-gray-600">You will be redirected to PayPal to complete your payment.</p>
                                    <button type="button" wire:click="redirectToPaypal" class="py-3 px-4 w-full inline-flex justify-center items-center text-sm font-semibold rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                                        Proceed to PayPal
                                    </button>
                                </div>
                            @elseif($paymentMethod === 'bank_transfer')
                                <div class="space-y-4">
                                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                        <h4 class="font-medium text-gray-800">Bank Account Details</h4>
                                        <ul class="mt-2 space-y-2 text-sm text-gray-600">
                                            <li><span class="font-medium">Bank Name:</span> Example Bank</li>
                                            <li><span class="font-medium">Account Name:</span> Arabic for All</li>
                                            <li><span class="font-medium">Account Number:</span> *********</li>
                                            <li><span class="font-medium">IBAN:</span> XX00 0000 0000 0000 0000 0000</li>
                                        </ul>
                                    </div>
                                    <button type="button" wire:click="confirmBankTransfer" class="py-3 px-4 w-full inline-flex justify-center items-center text-sm font-semibold rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                                        I've made the transfer
                                    </button>
                                </div>
                            @else
                                <p class="text-sm text-gray-600">Please select a payment method to continue.</p>
                            @endif
                        </div>
                    </div>
                </div>
                @endvolt
            </div>
        </div>
    </div>
</x-layouts.app>
