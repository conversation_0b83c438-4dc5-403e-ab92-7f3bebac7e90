<!DOCTYPE html>
<html lang="en" dir="rtl">
<head>
    {{-- Required Meta Tags Always Come First --}}
    <meta charset="utf-8">
    <meta name="robots" content="max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <link rel="canonical" href="https://arabicforall.com/">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="From bold visuals to interactive elements, this template is fully customizable to suit your unique needs and preferences.">

    <meta name="twitter:site" content="@arabicforall">
    <meta name="twitter:creator" content="@arabicforall">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Creative Agency Demo Template Tailwind CSS | Arabic for all UI, crafted with Tailwind CSS">
    <meta name="twitter:description" content="From bold visuals to interactive elements, this template is fully customizable to suit your unique needs and preferences.">
    <meta name="twitter:image" content="https://arabicforall.com/assets/img/og-image.png">

    <meta property="og:url" content="https://arabicforall.com/">
    <meta property="og:locale" content="en_US">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Arabic for all">
    <meta property="og:title" content="Creative Agency Demo Template Tailwind CSS | Arabic for all UI, crafted with Tailwind CSS">
    <meta property="og:description" content="From bold visuals to interactive elements, this template is fully customizable to suit your unique needs and preferences.">
    <meta property="og:image" content="https://arabicforall.com/assets/img/og-image.png">

    {{-- Title --}}
    <title>{{ $title ?? config('app.name') }}</title>

    {{-- Favicon --}}
    <link rel="shortcut icon" type="image/x-icon" href="{{ asset('assets/images/favicon.ico') }}" />

    {{-- Fonts --}}
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=ibm-plex-sans-arabic:400,500,600&display=swap" rel="stylesheet" />

    {{-- Theme Check and Update --}}

    <script>
        const html = document.querySelector('html');
        const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
        const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

        if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
        else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
        else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
        else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
    </script>

    @vite(['resources/css/app.css', 'resources/js/app.js'])

</head>

<body class="dark:bg-neutral-900">


<div class="max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto">
    <div class="grid lg:grid-cols-3 gap-y-8 lg:gap-y-0 lg:gap-x-6">
        {{-- Content --}}
        <div class="lg:col-span-2 flex flex-col justify-around">
            <div class="py-8 lg:pe-8 ">
                <div class="space-y-5 lg:space-y-8">
                    <a  class="inline-flex items-center gap-x-1.5 text-sm text-gray-600 decoration-2 hover:underline dark:text-blue-500"
                        href="{{ url('/') }}">
                        <svg class="flex-shrink-0 size-4 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
                        @lang('Back to home')
                    </a>
                    <h2 class="text-3xl font-bold lg:text-5xl dark:text-white">@lang('Checkout')</h2>

                    <div class="">
                        <button type="button"  product-code="123qwe" product-quantity="1" id="checkout" class="avangate_button py-2 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm font-semibold rounded-lg border border-transparent bg-green-600 text-white hover:bg-green-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:ring-2 focus:ring-green-500">
                            @lang('Place order')
                        </button>
{{--                        <a href="#buy" class="avangate_button" product-code="123qwe" product-quantity="1">Buy now!</a>--}}
                </div>
            </div>
            <x-ui.footer class="" />
        </div>
        {{-- End Content --}}
    </div>
</div>
{{--<script>--}}
{{--    (function(document, src, libName, config) {--}}
{{--        var script = document.createElement('script');--}}
{{--        script.src = src;--}}
{{--        script.async = true;--}}
{{--        var firstScriptElement = document.getElementsByTagName('script')[0];--}}
{{--        script.onload = function() {--}}
{{--            for (var namespace in config) {--}}
{{--                if (config.hasOwnProperty(namespace)) {--}}
{{--                    window[libName].setup.setConfig(namespace, config[namespace]);--}}
{{--                }--}}
{{--            }--}}
{{--            window[libName].register();--}}
{{--        };--}}

{{--        firstScriptElement.parentNode.insertBefore(script, firstScriptElement);--}}
{{--    })(document, 'https://secure.2checkout.com/checkout/client/twoCoInlineCart.js', 'TwoCoInlineCart', {--}}
{{--        "app": {--}}
{{--            "merchant": "{{config('payment.2co-inline.live.seller_id') }}"--}}
{{--        },--}}
{{--        "cart": {--}}
{{--            "host": "https:\/\/secure.2checkout.com"--}}
{{--        }--}}
{{--    });--}}
{{--</script>--}}
{{--<script type="text/javascript">--}}
{{--    window.document.getElementById('checkout').addEventListener('click', function() {--}}


{{--        TwoCoInlineCart.setup.setMerchant("{{config('payment.2co-inline.live.seller_id') }}");--}}
{{--        TwoCoInlineCart.setup.setMode('DYNAMIC'); // product type--}}
{{--        TwoCoInlineCart.register();--}}

{{--        TwoCoInlineCart.products.add({--}}
{{--            name: "test",--}}
{{--            quantity: 1,--}}
{{--            price: "2",--}}
{{--        });--}}

{{--        TwoCoInlineCart.cart.setCurrency("USD");--}}
{{--        TwoCoInlineCart.cart.setTest(true);--}}
{{--        TwoCoInlineCart.cart.checkout(); // start checkout process--}}
{{--    });--}}

{{--</script>--}}

{{--@dd()--}}
<script>
        (function (document, src, libName, config) {
        var script             = document.createElement('script');
        script.src             = src;
        script.async           = true;
        var firstScriptElement = document.getElementsByTagName('script')[0];
        script.onload          = function () {
        for (var namespace in config) {
        if (config.hasOwnProperty(namespace)) {
        window[libName].setup.setConfig(namespace, config[namespace]);
    }
    }
        window[libName].register();
    };

        firstScriptElement.parentNode.insertBefore(script, firstScriptElement);
    })(document, 'https://secure.2checkout.com/checkout/client/twoCoInlineCart.js', 'TwoCoInlineCart',{"app":{"merchant":"255341000951","iframeLoad":"checkout"},"cart":{"host":"https:\/\/secure.2checkout.com","customization":"inline"}});
{{--    })(document, 'https://secure.2checkout.com/checkout/client/twoCoInlineCart.js', 'TwoCoInlineCart',@json(session()->get('checkoutConfig')));--}}


{{--    --}}{{--document.addEventListener("DOMContentLoaded", function(event) {--}}
{{--    --}}{{--    window.document.getElementById('2coinline').addEventListener('click', function() {--}}
{{--    --}}{{--        TwoCoInlineCart.products.add({--}}
{{--    --}}{{--            code: "123qwe"--}}
{{--    --}}{{--        });--}}
{{--    --}}{{--        TwoCoInlineCart.cart.setTest(true);--}}
{{--    --}}{{--        TwoCoInlineCart.cart.checkout();--}}
{{--    --}}{{--    });--}}

{{--    --}}{{--});--}}

{{--    function checkout(price){--}}
{{--        TwoCoInlineCart.setup.setMerchant("{{ config('payment.2co-inline.live.seller_id') }}"); // your merchant code--}}
{{--        TwoCoInlineCart.setup.setMode("DYNAMIC"); // product type--}}
{{--        TwoCoInlineCart.register();--}}

{{--        TwoCoInlineCart.cart.setCurrency("USD"); // order currency--}}
{{--        TwoCoInlineCart.cart.setReset(true); // erase previous cart sessions--}}

{{--        TwoCoInlineCart.products.add({--}}
{{--            name        : "Total Price", //cart id--}}

{{--            price: price //cart total price--}}
{{--        });--}}
{{--        TwoCoInlineCart.cart.checkout(); //start checkout process--}}
{{--    }--}}
</script>





</body>

</html>
