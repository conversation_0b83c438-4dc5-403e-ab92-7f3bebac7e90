<?php
use function <PERSON><PERSON>\Folio\name;

name('product.show')


?>


<x-layouts.page title="Check out">

{{-- Blog Article --}}
    <div class="max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto">
        <div class="grid lg:grid-cols-3 gap-y-8 lg:gap-y-0 lg:gap-x-6">
            {{-- Content --}}
            <div class="lg:col-span-2 flex flex-col ">
                <div class="py-8 lg:pe-8 ">
                    <div class="space-y-5 lg:space-y-8">
                        <a  class="inline-flex items-center gap-x-1.5 text-sm text-gray-600 decoration-2 hover:underline dark:text-blue-500"
                            href="{{ url('/') }}">
                            <svg class="flex-shrink-0 size-4 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
                            @lang('Back to home')
                        </a>
                        <h2 class="text-3xl font-bold lg:text-5xl dark:text-white">@lang('Checkout')</h2>

                        <div class="flex items-center gap-x-5">
                            <a class="inline-flex items-center gap-1.5 py-1 px-3 sm:py-2 sm:px-4 rounded-full text-xs sm:text-sm bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-neutral-800 dark:hover:bg-neutral-800 dark:text-neutral-200" href="#">
                                Company News
                            </a>
                            <p class="text-xs sm:text-sm text-gray-800 dark:text-neutral-200">January 18, 2023</p>
                        </div>

                    </div>
                </div>

            </div>
            {{-- End Content --}}

            {{-- Sidebar --}}
            <div class="min-h-screen lg:col-span-1 lg:w-full lg:h-full ltr:lg:bg-gradient-to-r rtl:lg:bg-gradient-to-l lg:from-gray-50 lg:via-transparent lg:to-transparent dark:from-neutral-800">
                <div class="sticky top-0 start-0 py-8 lg:ps-8">

                    {{-- Order Summary Card --}}
                    <div class="flex flex-col ">
                        {{-- Header --}}
                        <div class="py-3 px-5 border-b border-stone-200 dark:border-neutral-700">
                            <h2 class="inline-block font-semibold text-stone-800 dark:text-neutral-200">
                                Items (2)
                            </h2>
                        </div>
                        {{-- End Header --}}

                        {{-- Body --}}
                        <div class="pt-4 pb-5 px-5 flex flex-col justify-between h-full">
                            {{-- Item --}}
                            <div class="pt-3 mt-3 border-t border-stone-200 first:pt-0 first:mt-0 first:border-t-0 dark:border-neutral-700">
                                <div class="flex items
