<?php
use function Livewire\Volt\{state, mount};

state(['course' => null]);
mount(function (\Modules\Course\Models\Course $course) {
    $this->course = $course;

    // Redirect directly to the module page if there's only one module
    if ($course->modules->count() == 1) {
        $module = $course->modules->first();
        return redirect()->route('module.show', ['course' => $course, 'module' => $module]);
    }
});

?>


<div>
    <x-slot name="header">
        <h2 class="text-xl font-bold md:text-2xl md:leading-tight dark:text-white">{{ $course->title }}</h2>
        <p class="mt-1 text-gray-600 dark:text-neutral-400">{{ __('Choose the Module') }}</p>
    </x-slot>

    <div class="container max-w-7xl px-4 lg:px-6 py-8 lg:py-12 mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 lg:gap-12">
                <div class="col-span-3 ">
                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-3">
                            @foreach($course->modules as $module)

                                {{-- Card --}}
                                <a class="md:max-w-1/5 md:max-w-1/5 group p-4 inline-flex flex-col justify-center items-center text-center bg-white border border-stone-200 rounded-xl shadow-sm hover:shadow-md focus:outline-none focus:shadow-md transition dark:bg-neutral-800 dark:border-neutral-700 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                   href="{{ route('module.show', ['course' => $course, 'module' => $module]) }}">
{{--                                    <x-heroicon-o-book-open class="size-9 text-stone-500 dark:text-neutral-400"/>--}}
                                    <img src="{{ $module->getMediaUrlWithFallback(conversion: 'cover') }}"
                                         class="w-80" alt="">
                                    <div class="mt-3">
                                        <h3 class="text-sm font-semibold text-stone-800 group-hover:text-green-600 group-focus:text-green-600 dark:text-neutral-200 dark:group-hover:text-green-500 dark:group-focus:text-green-500">
                                            {{ $module->title }}
                                        </h3>
                                        <p class="text-[13px] text-stone-500 dark:text-neutral-500">
                                            {{ $module->description }}
                                        </p>
                                    </div>
                                </a>
                                {{-- End Card --}}

                            @endforeach
                        </div>
                </div>
            </div>
        </div>



</div>

