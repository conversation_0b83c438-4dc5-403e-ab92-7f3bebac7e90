<?php
use function Livewire\Volt\{state, mount};

state([
    'course' => fn(\Modules\Course\Models\Course $course) => $course,
    'module' => fn(\Modules\Course\Models\Module $module) => $module,
]);
//mount(fn (\Modules\Course\Models\Course $course) => $this->course = $course);

//mount(function (\Modules\Course\Models\Course $course, \Modules\Course\Models\Module $module) {
//    $this->course = $course;
//    $this->module = $module;
//});



mount(function (\Modules\Course\Models\Course $course, \Modules\Course\Models\Module $module) {
    $this->course = $course;
    $this->module = $module;

    // check if user is purchased the course and course is not 0 price
    if ($course->price > 0 && !$course->isPurchasedBy(auth()->user())) {
        return redirect()->route('courses.checkout', ['course' => $course]);
    }

});

?>


<div>

    @if($module->units->count() > 0)
        @livewire('material', ['module' => $module])
    @else
        <div class="p-5  flex flex-col justify-center items-center text-center">

            <div class="max-w-sm mx-auto">
                <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                    {{ __('No Content') }}
                </p>
                <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                    We'll notify you about important updates and any time you're
                    mentioned on Arabic for All.
                </p>
            </div>

            <a class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
               href="#">
                Learn more
            </a>
        </div>

    @endif

    <x-slot name="header">
        <ol class="flex items-center whitespace-nowrap">
            <li class="inline-flex items-center">
                <a wire:navigate class="flex items-center text-sm text-gray-500 hover:text-blue-600 focus:outline-none focus:text-blue-600 dark:text-neutral-500 dark:hover:text-blue-500 dark:focus:text-blue-500"
                   href="{{ route('modules.index', ['course' => $course]) }}">
                    {{ $course->title }}
                    <svg class="shrink-0 mx-2 size-4 text-gray-400 dark:text-neutral-600"
                         xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                         fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                         stroke-linejoin="round">
                        <path d="m15 18-6-6 6-6"></path>
                    </svg>
                </a>
            </li>

            <li class="inline-flex items-center text-sm font-semibold text-gray-800 truncate dark:text-neutral-200"
                aria-current="page">
                {{ $module->title }}
            </li>
        </ol>

    </x-slot>

    <x-slot name="buttons">
        {{-- Page Header --}}
        <div class="grid sm:flex sm:justify-between sm:items-center gap-3 sm:gap-5">
            {{-- Next Prev Buttons --}}
            <div class="inline-flex justify-between items-center gap-x-3">
                <div class="flex justify-end rtl:flex-row-reverse items-center gap-x-2">
                    {{-- Button --}}
                    <a href="" type="button"
                       class="py-2 px-2.5 inline-flex items-center gap-x-1.5 text-sm font-medium rounded-lg border border-stone-200 bg-white text-stone-800 shadow-sm hover:bg-stone-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                        @lang('Next')
                        <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                             stroke-linecap="round" stroke-linejoin="round">
                            <path d="m15 18-6-6 6-6"/>
                        </svg>

                    </a>
                    {{-- End Button --}}

                    {{-- Button --}}
                    <button type="button"
                            class="py-2 px-2.5 inline-flex items-center gap-x-1.5 text-sm font-medium rounded-lg border border-stone-200 bg-white text-stone-800 shadow-sm hover:bg-stone-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                        <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                             stroke-linecap="round" stroke-linejoin="round">
                            <path d="m9 18 6-6-6-6"/>
                        </svg>

                         @lang('Prev')

                    </button>
                    {{-- End Button --}}

                </div>
            </div>
        </div>
        {{-- End Page Header --}}

    </x-slot>

    {{-- Floating buttons container --}}
    <div class="fixed bottom-0 left-0 p-4">
        {{-- Decrease font size button --}}
        <button id="decrease-font-size" class="bg-gray-200 hover:bg-gray-300 text-gray-600 py-2 px-4 rounded">
            -A
        </button>
        {{-- Increase font size button --}}
        <button id="increase-font-size" class="bg-gray-200 hover:bg-gray-300 text-gray-600 py-2 px-4 rounded">
            +A
        </button>
    </div>

    @push('scripts')
        <script>
            const decreaseFontSizeButton = document.getElementById('decrease-font-size');
            const increaseFontSizeButton = document.getElementById('increase-font-size');
            let fontSize = 16;

            decreaseFontSizeButton.addEventListener('click', () => {
                fontSize -= 1;
                document.body.style.fontSize = `${fontSize}px`;
                //     all p and a tags
                document.querySelectorAll('p, a').forEach((element) => {
                    element.style.fontSize = `${fontSize}px`;
                });
            });

            increaseFontSizeButton.addEventListener('click', () => {
                fontSize += 1;
                document.body.style.fontSize = `${fontSize}px`;

                document.querySelectorAll('p, a').forEach((element) => {
                    element.style.fontSize = `${fontSize}px`;
                });
            });

        </script>
    @endpush


</div>
