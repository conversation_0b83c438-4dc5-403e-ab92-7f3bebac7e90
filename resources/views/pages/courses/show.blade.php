<?php
use function Livewire\Volt\{state, mount};

//\Laravel\Folio\name('course.show');

//if (isset($course)) {
//    $course = \Modules\Course\Models\Course::whereSlug($course)->with(['modules', 'modules.units'])->first();
//}

state(['course' => null]);
mount(fn (\Modules\Course\Models\Course $course) => $this->course = $course);

?>


<div>

    {{--    <x-slot name="header">--}}
    {{--        <div class="mt-8 max-w-2xl text-center mx-auto mb-4 lg:mb-8">--}}
    {{--            <h2 class="text-2xl font-bold md:text-4xl md:leading-tight dark:text-white">{{ $course->title }}</h2>--}}
    {{--        </div>--}}
    {{--    </x-slot>--}}


    <x-slot name="header">

        <h2 class="text-xl m-4 inline-block font-semibold text-gray-800 dark:text-neutral-200">
            {{ $course->title }}
        </h2>

        <div class="flex justify-end items-center gap-x-2">
            {{--                    <span--}}
            {{--                        class="py-2 px-2 inline-flex items-center gap-x-1 text-xs font-medium bg-red-100 text-red-800 rounded-full dark:bg-red-500/10 dark:text-red-500">--}}
            {{--                        <svg class="flex-shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24" height="24"--}}
            {{--                             viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"--}}
            {{--                             stroke-linejoin="round">--}}
            {{--                          <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>--}}
            {{--                          <path d="M12 9v4"></path>--}}
            {{--                          <path d="M12 17h.01"></path>--}}
            {{--                        </svg>--}}
            {{--                        {{ $course->level->getLabel()}}--}}
            {{--                    </span>--}}

            <a href="{{ route('courses.checkout',  ['course' => $course]) }}" type="button"
               class="py-2 px-2.5 inline-flex items-center gap-x-2 text-md font-large rounded-lg border border-transparent bg-orange-600 text-white hover:bg-orange-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:ring-2 focus:ring-orange-600 dark:focus:ring-orange-500">
                <span>{{$course->price == 0 ? __('Enroll') : '$'.$course->price . ' - ' .__('Buy Course')}}</span>
            </a>

        </div>

    </x-slot>


    {{-- End Header --}}


    <div class="container max-w-7xl px-4 lg:px-6 py-8 lg:py-12 mx-auto">
        <div class="md:grid md:grid-cols-12 gap-6 md:gap-8 lg:gap-12">
            <div class="col-span-4 mb-9">
                <div class="group relative block rounded-xl overflow-hidden cursor-pointer" aria-haspopup="dialog"
                     aria-expanded="false" aria-controls="hs-custom-backdrop-modal"
                     data-hs-overlay="#hs-custom-backdrop-modal">
                    <div class="aspect-w-12 aspect-h-7 sm:aspect-none rounded-xl overflow-hidden">
                        <img
                            class="group-hover:scale-105 transition-transform duration-500 ease-in-out rounded-xl w-full object-cover"
                            src="{{ $course->getMediaUrlWithFallback(conversion: 'cover') }}" alt="Image Description">
                    </div>
                    <div class="absolute bottom-0 p-2">
                        <div
                            class="font-semibold text-gray-800 rounded-lg bg-white bg-opacity-80 p-3 dark:bg-neutral-800 dark:text-neutral-200">
                            @lang('Intro video')
                        </div>
                    </div>

                </div>
                <div id="hs-custom-backdrop-modal"
                     class="hs-overlay hs-overlay-backdrop-open:bg-blue-950/90 hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none dark:hs-overlay-backdrop-open:bg-blue-950/90"
                     role="dialog" tabindex="-1" aria-labelledby="hs-custom-backdrop-label">
                    <div
                        class="hs-overlay-animation-target hs-overlay-open:scale-100 hs-overlay-open:opacity-100 scale-95 opacity-0 ease-in-out transition-all duration-200 sm:max-w-lg sm:w-full m-3 sm:mx-auto min-h-[calc(100%-3.5rem)] flex items-center">
                        <div
                            class="flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto dark:bg-neutral-800 dark:border-neutral-700 dark:shadow-neutral-700/70">
                            <div class="p-4 overflow-y-auto">

                                <div class="aspect-w-16 aspect-h-9 rounded-xl overflow-hidden">
                                    <iframe width="560" height="315"
                                            src="https://www.youtube.com/embed/{{$course->youtube_intro_video_id}}"
                                            title="YouTube video player" frameborder="0"
                                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                            referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <a href="{{ route('modules.index', ['course' => $course]) }}"
                   type="button"
                   class="my-5 w-full py-3 px-4 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none">
                    @lang('Start Course')
                </a>
                <h2 class="text-2xl text-center my-3 ms-3 font-semibold text-gray-800 dark:text-neutral-200">
                    @lang('Course Content')
                </h2>
                <x-ui.accordion :items="$course->modules"/>

            </div>
            <div class="col-span-8 prose prose-slate dark:text-gray-300">
                {{--                    <h1 class="text-3xl font-semibold text-gray-800 dark:text-neutral-200">--}}
                {{--                        {{ $course->title }}--}}
                {{--                    </h1>--}}

                {{--                    {!! Str::inlineMarkdown($course->description) !!}--}}
                {{--                    <p class="mt-4 text-gray-600 dark:text-neutral-300">--}}
                {{--                        {{ $course->description }}--}}
                {{--                    </p>--}}
                {{ \Filament\Support\Markdown::block($course->description) }}
            </div>
        </div>
    </div>


</div>


