<?php

use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use Livewire\Volt\Component;
use Modules\Course\Models\Material;
use Modules\Course\Models\Module;
use Modules\Course\Models\Note;
use Modules\Course\Models\Unit;
use Modules\Course\Models\Lesson;

new class extends Component {
    public Module $module;
    public Unit|null $unit = null;
    public Lesson|null $lesson = null;
    public $materials;
    public string $teacherNotes = '';
    public int|null $selectedUnit = null;

    #[Url(as: 'lesson', keep: true)]
    public ?string $query_lesson = null;

    public function mount(): void
    {
        $this->initializeUnit();
        $this->loadLesson();
        $this->loadMaterialsAndNotes();
    }

    private function initializeUnit(): void
    {
        // Initialize unit with eager loading
        $this->unit = $this->module->units()
            ->with(['lessons.materials.audio'])
            ->first();
    }

    private function loadLesson(): void
    {
        // Handle query_lesson if provided
        if ($this->query_lesson) {
            $this->loadLessonFromQuery();
        } elseif ($this->unit) {
            // If no specific lesson is set, get the first lesson from the unit
            $this->lesson = $this->unit->lessons()->with('materials')->first();
        }
    }

    private function loadLessonFromQuery(): void
    {
        $queriedLesson = Lesson::find($this->query_lesson);
        if ($queriedLesson && $queriedLesson->unit->module_id === $this->module->id) {
            $this->lesson = $queriedLesson;
            $this->unit = $queriedLesson->unit;
        }
    }

    private function loadMaterialsAndNotes(): void
    {
        // Set query_lesson and load related data if lesson exists
        if ($this->lesson) {
            $this->query_lesson = (string) $this->lesson->id;
            $this->materials = $this->lesson->materials;
            $this->loadTeacherNotes();
        }
    }

    private function loadTeacherNotes(): void
    {
        // Safely fetch teacher notes
        $note = Note::where('user_id', auth()->id())
            ->where('lesson_id', $this->lesson->id)
            ->first();
        $this->teacherNotes = $note ? $note->note : '';
    }

    public function updatedTeacherNotes($teacherNotes): void
    {
        if (!$this->lesson) {
            return;
        }

        Note::updateOrCreate(
            [
                'user_id' => auth()->id(),
                'lesson_id' => $this->lesson->id
            ],
            [
                'note' => $teacherNotes,
                'meta' => ''
            ]
        );
    }

    public function updated($property): void
    {
        if ($property === 'selectedUnit') {
            $this->changeUnit();
        }
    }

    private function changeUnit(): void
    {
        $this->unit = $this->module->units()
            ->with(['lessons.materials.audio'])
            ->find($this->selectedUnit);

        if ($this->unit) {
            $this->lesson = $this->unit->lessons->first();
            $this->materials = $this->lesson ? $this->lesson->materials : collect();
            $this->loadMaterialsAndNotes();
        }
    }

    public function setLesson($lesson): void
    {
        $this->query_lesson = $lesson;
        $this->lesson = Lesson::with('materials')->find($lesson);

        if ($this->lesson) {
            $this->materials = $this->lesson->materials;

            $note = Note::where('user_id', auth()->id())
                ->where('lesson_id', $this->lesson->id)
                ->first();
            $this->teacherNotes = $note ? $note->note : '';

            $this->dispatch('lesson-changed', lesson: $this->lesson->id);
            $this->dispatch('scrollToDiv', 'tedxt');
        }
    }
}

?>

<div class="">
    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" class="">
        <!-- Container -->
        <div class="max-w-[90rem] mx-auto pt-8">
            <!-- Content -->
            <div class="p-2 sm:p-5 md:pt-5 grid grid-cols-3 gap-4">
                <aside id="hs-pro-sidebar" class="w-[290px] hs-overlay [--overlay-backdrop:false] [--auto-close:lg] hidden fixed inset-y-0 start-0 z-[60]  bg-white  lg:block lg:absolute lg:start-auto lg:z-40 lg:translate-x-0 lg:inset-y-auto " tabindex="-1" aria-label="Mini Sidebar">
                    <div class="h-full p-4">
                        <div class="relative h-full">
                            <div class="lg:sticky lg:top-5 space-y-4">
                                @if($unit != null)
                                    <!-- Activity Pricing Card -->
                                    <select wire:model.change="selectedUnit"
                                            class="py-3 px-4 rtl:ps-9 pe-9 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600">
                                        @foreach($module->units as $item)
                                            <option wire:key="{{ $item->id }}"
                                                    value="{{ $item->id }}">{{ $item->title }}</option>
                                        @endforeach
                                    </select>

                                    @if($unit->lessons->count() > 0)
                                        @foreach($unit->lessons as $lessonItem)
                                            @php($lessonItemMaterials = $lessonItem->materials)
                                            <a wire:key="{{ $lessonItem->id }}" wire:click="setLesson({{ $lessonItem->id }});"
                                               class="-mx-1 p-3 group flex rounded-2xl {{ $lessonItem->id == $lesson?->id ? 'bg-gray-100 dark:bg-neutral-800' : '' }}  hover:bg-gray-100 focus:outline-none focus:bg-gray-100 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                                               href="#">
                                                <!-- Icon -->
                                                <div
                                                    class="size-11 inline-flex justify-center items-center rounded-full bg-gray-100 border border-transparent text-gray-800 group-hover:bg-gray-200 group-focus:bg-gray-200 dark:bg-neutral-800 dark:text-neutral-300 dark:group-hover:bg-neutral-700 dark:group-focus:bg-neutral-700">
                                                    @if($lessonItemMaterials->where('type',\Modules\Course\Enum\ContentType::Audio)->count() > 0)
                                                        <x-heroicon-o-speaker-wave class="flex-shrink-0 size-5"/>
                                                    @else
                                                        <x-heroicon-o-document-text class="flex-shrink-0 size-5"/>
                                                    @endif
                                                </div>
                                                <!-- End Icon -->

                                                <div class="grow ms-4">
                                                    <div class="flex justify-between items-center">
                                                        <div>
                                                            <h4 class="font-medium text-gray-800 dark:text-white">
                                                                {{ $lessonItem->title }}
                                                            </h4>
                                                            <ul>
                                                                @if($lessonItemMaterials->where('type',\Modules\Course\Enum\ContentType::Audio)->count() > 0)
                                                                    <x-heroicon-c-speaker-wave
                                                                        class="h-4 w-4 text-blue-400 inline-block"/>
                                                                @endif
                                                                @if($lessonItemMaterials->where('type',\Modules\Course\Enum\ContentType::Video)->count() > 0)
                                                                    <x-heroicon-c-video-camera
                                                                        class="h-4 w-4 text-blue-400 inline-block"/>
                                                                @endif
                                                                @if($lessonItemMaterials->where('type',\Modules\Course\Enum\ContentType::PDF)->count() > 0)
                                                                    <x-heroicon-c-document-text
                                                                        class="h-4 w-4 text-blue-400 inline-block"/>
                                                                @endif
                                                            </ul>
                                                        </div>

                                                        <div class="text-end">
                                                            <p class="font-medium sm:text-lg text-gray-800 dark:text-white">
                                                                ...
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- End Col -->
                                            </a>
                                        @endforeach
                                    @else
                                        <div class="text-center p-6">
                                            <div class="mb-3">
                                                <x-heroicon-o-book-open class="h-10 w-10 mx-auto text-gray-400" />
                                            </div>
                                            <h3 class="text-lg font-medium text-gray-800 dark:text-white">@lang('No Lessons Available')</h3>
                                            <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">@lang('There are no lessons available for this unit.')</p>
                                        </div>
                                    @endif
                                @endif

                            </div>

                        </div>
                    </div>

                    <div class="lg:hidden absolute top-3 -end-3 z-10">
                        <!-- Sidebar Close -->
                        <button type="button" class="w-6 h-7 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-md border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#hs-pro-sidebar">
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="7 8 3 12 7 16"/><line x1="21" x2="11" y1="12" y2="12"/><line x1="21" x2="11" y1="6" y2="6"/><line x1="21" x2="11" y1="18" y2="18"/></svg>
                        </button>
                        <!-- End Sidebar Close -->
                    </div>
                </aside>

                <div class="lg:ps-[300px] space-y-5 col-span-3">
                    <!-- Card -->

                    <div class=" justify-center">
                        <div class="">
                            <!-- Card -->
                            <div
                                id="tedxt"
                                class="flex flex-col bg-white border border-stone-200 overflow-hidden rounded-xl shadow-sm dark:bg-neutral-800 dark:border-neutral-700">
                                <!-- Header -->

                                <div
                                    class="py-3 px-5 flex  items-center gap-x-5 border-b border-stone-200 dark:border-neutral-700">
                                    <div class="lg:hidden">
                                        <!-- Sidebar Toggle -->
                                        <button type="button" class="w-7 h-[38px] inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 focus:outline-none focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" aria-haspopup="dialog" aria-expanded="false" aria-controls="hs-pro-sidebar" aria-label="Toggle navigation"  data-hs-overlay="#hs-pro-sidebar">
                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 8L21 12L17 16M3 12H13M3 6H13M3 18H13"/></svg>
                                        </button>
                                        <!-- End Sidebar Toggle -->
                                    </div>


                                    <h2 class="inline-block font-semibold text-stone-800 dark:text-neutral-200">
                                        @if($lesson)
                                            @lang('Lesson Details') - {{ $lesson->title }}
                                        @else
                                            @lang('Lesson Details')
                                        @endif
                                    </h2>

                                </div>
                                <!-- End Header -->


                                @if($lesson && $lesson->has('materials'))
                                    {{--                            @dd($lesson->pluck('id')->join('-'))--}}
                                    <livewire:lesson-tabs3 :$lesson :key="$lesson->id"/>
                                @else
                                    <x-ui.empty-state/>
                                @endif

                            </div>

                            <!-- Accordion -->
                            @if($lesson != null)
                                <div class="hs-accordion-group">
                                    @if($materials->where('type',\Modules\Course\Enum\ContentType::Audio)->count() > 0)
                                        <div
                                            class="hs-accordion hs-accordion-active:border-gray-200 active bg-white border border-transparent rounded-xl dark:hs-accordion-active:border-neutral-700 dark:bg-neutral-800 dark:border-transparent"
                                            id="hs-active-bordered-heading-one">
                                            <button
                                                class="hs-accordion-toggle hs-accordion-active:text-blue-600 inline-flex justify-between items-center gap-x-3 w-full font-semibold text-start text-gray-800 py-4 px-5 hover:text-gray-500 disabled:opacity-50 disabled:pointer-events-none dark:hs-accordion-active:text-blue-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:outline-none dark:focus:text-neutral-400"
                                                aria-expanded="true" aria-controls="hs-basic-active-bordered-collapse-one">
                                                @lang('Voices')
                                                <svg class="hs-accordion-active:hidden block size-3.5"
                                                     xmlns="http://www.w3.org/2000/svg"
                                                     width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     stroke="currentColor"
                                                     stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M5 12h14"></path>
                                                    <path d="M12 5v14"></path>
                                                </svg>
                                                <svg class="hs-accordion-active:block hidden size-3.5"
                                                     xmlns="http://www.w3.org/2000/svg"
                                                     width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     stroke="currentColor"
                                                     stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M5 12h14"></path>
                                                </svg>
                                            </button>
                                            <div id="hs-basic-active-bordered-collapse-one"
                                                 class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300"
                                                 role="region" aria-labelledby="hs-active-bordered-heading-one">
                                                <div class="pb-4 px-5">
                                                    <dl class="grid grid-cols-2 gap-x-2">
                                                        @isset($materials)
                                                            @foreach($materials->where('type',\Modules\Course\Enum\ContentType::Audio) as $material)
                                                                <dt wire:key="{{ $material->id }}"
                                                                    class="py-5  align-middle text-md text-gray-500 dark:text-neutral-500">
                                                                    {{ $material->audio->name }}:
                                                                </dt>
                                                                <dd wire:key="{{ $material->id }}"
                                                                    class="py-1 inline-flex justify-end items-center gap-x-2 text-end font-medium text-sm text-gray-800 dark:text-neutral-200">
                                                                    {{--                                                            <audio  controls class="w-full">--}}
                                                                    {{--                                                                <source src="{{ Storage::url($material->audio->path) }}"--}}
                                                                    {{--                                                                        type="audio/mp3">--}}
                                                                    {{--                                                                Your browser does not support the audio element.--}}
                                                                    {{--                                                            </audio>--}}
                                                                    <audio
                                                                        {{--                                                                class="mejs__player"--}}
                                                                        {{--                                                                data-mejsoptions='{"alwaysShowControls": "true"}'--}}
                                                                        src="{{Storage::url($material->audio->path)}}" type="audio/mp3" controls="controls"></audio>

                                                                </dd>
                                                            @endforeach
                                                        @endif
                                                    </dl>

                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    @if($lesson->learn_key_points != null)
                                        <div
                                            class="hs-accordion hs-accordion-active:border-gray-200 bg-white border border-transparent rounded-xl dark:hs-accordion-active:border-neutral-700 dark:bg-neutral-800 dark:border-transparent"
                                            id="hs-active-bordered-heading-two">
                                            <button
                                                class="hs-accordion-toggle hs-accordion-active:text-blue-600 inline-flex justify-between items-center gap-x-3 w-full font-semibold text-start text-gray-800 py-4 px-5 hover:text-gray-500 disabled:opacity-50 disabled:pointer-events-none dark:hs-accordion-active:text-blue-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:outline-none dark:focus:text-neutral-400"
                                                aria-expanded="false" aria-controls="hs-basic-active-bordered-collapse-two">
                                                @lang('Learn key points')
                                                <svg class="hs-accordion-active:hidden block size-3.5"
                                                     xmlns="http://www.w3.org/2000/svg"
                                                     width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     stroke="currentColor"
                                                     stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M5 12h14"></path>
                                                    <path d="M12 5v14"></path>
                                                </svg>
                                                <svg class="hs-accordion-active:block hidden size-3.5"
                                                     xmlns="http://www.w3.org/2000/svg"
                                                     width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     stroke="currentColor"
                                                     stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M5 12h14"></path>
                                                </svg>
                                            </button>
                                            <div id="hs-basic-active-bordered-collapse-two"
                                                 class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300"
                                                 role="region" aria-labelledby="hs-active-bordered-heading-two">
                                                <div class="pb-4 px-5 text-gray-800 dark:text-neutral-200">
                                                    <ul class="space-y-3 text-sm">
                                                        @forelse($lesson->learn_key_points ?? [] as $point)
                                                            <li wire:key="{{ $loop->index }}" class="flex gap-x-3">
                                                        <span
                                                            class="size-5 flex justify-center items-center rounded-full bg-blue-50 text-blue-600 dark:bg-blue-800/30 dark:text-blue-500">
                                                          <svg class="shrink-0 size-3.5"
                                                               xmlns="http://www.w3.org/2000/svg"
                                                               width="24"
                                                               height="24" viewBox="0 0 24 24" fill="none"
                                                               stroke="currentColor"
                                                               stroke-width="2" stroke-linecap="round"
                                                               stroke-linejoin="round">
                                                            <polyline points="20 6 9 17 4 12"></polyline>
                                                          </svg>
                                                        </span>
                                                                <span class="text-gray-800 dark:text-neutral-400">
                                                          {{ $point }}
                                                        </span>
                                                            </li>
                                                        @empty
                                                            <li class="flex gap-x-3">
                                                                <span class="text-gray-800 dark:text-neutral-400">
                                                                  Nothing to show
                                                                </span>
                                                            </li>
                                                        @endforelse
                                                    </ul>

                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    @if($lesson->description != null)
                                        <div
                                            class="hs-accordion hs-accordion-active:border-gray-200 bg-white border border-transparent rounded-xl dark:hs-accordion-active:border-neutral-700 dark:bg-neutral-800 dark:border-transparent"
                                            id="hs-active-bordered-heading-two">
                                            <button
                                                class="hs-accordion-toggle hs-accordion-active:text-blue-600 inline-flex justify-between items-center gap-x-3 w-full font-semibold text-start text-gray-800 py-4 px-5 hover:text-gray-500 disabled:opacity-50 disabled:pointer-events-none dark:hs-accordion-active:text-blue-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:outline-none dark:focus:text-neutral-400"
                                                aria-expanded="false" aria-controls="hs-basic-active-bordered-collapse-two">
                                                @lang('Lesson Description')
                                                <svg class="hs-accordion-active:hidden block size-3.5"
                                                     xmlns="http://www.w3.org/2000/svg"
                                                     width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     stroke="currentColor"
                                                     stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M5 12h14"></path>
                                                    <path d="M12 5v14"></path>
                                                </svg>
                                                <svg class="hs-accordion-active:block hidden size-3.5"
                                                     xmlns="http://www.w3.org/2000/svg"
                                                     width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     stroke="currentColor"
                                                     stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M5 12h14"></path>
                                                </svg>
                                            </button>
                                            <div id="hs-basic-active-bordered-collapse-two"
                                                 class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300"
                                                 role="region" aria-labelledby="hs-active-bordered-heading-two">
                                                <div class="pb-4 px-5 text-gray-800 dark:text-neutral-200">
                                                    <p class="text-gray-800 dark:text-neutral-200">
                                                        {{ $lesson->description }}
                                                    </p>

                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    @auth
                                        <div
                                            class="hs-accordion hs-accordion-active:border-gray-200 bg-white border border-transparent rounded-xl dark:hs-accordion-active:border-neutral-700 dark:bg-neutral-800 dark:border-transparent"
                                            id="hs-active-bordered-heading-three">
                                            <button
                                                class="hs-accordion-toggle hs-accordion-active:text-blue-600 inline-flex justify-between items-center gap-x-3 w-full font-semibold text-start text-gray-800 py-4 px-5 hover:text-gray-500 disabled:opacity-50 disabled:pointer-events-none dark:hs-accordion-active:text-blue-500 dark:text-neutral-200 dark:hover:text-neutral-400 dark:focus:outline-none dark:focus:text-neutral-400"
                                                aria-expanded="false" aria-controls="hs-basic-active-bordered-collapse-three">
                                                @lang('Teacher Notes')
                                                <svg class="hs-accordion-active:hidden block size-3.5"
                                                     xmlns="http://www.w3.org/2000/svg"
                                                     width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     stroke="currentColor"
                                                     stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M5 12h14"></path>
                                                    <path d="M12 5v14"></path>
                                                </svg>
                                                <svg class="hs-accordion-active:block hidden size-3.5"
                                                     xmlns="http://www.w3.org/2000/svg"
                                                     width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                     stroke="currentColor"
                                                     stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M5 12h14"></path>
                                                </svg>
                                            </button>
                                            <div id="hs-basic-active-bordered-collapse-three"
                                                 class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300"
                                                 role="region" aria-labelledby="hs-active-bordered-heading-three">
                                                <div class="pb-4 px-5">
                                                    <div class="w-full mx-auto">
                                            <textarea wire:model.blur="teacherNotes" x-data="{
                                                        resize () {
                                                            $el.style.height = '120px';
                                                            $el.style.height = $el.scrollHeight + 'px'
                                                        }
                                                    }"
                                                      x-init="resize()"
                                                      @input="resize()"
                                                      type="text"
                                                      class="flex w-full h-auto min-h-[180px] px-3 py-2 text-sm bg-white border rounded-md border-neutral-300 ring-offset-background placeholder:text-neutral-400 focus:border-neutral-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-neutral-400 disabled:cursor-not-allowed disabled:opacity-50"
                                            ></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endauth

                                </div>
                            @endif

                        </div>

                    </div>
                    <!-- End Card -->

                </div>
            </div>
            <!-- End Content -->
        </div>
        <!-- End Container -->
    <!-- ========== END MAIN CONTENT ========== -->
    </main>


</div>

@push('styles')

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mediaelement/7.0.5/mediaelementplayer.min.css" integrity="sha512-7D82/+kVzJhvYhr5k2+TueNVDAoZA4MAklU9vjupYEAnLx97qV3UFZQZgwaD6dPSAEEIiQXBDkK0Wh5BMTUFBA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/mediaelement/7.0.5/mediaelementplayer-legacy.min.css" integrity="sha512-1F9Inrxny4CHWtGkZ1DdCJfpfkL4/mDe4sEKFkUquxNQaZRJSo7XyrAQpsdAboHOZL9WkeiJB4O9BfVToRrDfg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
@endpush

@push('scripts')
    {{--    <script src="https://cdnjs.cloudflare.com/ajax/libs/mediaelement/7.0.5/mediaelement.min.js" integrity="sha512-FJ0tgnw59v9io5WU0Esqfu2tcMYHfo7U+GwzrSgcxiqJtFK1/GNiyY8fWfccwvtbR3yyCB+NKbjO88FwpxLZQQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>--}}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mediaelement/7.0.5/mediaelement-and-player.min.js" integrity="sha512-ZjSJNNah4veKupXd2MgQ5QFEjF9sKShuRAzH/isJ5AzFMBHGNxSGz/FBkYbmbm/fYohH5hqHb9O74eSspybb5g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script>

        function initMediaElement() {
            var mediaElements = document.querySelectorAll('audio');
            for (var i = 0, total = mediaElements.length; i < total; i++) {
                new MediaElementPlayer(mediaElements[i], {
                    iconSprite: '/mejs-controls.svg',
                    // When using `MediaElementPlayer`, an `instance` argument
                    // is available in the `success` callback
                    // success: function(mediaElement, originalNode, instance) {
                    //
                    // }
                });
            }
        }






        document.addEventListener('livewire:init', () => {
            document.addEventListener('livewire:navigated', () => {
                initMediaElement();
            });

            window.Livewire.on('lesson-changed-end', (event) => {
                initMediaElement();

            });
        });



    </script>
    <script>
        document.addEventListener('contextmenu', event => event.preventDefault());
        document.addEventListener('keydown', event => {
            if (event.ctrlKey && (event.key === 'p' || event.key === 's')) {
                event.preventDefault();
            }
        });
    </script>


@endpush
