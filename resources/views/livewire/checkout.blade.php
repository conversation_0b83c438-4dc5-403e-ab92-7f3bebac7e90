<div class="max-w-[85rem] px-4 sm:px-6 lg:px-8 mx-auto">
    <div class="grid lg:grid-cols-3 gap-y-8 lg:gap-y-0 lg:gap-x-6">
        <!-- Content -->
        <div class="lg:col-span-2 flex flex-col justify-around">
            <div class="py-8 lg:pe-8 ">
                <div class="space-y-5 lg:space-y-8">
                    <a  class="inline-flex items-center gap-x-1.5 text-sm text-gray-600 decoration-2 hover:underline dark:text-blue-500"
                        href="{{ url('/') }}">
                        <svg class="flex-shrink-0 size-4 rtl:rotate-180" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
                        @lang('Back to home')
                    </a>
                    <h2 class="text-3xl font-bold lg:text-5xl dark:text-white">@lang('Checkout')</h2>

                    <div class="">
                        <x-filament-panels::form  >
                            {{ $this->form }}
                        </x-filament-panels::form>
                    </div>

                </div>
            </div>
            <x-ui.footer class="" />

        </div>
        <!-- End Content -->

        <!-- Sidebar -->
        <div class="min-h-screen lg:col-span-1 lg:w-full lg:h-full ltr:lg:bg-gradient-to-r rtl:lg:bg-gradient-to-l lg:from-gray-50 lg:via-transparent lg:to-transparent dark:from-neutral-800">
            <div class="sticky top-0 start-0 py-8 lg:ps-8">

                <!-- Order Summary Card -->
                <div class="flex flex-col ">

                    <!-- Body -->
                    <div class="pt-4 pb-5 px-5 flex flex-col justify-between h-full">
                        <!-- Item -->
                        <div class="pt-3 mt-3 border-t border-stone-200 first:pt-0 first:mt-0 first:border-t-0 dark:border-neutral-700">
                            <div class="flex items-center gap-x-5">
                                <div class="relative flex-shrink-0">
                                    <img class="flex-shrink-0 size-20 rounded-md" src="{{ $course->getMediaUrlWithFallback(conversion: 'cover') }}" alt="Image Description">
                                    <div class="absolute -top-2.5 -end-2.5 z-10">
                                        <span class="py-0.5 px-1.5 min-w-[24px] min-h-[24px] flex flex-col justify-center items-center rounded-full text-xs font-medium bg-green-600 border-2 border-white text-white dark:bg-green-500 dark:border-neutral-700">1</span>
                                    </div>
                                </div>
                                <div class="grow">
                                    <ul>
                                        <li>
                                            <span class="text-sm text-stone-800 dark:text-neutral-200">${{ $course->price }}</span>
                                        </li>
                                        <li>
                                            <span class="text-sm text-stone-800 dark:text-neutral-200">{{ $course->title }}</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <!-- End Item -->

                        <p class="mt-2 text-xs text-stone-500 dark:text-neutral-500">
                            @lang('Terms and conditions applied')
                        </p>
                        <!-- End Input -->

                        <!-- List -->
                        <div class="pt-3 mt-3 border-t border-stone-200 first:pt-0 first:mt-0 first:border-t-0 dark:border-neutral-700">
                            <dl class="grid grid-cols-2 gap-2">
                                <dt class="text-sm text-stone-800 dark:text-neutral-200">@lang('Subtotal'):</dt>
                                <dd class="text-end font-semibold text-sm text-stone-800 dark:text-neutral-200">${{ $course->price }}</dd>
                            </dl>
                        </div>
                        <!-- End List -->

                        <!-- List -->
{{--                        <div class="pt-3 mt-3 border-t border-stone-200 first:pt-0 first:mt-0 first:border-t-0 dark:border-neutral-700">--}}
{{--                            <dl class="grid grid-cols-2 gap-2">--}}
{{--                                <dt class="text-sm text-stone-800 dark:text-neutral-200">@lang('Est tax'):</dt>--}}
{{--                                <dd class="text-end font-semibold text-sm text-stone-800 dark:text-neutral-200">${{ number_format($tax, 2) }}</dd>--}}
{{--                            </dl>--}}
{{--                        </div>--}}
                        <!-- End List -->

                        <!-- List -->
                        <div class="pt-3 mt-3 border-t border-stone-200 first:pt-0 first:mt-0 first:border-t-0 dark:border-neutral-700">
                            <dl class="grid grid-cols-2 gap-2">
                                <dt class="text-sm text-stone-800 dark:text-neutral-200">@lang('Discount'):</dt>
                                <dd class="text-end font-semibold text-sm text-stone-800 dark:text-neutral-200">${{ number_format($discount, 2) }}</dd>
                            </dl>
                        </div>
                        <!-- End List -->

                        <!-- List -->
                        <div class="pt-3 mt-3 border-t border-stone-200 first:pt-0 first:mt-0 first:border-t-0 dark:border-neutral-700">
                            <dl class="grid grid-cols-2 gap-2">
                                <dt class="text-sm text-stone-800 dark:text-neutral-200">@lang('Order total'):</dt>
                                <dd class="text-end font-semibold text-sm text-stone-800 dark:text-neutral-200">${{ number_format($totalPrice, 2) }}</dd>
                            </dl>
                        </div>
                        <!-- End List -->

                        <!-- List -->
                        <div class="pt-3 mt-3 border-t border-stone-200 first:pt-0 first:mt-0 first:border-t-0 dark:border-neutral-700">
                            <div class="grid grid-cols-2 gap-2">
                                <div>
                                    <span class="text-[13px] text-gray-800 dark:text-neutral-200">@lang('Promo code')</span>
                                </div>

                                <div class="flex justify-end">

                                    <!-- Promo Code Button Icon -->
                                    <div class="hs-dropdown [--auto-close:inside] [--placement:bottom-right] inline-flex">
                                        <button id="hs-pro-shospcd" type="button" class="inline-flex items-center gap-x-1 text-[13px] text-gray-500 underline underline-offset-4 hover:text-gray-600 focus:outline-none focus:text-gray-600 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-500 dark:hover:text-neutral-400 dark:focus:text-neutral-400" aria-haspopup="menu" aria-expanded="false" aria-label="Dropdown">
                                            @lang('Enter code')
                                        </button>
                                        <!-- End Promo Code Button Icon -->

                                        <!-- Promo Code Dropdown -->
                                        <div class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] md:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 w-72 hidden z-10 bg-white border border-gray-100 mt-2 rounded-xl shadow-lg before:absolute before:-top-4 before:start-0 before:w-full before:h-5 dark:bg-neutral-900 dark:border-neutral-700" role="menu" aria-orientation="vertical" aria-labelledby="hs-pro-shospcd">
                                            <div class="p-2">
                                                <!-- Input Group -->
                                                <div>
                                                    <label for="hs-pro-shchsetcpc" class="sr-only">
                                                        Promo code
                                                    </label>

                                                    <div class="p-0.5 flex flex-row items-center bg-white border border-gray-200 rounded-lg dark:bg-neutral-900 dark:border-neutral-700">
                                                        <div class="relative w-full">
                                                            <input wire:model="couponCode" type="text" class="py-2 px-3 block w-full border-transparent rounded-md text-xs focus:border-transparent focus:ring-transparent disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:text-neutral-400 dark:placeholder-neutral-500" placeholder="@lang('Promo code')">
                                                        </div>
                                                        <button wire:click="applyCoupon" type="button" class="py-2 px-3 whitespace-nowrap text-xs font-medium text-indigo-600 border border-transparent rounded-md hover:bg-indigo-50 focus:outline-none focus:bg-indigo-50 disabled:opacity-50 disabled:pointer-events-none dark:text-indigo-500 dark:hover:bg-indigo-500/20 dark:focus:bg-indigo-500/20">
                                                            @lang('Apply')
                                                        </button>
                                                    </div>
                                                </div>
                                                <!-- End Input Group -->
                                            </div>
                                        </div>
                                    </div>
                                    <!-- End Promo Code Dropdown -->
                                </div>
                            </div>
                        </div>
                        <!-- End List -->


                    </div>
                    <!-- End Body -->

                    <!-- Button Group -->
                    <div class="p-4 border-t border-stone-200 dark:border-neutral-700">
                        <div class="flex gap-x-3">
                            <a href="{{ url("/") }}" type="button" class="py-2 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border border-stone-200 bg-white text-stone-800 shadow-sm hover:bg-stone-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-stone-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700">
                                @lang('Cancel')
                            </a>

                            <button wire:click="create" form="order-23" type="button" class="py-2 px-3 w-full inline-flex justify-center items-center gap-x-2 text-sm font-semibold rounded-lg border border-transparent bg-green-600 text-white hover:bg-green-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:ring-2 focus:ring-green-500">
                                @lang('Place order')
                            </button>
                        </div>
                    </div>
                    <!-- End Button Group -->
                </div>
                <!-- End Order Summary Card -->
            </div>
        </div>
        <!-- End Sidebar -->
    </div>
</div>
