<?php

use Livewire\Attributes\On;
use Livewire\Attributes\Reactive;
use Livewire\Volt\Component;

use Modules\Course\Models\Lesson;

new class extends Component {

    public Lesson $lesson;
    public $materials;

    public function mount($lesson): void
    {
        $this->lesson = $lesson;
        $this->materials = $this->lesson->materials->where('type','!==', \Modules\Course\Enum\ContentType::Audio);
    }

    #[On('lesson-changed')]
    public function LessonChanged($lesson): void
    {
        $this->lesson = Lesson::find($lesson);
        $this->materials = $this->lesson->materials->where('type','!==', \Modules\Course\Enum\ContentType::Audio);
    }

    public function rendered($view, $html)
    {
        $this->dispatch('lesson-changed-end');
    }


}


?>


<div id="fullscreen"  class=" bg-white rounded-lg shadow-md dark:bg-neutral-800"
     x-data="{ textSize: 1}" >

    <style>
        .fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            background-color: white;
        }
    </style>
    <div
        class="px-5 pt-3 flex justify-between items-center border-b border-gray-200 dark:border-neutral-700">
        <nav id="tabbbbb" class=" flex gap-x-1"
             aria-label="Tabs" role="tablist" aria-orientation="horizontal">
            @foreach($materials as $item)
                <button wire:key="{{ str()->random(50) }}" type="button"
                        class="hs-tab-active:font-semibold hs-tab-active:border-blue-600 hs-tab-active:text-blue-600 py-4 px-1 inline-flex items-center gap-x-2 border-b-2 border-transparent text-sm whitespace-nowrap text-gray-500 hover:text-blue-600 focus:outline-none focus:text-blue-600 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-400 dark:hover:text-blue-500 {{ $loop->first ? 'active' : '' }}"
                        id="tabs-item-{{ $item->id }}"
                        aria-selected="{{ $loop->first ? 'true' : 'false' }}"
                        data-hs-tab="#tabs-{{ $item->id }}"
                        aria-controls="tabs-{{ $item->id }}"
                        role="tab">
                    {{ $item->title }}
                </button>
            @endforeach
        </nav>
        <div class="">
            {{--            <div class="hs-tooltip relative inline-block mb-3">--}}
            {{--                <a x-on:click.prevent="textSize += 0.1"--}}
            {{--                   class="hs-tooltip-toggle w-7 h-7 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"--}}
            {{--                   href="#">--}}
            {{--                    <x-heroicon-o-plus class="flex-shrink-0 w-4 h-4"/>--}}
            {{--                </a>--}}
            {{--                <span--}}
            {{--                    class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700"--}}
            {{--                    role="tooltip">--}}
            {{--                {{ __('Increase Text Size') }}--}}
            {{--            </span>--}}
            {{--            </div>--}}
            {{--            <div class="hs-tooltip relative inline-block mb-3">--}}
            {{--                <a x-on:click.prevent="textSize -= 0.1"--}}
            {{--                   class="hs-tooltip-toggle w-7 h-7 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"--}}
            {{--                   href="#">--}}
            {{--                    <x-heroicon-o-minus class="flex-shrink-0 w-4 h-4"/>--}}
            {{--                </a>--}}
            {{--                <span--}}
            {{--                    class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700"--}}
            {{--                    role="tooltip">--}}
            {{--                {{ __('Decrease Text Size') }}--}}
            {{--            </span>--}}
            {{--            </div>--}}
            <div class="hs-tooltip relative inline-block mb-3">
                <a {{--x-on:click.prevent="window.showMarkerArea();"--}}
                   class="hs-tooltip-toggle w-7 h-7 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                   href="#">
                    <x-heroicon-o-minus class="flex-shrink-0 w-4 h-4"/>
                </a>

                <button {{--x-on:click.prevent="isFullscreen = !isFullscreen"--}} onclick="requestFullScreen(document.getElementById('fullscreen'))"
                        class="hs-tooltip-toggle w-7 h-7 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                        href="#">
                    <x-heroicon-o-arrows-pointing-out class="flex-shrink-0 w-4 h-4"/>
                </button>




                <span
                    class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700"
                    role="tooltip">
                        {{ __('Fullscrren') }}
                </span>
            </div>

        </div>

    </div>

    <div class="m-4">
        @foreach($materials as $item)
            <div wire:key="{{ $item->id }}"
                 id="tabs-{{ $item->id }}"
                 role="tabpanel"
                 @class([
                     'hidden' => !$loop->first,
                 ])
                 aria-labelledby="tabs-item-{{ $item->id }}">

            @switch($item->type)
                    @case(\Modules\Course\Enum\ContentType::Embed)


                        @break

                    @case(\Modules\Course\Enum\ContentType::Video)

                        @if($item->video?->path == null)
                            {!! $item->embed_code !!}
                        @endif

                        <div style="position: relative; padding-top: 56.25%;">
                            <iframe
                                src="https://customer-{{ config('services.cloudflare.code') }}.cloudflarestream.com/{{ $item->video?->path }}/iframe"
                                loading="lazy"
                                style="border: none; position: absolute; top: 0; left: 0; height: 100%; width: 100%;"
                                allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
                                allowfullscreen="true"
                            ></iframe>
                        </div>


                        @break

                    @case(\Modules\Course\Enum\ContentType::Image)
                        <div id="images">
                            <a class="overflow-hidden p-5"
                               href="{{ Storage::url($item->file) }}"
                               data-img="{{ Storage::url($item->file) }}"
                               data-width="8230"
                               data-height="10850"
                            >

                                <img id="myImage" src="{{ Storage::url($item->file) }}" alt="{{ $item->title }}"
                                     data-image="{{ $item?->file }}"
                                     onclick="window.showMarkerArea();"
                                     class="myImage w-[100vw] mx-auto h-auto shadow-lg">

                            </a>
{{--                            <pdfjs-viewer-element--}}
{{--                                style="height: 100dvh"--}}
{{--                                class="border-start"--}}
{{--                                src="{{ (Storage::url(str_replace('.jpg', '', $item->file).'.pdf')) }}"--}}
{{--                                viewer-path="{{ asset('/pdfjs') }}"--}}
{{--                                phrase="true"--}}
{{--                                pagemode="none"--}}
{{--                                viewer-css-theme="AUTOMATIC"--}}
{{--                                viewer-extra-styles="#download,#print,#editorStamp{display: none}"--}}
{{--                                viewer-extra-styles-urls="">--}}
{{--                            </pdfjs-viewer-element>--}}


                        </div>


                        @break

                    @case(\Modules\Course\Enum\ContentType::PDF)
{{--                        @dd(\Modules\MediaLibrary\Models\PdfPage::find($item->pdf_pages[0])->page_path)--}}
                    @php
                        if ($item->external_link) {
                            $src = Storage::drive('local')->temporaryUrl($item->external_link, now()->addHours(6));
                        } else {
                            $src = Storage::drive('local')->temporaryUrl(\Modules\MediaLibrary\Models\PdfPage::find($item->pdf_pages[0])->page_path, now()->addHours(6));
                        }
                    @endphp
                        <pdfjs-viewer-element
                            style="height: 100dvh"
                            class="border-start"
                            src="{{ $src }}"
                            viewer-path="{{ asset('/pdfjs') }}"
                            phrase="true"
                            locale="ar"
                            pagemode="none"
                            viewer-css-theme="LIGHT"
                            viewer-extra-styles="#download,#print,#editorStamp{display: none}"
                            viewer-extra-styles-urls="/pdfjs/custom.css">
                        </pdfjs-viewer-element>

                        @if(isset($item->text))
                        <div class="mt-4 py-3 flex items-center text-sm text-stone-800 before:flex-1 before:border-t before:border-stone-200 before:me-3 after:flex-1 after:border-t after:border-stone-200 after:ms-3 dark:text-white dark:before:border-neutral-600 dark:after:border-neutral-600">
                            <button type="button" class="hs-collapse-toggle py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none" id="hs-basic-collapse-{{$item->id}}" aria-expanded="false" aria-controls="hs-basic-collapse-{{$item->id}}-heading" data-hs-collapse="#hs-basic-collapse-{{$item->id}}-heading">
                                @lang('Lesson Guide')
                                <svg class="hs-collapse-open:rotate-180 shrink-0 size-4 text-white" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="m6 9 6 6 6-6"></path>
                                </svg>
                            </button>
                        </div>
                        <div id="hs-basic-collapse-{{$item->id}}-heading" class="hs-collapse hidden w-full overflow-hidden transition-[height] duration-300" aria-labelledby="hs-basic-collapse-{{$item->id}}">
                            <div class="mt-5 text-gray-500 dark:text-gray-100">
                                <p>
                                    {!! $item->text !!}
                                </p>
                            </div>
                        </div>
                        @endif

                        @break

                    @case(\Modules\Course\Enum\ContentType::Youtube)
                        <iframe width="100%" height="500"
                                src="https://www.youtube.com/embed/{{ $item->youtube_id }}"
                                title="YouTube video player" frameborder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope;"
                                referrerpolicy="strict-origin-when-cross-origin"
                                allowfullscreen>
                        </iframe>
                        @break
                    @default
                        <p class="mt-2 font-medium dark:text-neutral-200 ">
                            {!! $item->text !!}
                        </p>
                @endswitch

            </div>
        @endforeach

    </div>

    @push('styles')

        <link href="
            https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.6.82/pdf_viewer.min.css
        " rel="stylesheet">

    @endpush
    @push('scripts')



        <script type="module" src="https://cdn.skypack.dev/pdfjs-viewer-element"></script>
        <script type="module" >

            // document.addEventListener('DOMContentLoaded', async () => {
            //
            //     const element = document.getElementById('images');
            //     const iframe = document.createElement('iframe');
            //
            //     iframe.src = `/pdfjs/web/viewer.html?file=${encodeURIComponent('https://arabic.test/sample.pdf')}`;
            //     iframe.width = '100%';
            //     iframe.height = '800px';
            //
            //     element.appendChild(iframe);
            // })

        </script>
        <script>
            // document.addEventListener('DOMContentLoaded', async () => {
            //     const viewer = document.querySelector('pdfjs-viewer-element')
            //     // Wait for the viewer initialization, receive PDFViewerApplication
            //     const viewerApp = await viewer.initialize()
            //     // Open PDF file data using Uint8Array instead of URL
            //     viewerApp.open(pdfData)
            // })


            // document.addEventListener('livewire:init', () => {
            //     window.Livewire.on('lesson-changed-end', async (event) => {
            //         console.log('lesson-changed-sad');
            //         const viewer = document.querySelector('pdfjs-viewer-element')
            //         //get src attribute
            //         const src = viewer.getAttribute('src');
            //
            //
            //         // Wait for the viewer initialization, receive PDFViewerApplication
            //         const viewerApp = await viewer.initialize()
            //
            //         viewerApp.open({url: src})
            //
            //         // window.showMarkerArea();
            //
            //     });
            // });


            function requestFullScreen(element) {
                // Supports most browsers and their versions.
                var requestMethod = element.requestFullScreen || element.webkitRequestFullScreen || element.mozRequestFullScreen || element.msRequestFullScreen;

                if (requestMethod) { // Native full screen.
                    requestMethod.call(element);
                    element.style.overflowY = 'auto'; // add scrollbar

                } else if (typeof window.ActiveXObject !== "undefined") { // Older IE.
                    var wscript = new ActiveXObject("WScript.Shell");
                    if (wscript !== null) {
                        wscript.SendKeys("{F11}");
                    }
                }
                // if fullscreen is on then exit
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                    element.style.overflowY = 'hidden'; // remove scrollbar

                }
            }





            // Get the image element
            // const image = document.querySelector('.myImage');
            //
            // // Add an event listener for clicks on the image
            // image.addEventListener('click', (event) => {
            //     // Get the mouse coordinates
            //     const x = event.clientX;
            //     const y = event.clientY;
            //
            //     // Calculate the zoomed image coordinates
            //     const zoomX = x - (image.width / 2);
            //     const zoomY = y - (image.height / 2);
            //
            //     // Apply CSS transformations
            //     image.style.transform = `scale(2) translate(${zoomX}px, ${zoomY}px)`;
            // });


        </script>


    @endpush


</div>
