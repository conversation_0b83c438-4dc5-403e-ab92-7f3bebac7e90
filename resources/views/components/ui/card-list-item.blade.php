@props(['course'])

<!-- Card -->
<div class="p-3 relative group flex items-center gap-x-3 bg-white border border-gray-200 rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
    <img class="size-[38px] object-cover rounded-lg"
         src="{{ $course->getMediaUrlWithFallback(conversion: 'cover') }}"
         alt="Image Description">

    <div class="grow truncate">
        <a href="{{ route('course.show', ['course' => $course]) }}" class="block truncate text-sm font-semibold text-gray-800 dark:text-neutral-200">
            {{ $course->title }}
        </a>
{{--        <p class="block truncate text-xs text-gray-500 dark:text-neutral-500">--}}
{{--            @lang('Enrolled'): {{ $course->created_at->translatedFormat('d F Y') }}--}}
{{--        </p>--}}
    </div>

    <!-- More Dropdown -->
{{--    <div class="lg:absolute lg:top-3 lg:end-3 group-hover:opacity-100 lg:opacity-0">--}}
{{--        <div class="p-0.5 sm:p-1 inline-flex items-center gap-0.5 bg-white border border-gray-200 lg:shadow rounded-lg dark:bg-neutral-800 dark:border-neutral-700">--}}

{{--            <!-- Button Icon -->--}}
{{--            <div class="hs-tooltip inline-block">--}}
{{--                <button type="button"--}}
{{--                        class="hs-tooltip-toggle size-[30px] inline-flex justify-center items-center gap-x-2 rounded-lg border border-transparent text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-400 focus:outline-none focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"--}}
{{--                        data-hs-overlay="#hs-pro-dupfmsh">--}}
{{--                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"--}}
{{--                         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"--}}
{{--                         stroke-linejoin="round">--}}
{{--                        <circle cx="18" cy="5" r="3"/>--}}
{{--                        <circle cx="6" cy="12" r="3"/>--}}
{{--                        <circle cx="18" cy="19" r="3"/>--}}
{{--                        <line x1="8.59" x2="15.42" y1="13.51" y2="17.49"/>--}}
{{--                        <line x1="15.41" x2="8.59" y1="6.51" y2="10.49"/>--}}
{{--                    </svg>--}}
{{--                </button>--}}

{{--                <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">--}}
{{--                  Share--}}
{{--                </span>--}}
{{--            </div>--}}
{{--            <!-- End Button Icon -->--}}

{{--            <div class="w-px h-5 mx-1 bg-gray-200 dark:bg-neutral-700"></div>--}}

{{--            <!-- Button Icon -->--}}
{{--            <div class="hs-tooltip inline-block">--}}
{{--                <button type="button"--}}
{{--                        class="hs-tooltip-toggle size-[30px] inline-flex justify-center items-center gap-x-2 rounded-lg border border-transparent text-red-600 hover:bg-red-100  disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-red-100 dark:text-red-500 dark:hover:bg-red-500/20 dark:focus:bg-red-500/20"--}}
{{--                        data-hs-overlay="#hs-pro-dupfmdl">--}}
{{--                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"--}}
{{--                         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"--}}
{{--                         stroke-linejoin="round">--}}
{{--                        <path d="M3 6h18"/>--}}
{{--                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>--}}
{{--                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>--}}
{{--                        <line x1="10" x2="10" y1="11" y2="17"/>--}}
{{--                        <line x1="14" x2="14" y1="11" y2="17"/>--}}
{{--                    </svg>--}}
{{--                </button>--}}
{{--                <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">--}}
{{--                  Delete--}}
{{--                </span>--}}
{{--            </div>--}}
{{--            <!-- End Button Icon -->--}}
{{--        </div>--}}
{{--    </div>--}}
    <!-- End More Dropdown -->
</div>
<!-- End Card -->
<!-- Share File Modal -->
{{--<div id="hs-pro-dupfmsh"--}}
{{--     class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto [--close-when-click-inside:true] pointer-events-none">--}}
{{--    <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all sm:max-w-md sm:w-full m-3 sm:mx-auto h-[calc(100%-3.5rem)] min-h-[calc(100%-3.5rem)] flex items-center">--}}
{{--        <div class="relative w-full max-h-full overflow-hidden flex flex-col bg-white rounded-xl pointer-events-auto shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)] dark:shadow-[0_10px_40px_10px_rgba(0,0,0,0.2)] dark:bg-neutral-800">--}}
{{--            <!-- Header -->--}}
{{--            <div class="py-3 px-4 flex justify-between items-center border-b dark:border-neutral-700">--}}
{{--                <h3 class="font-semibold text-gray-800 dark:text-neutral-200">--}}
{{--                    Share this file--}}
{{--                </h3>--}}
{{--                <button type="button"--}}
{{--                        class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-700 dark:hover:bg-neutral-600 dark:text-neutral-400 dark:focus:bg-neutral-600"--}}
{{--                        data-hs-overlay="#hs-pro-dupfmsh">--}}
{{--                    <span class="sr-only">Close</span>--}}
{{--                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"--}}
{{--                         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"--}}
{{--                         stroke-linejoin="round">--}}
{{--                        <path d="M18 6 6 18"/>--}}
{{--                        <path d="m6 6 12 12"/>--}}
{{--                    </svg>--}}
{{--                </button>--}}
{{--            </div>--}}
{{--            <!-- End Header -->--}}

{{--            <div class="overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">--}}
{{--                <form>--}}
{{--                    <!-- Body -->--}}
{{--                    <div class="p-4 space-y-4">--}}
{{--                        <input type="text"--}}
{{--                               class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600"--}}
{{--                               placeholder="Search for team or person">--}}

{{--                        <textarea id="hs-pro-daemdc"--}}
{{--                                  class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm placeholder:text-gray-400 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:placeholder:text-white/60 dark:focus:ring-neutral-600"--}}
{{--                                  rows="3" placeholder="Add a message, if you’d like."></textarea>--}}

{{--                        <!-- Media -->--}}
{{--                        <div class="p-3 flex items-center gap-x-3 border border-gray-200 rounded-xl dark:border-neutral-700">--}}
{{--                            <img class="size-[38px] object-cover rounded-md"--}}
{{--                                 src="https://images.unsplash.com/photo-1635776062127-d379bfcba9f8?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=988&q=80"--}}
{{--                                 alt="Image Description">--}}

{{--                            <div class="grow truncate">--}}
{{--                                <p class="block truncate text-sm font-semibold text-gray-800 dark:text-neutral-200">--}}
{{--                                    gradient.png--}}
{{--                                </p>--}}
{{--                                <p class="block truncate text-xs text-gray-500 dark:text-neutral-500">--}}
{{--                                    James Sep 2nd, 2022--}}
{{--                                </p>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <!-- End Media -->--}}
{{--                    </div>--}}
{{--                    <!-- End Footer -->--}}

{{--                    <!-- Footer -->--}}
{{--                    <div class="p-4 flex justify-between gap-x-2">--}}
{{--                        <div class="w-full">--}}
{{--                            <input id="hs-mshfctc" type="text" class="hidden"--}}
{{--                                   value="https://www.figma.com/community/file/1179068859697769656">--}}

{{--                            <button type="button"--}}
{{--                                    class="js-clipboard [--is-toggle-tooltip:false] hs-tooltip py-2 px-2.5 inline-flex justify-center items-center gap-x-1 rounded-lg bg-gray-100 font-medium text-xs text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 dark:bg-neutral-700 dark:text-neutral-200 dark:hover:bg-neutral-600 dark:focus:bg-neutral-600"--}}
{{--                                    data-clipboard-target="#hs-mshfctc"--}}
{{--                                    data-clipboard-action="copy"--}}
{{--                                    data-clipboard-success-text="Link copied">--}}
{{--                                <svg class="flex-shrink-0 size-3" xmlns="http://www.w3.org/2000/svg" width="24"--}}
{{--                                     height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"--}}
{{--                                     stroke-linecap="round" stroke-linejoin="round">--}}
{{--                                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>--}}
{{--                                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>--}}
{{--                                </svg>--}}
{{--                                <span class="js-clipboard-success-text">Copy link</span>--}}
{{--                            </button>--}}
{{--                        </div>--}}

{{--                        <div class="w-full flex justify-end items-center gap-x-2">--}}
{{--                            <button type="button"--}}
{{--                                    class="py-2 px-3 inline-flex justify-center items-center text-start bg-white border border-gray-200 text-gray-800 text-sm font-medium rounded-lg shadow-sm align-middle hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"--}}
{{--                                    data-hs-overlay="#hs-pro-dupfmsh">--}}
{{--                                Cancel--}}
{{--                            </button>--}}

{{--                            <button type="button"--}}
{{--                                    class="py-2 px-3 inline-flex justify-center items-center gap-x-2 text-start bg-blue-600 border border-blue-600 text-white text-sm font-medium rounded-lg shadow-sm align-middle hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:ring-1 focus:ring-blue-300 dark:focus:ring-blue-500"--}}
{{--                                    data-hs-overlay="#hs-pro-dupfmsh">--}}
{{--                                Share--}}
{{--                            </button>--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                    <!-- End Footer -->--}}
{{--                </form>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </div>--}}
{{--</div>--}}
<!-- End Share File Modal -->
<!-- Delet File Modal -->
<!-- End Delet File Modal -->
