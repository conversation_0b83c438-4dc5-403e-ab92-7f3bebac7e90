<div {{ $attributes->class(['fixed bottom-0 start-1/2 -translate-x-1/2 p-6 z-50 w-full max-w-md']) }}>
    <div class="py-2 ps-5 pe-2 bg-stone-800 rounded-full shadow-md dark:bg-neutral-950">
        <div class="flex justify-between items-center gap-x-3">
            <a class="text-red-400 decoration-2 font-medium text-sm hover:underline focus:outline-none focus:underline dark:text-red-500"
               href="#">Delete</a>

            <div class="inline-flex items-center gap-x-2">
                <a class="text-stone-300 decoration-2 font-medium text-sm hover:underline focus:outline-none focus:underline dark:text-neutral-400"
                   href="#">Cancel</a>
                <div class="w-px h-4 bg-stone-700 dark:bg-neutral-700"></div>
                <a class="text-green-400 decoration-2 font-medium text-sm hover:underline focus:outline-none focus:underline dark:text-green-500"
                   href="#">Save changes</a>

                <!-- Close Button -->
                <button type="button"
                        class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full text-stone-400 hover:bg-stone-700 focus:outline-none focus:bg-stone-700 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800">
                    <span class="sr-only">Close</span>
                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                         stroke-linejoin="round">
                        <path d="M18 6 6 18"/>
                        <path d="m6 6 12 12"/>
                    </svg>
                </button>
                <!-- End Close Button -->
            </div>
        </div>
    </div>
</div>
