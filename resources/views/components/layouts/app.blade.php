<x-html :title="$title ?? generalSetting('site_name')" class="flex flex-col  dark:bg-neutral-900" dir="{{ app()->currentLocale() == 'ar' ? 'rtl' : 'ltr'}}">
    <x-slot name="head">
        <x-social-meta
            :title="generalSetting('site_name')"
            description="ArabicForAll - Learn Arabic Language"
            image="http://example.com/social.jpg"
        />
        {{-- <link rel="shortcut icon" href="https://preline.co/favicon.ico"> --}}
        <link rel="shortcut icon" type="image/x-icon" href="{{ asset('favicon.ico') }}" />

        <!-- Font -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap" rel="stylesheet">
        <!-- Theme Check and Update -->
        <script>
            const html = document.querySelector('html');
            const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
            const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

            if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
            else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
            else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
            else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
        </script>

        <!-- CSS HS -->
        @vite(['resources/css/app.css','resources/js/app.js'])

        @stack('styles')
        <!-- google analytics -->
        @if (generalSetting()->google_analytics_tracking_id)
            <script async src="https://www.googletagmanager.com/gtag/js?id={{ generalSetting('google_analytics_tracking_id') }}"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '{{ generalSetting('google_analytics_tracking_id') }}');
            </script>
        @endif

    </x-slot>
    <!-- ========== END HEAD ========== -->


    <!-- ========== HEADER ========== -->
    <x-ui.header />
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" class=" *:relative flex flex-col flex-grow w-full bg-neutral-50 dark:bg-neutral-900">

        @if (isset($header))
            <header class="">
                <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">

                    <div class="dark:border-neutral-700">
                        <!-- Page Header -->
                        <div class="py-3 px-5 flex flex-col sm:flex-row justify-between items-center gap-x-5">
                            {{ $header }}
                        </div>
                        <!-- End Page Header -->
                    </div>

                </div>
            </header>
        @endif



            {{ $slot }}

            <livewire:toast />

    </main>

    <x-ui.footer />

    <!-- ========== END MAIN CONTENT ========== -->

    <!-- ========== FOOTER ========== -->


    <!-- JS Implementing Plugins -->

    @stack('scripts')

    <!-- JS PLUGINS -->
    <!-- Required plugins -->
</x-html>
