<x-html :title="$title ?? generalSetting('site_name')" class="dark:bg-neutral-900" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr'}}">
    <x-slot name="head">
        <x-social-meta
                :title="generalSetting('site_name')"
                description="Blade components are awesome!"
                image="http://example.com/social.jpg"
        />
        {{-- <link rel="shortcut icon" href="https://preline.co/favicon.ico"> --}}
        <link rel="shortcut icon" type="image/x-icon" href="{{ asset('favicon.ico') }}" />

        <!-- Font -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap" rel="stylesheet">
        <!-- Theme Check and Update -->
        <script>
            const html = document.querySelector('html');
            const isLightOrAuto = localStorage.getItem('hs_theme') === 'light' || (localStorage.getItem('hs_theme') === 'auto' && !window.matchMedia('(prefers-color-scheme: dark)').matches);
            const isDarkOrAuto = localStorage.getItem('hs_theme') === 'dark' || (localStorage.getItem('hs_theme') === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches);

            if (isLightOrAuto && html.classList.contains('dark')) html.classList.remove('dark');
            else if (isDarkOrAuto && html.classList.contains('light')) html.classList.remove('light');
            else if (isDarkOrAuto && !html.classList.contains('dark')) html.classList.add('dark');
            else if (isLightOrAuto && !html.classList.contains('light')) html.classList.add('light');
        </script>
        <!-- CSS HS -->
        @vite(['resources/css/app.css','resources/js/app.js'])
    </x-slot>
    <!-- ========== END HEAD ========== -->

    <!-- ========== HEADER ========== -->
    <x-ui.header />
    <!-- ========== END HEADER ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" class="min-h-screen">

        @if (isset($header))
            <header class="">
                <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">

                    <div class="dark:border-neutral-700">
                        <!-- Page Header -->
                        <div class="py-3 px-5 flex flex-col sm:flex-row justify-between items-center gap-x-5">
                            {{ $header }}
                        </div>
                        <!-- End Page Header -->
                    </div>

                </div>
            </header>
        @endif

        {{ $slot }}

    </main>
    <!-- ========== END MAIN CONTENT ========== -->

    <livewire:toast />

    <!-- JS Implementing Plugins -->


    <!-- JS PLUGINS -->
    <!-- Required plugins -->
</x-html>
