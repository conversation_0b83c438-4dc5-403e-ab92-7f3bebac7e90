<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz_questions', function (Blueprint $table) {
            $table->id();
            $table->string('question', 250);
            $table->integer('marks_per_question')->default(0);
            $table->integer('duration')->default(0);
            $table->tinyInteger('type')->default(1); // 1 = single choice, 2 = multiple choice, 3 = true/false
            $table->foreignIdFor(\Modules\Quiz\Models\Quiz::class)->constrained()->cascadeOnDelete();
            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz_questions');
    }
};
