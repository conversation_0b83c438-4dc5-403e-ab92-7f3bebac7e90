<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->encrypt('general.openai_key');
        $this->migrator->encrypt('general.zoom_client_secret');
        $this->migrator->encrypt('general.smtp_username');
        $this->migrator->encrypt('general.smtp_password');
    }
};
