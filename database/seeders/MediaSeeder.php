<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MediaSeeder extends Seeder
{
    public function run()
    {
        DB::table('media')->insert([
            'id' => 1,
            'model_type' => 'course',
            'model_id' => 1,
            'uuid' => '67aed8b1-bb89-47e7-a0c5-aa8a2101c05e',
            'collection_name' => 'default',
            'name' => '01J1KEDW3C1C3KPCG7160DF56X',
            'file_name' => '01J9R98QQ6S587H70B8731QY8B.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 352311,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '{"cover":true}',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:20:24',
            'updated_at' => '2024-10-09 10:20:24',
        ]);

        DB::table('media')->insert([
            'id' => 3,
            'model_type' => 'module',
            'model_id' => 1,
            'uuid' => 'e25572a9-08b7-40e5-8007-856055980c08',
            'collection_name' => 'default',
            'name' => 1,
            'file_name' => '01J9R9J2XS10Z4AH7ZNJ2PMA5F.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 619067,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:25:31',
            'updated_at' => '2024-10-09 10:25:31',
        ]);

        DB::table('media')->insert([
            'id' => 4,
            'model_type' => 'module',
            'model_id' => 2,
            'uuid' => '8190bace-8516-483e-9844-e132bfc204c3',
            'collection_name' => 'default',
            'name' => 2,
            'file_name' => '01J9R9JGRJ56Z2VG2XARX1YREW.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 605572,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:25:45',
            'updated_at' => '2024-10-09 10:25:45',
        ]);

        DB::table('media')->insert([
            'id' => 5,
            'model_type' => 'module',
            'model_id' => 3,
            'uuid' => 'bf33ef5e-64ba-4ed7-a953-0051cbbffd86',
            'collection_name' => 'default',
            'name' => 3,
            'file_name' => '01J9R9JZ75EA371KPCNR8B12E5.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 625804,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:26:00',
            'updated_at' => '2024-10-09 10:26:00',
        ]);

        DB::table('media')->insert([
            'id' => 6,
            'model_type' => 'module',
            'model_id' => 4,
            'uuid' => '82521bc1-ba58-4ab4-afb5-ca231b8ad6f7',
            'collection_name' => 'default',
            'name' => 4,
            'file_name' => '01J9R9KAH6R6D3ZSWJCQMTX22A.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 611342,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:26:11',
            'updated_at' => '2024-10-09 10:26:11',
        ]);

        DB::table('media')->insert([
            'id' => 7,
            'model_type' => 'module',
            'model_id' => 5,
            'uuid' => '727d9f08-9090-4475-90dd-0a39bebcb413',
            'collection_name' => 'default',
            'name' => 'book1',
            'file_name' => '01J9R9KT3CY0R4XQ8JGPD20EWT.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 195631,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:26:27',
            'updated_at' => '2024-10-09 10:26:27',
        ]);

        DB::table('media')->insert([
            'id' => 8,
            'model_type' => 'module',
            'model_id' => 6,
            'uuid' => 'e3174e9f-0d18-47a2-9f14-2ed1ff59d5b5',
            'collection_name' => 'default',
            'name' => 'book2',
            'file_name' => '01J9R9M4HJ939HWEJTPNTM83H3.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 196690,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:26:38',
            'updated_at' => '2024-10-09 10:26:38',
        ]);

        DB::table('media')->insert([
            'id' => 9,
            'model_type' => 'module',
            'model_id' => 7,
            'uuid' => 'f27d1e98-4a82-4a24-a49a-a9616f30f7ff',
            'collection_name' => 'default',
            'name' => 'book3',
            'file_name' => '01J9R9MGFK3KZHSCWMNYGHPDCX.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 196736,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:26:50',
            'updated_at' => '2024-10-09 10:26:50',
        ]);

        DB::table('media')->insert([
            'id' => 10,
            'model_type' => 'module',
            'model_id' => 8,
            'uuid' => 'e2d3ede9-dbe6-410e-90f5-a2ee0650a690',
            'collection_name' => 'default',
            'name' => 'book4',
            'file_name' => '01J9R9MVV31JNEDQMP6G6W200M.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 185620,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:27:02',
            'updated_at' => '2024-10-09 10:27:02',
        ]);

        DB::table('media')->insert([
            'id' => 11,
            'model_type' => 'module',
            'model_id' => 19,
            'uuid' => '353d3df6-361f-43db-8ec0-0e1c2c3d30de',
            'collection_name' => 'default',
            'name' => 'book5',
            'file_name' => '01J9R9N6KHGF5YQCP6R4G1Z9JD.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 193445,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:27:13',
            'updated_at' => '2024-10-09 10:27:13',
        ]);

        DB::table('media')->insert([
            'id' => 12,
            'model_type' => 'module',
            'model_id' => 20,
            'uuid' => 'd4e50d95-d9eb-40ae-b6b2-c09dc32d811f',
            'collection_name' => 'default',
            'name' => 'book6',
            'file_name' => '01J9R9NGZ21B77NAZ1VEPGVXW8.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 190816,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:27:23',
            'updated_at' => '2024-10-09 10:27:23',
        ]);

        DB::table('media')->insert([
            'id' => 13,
            'model_type' => 'module',
            'model_id' => 21,
            'uuid' => '68a2cc61-96f4-412b-a8c1-a4fbe70e9666',
            'collection_name' => 'default',
            'name' => 'book7',
            'file_name' => '01J9R9NXMRBEVF35VDMNCS78WT.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 202772,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:27:36',
            'updated_at' => '2024-10-09 10:27:36',
        ]);

        DB::table('media')->insert([
            'id' => 14,
            'model_type' => 'module',
            'model_id' => 22,
            'uuid' => 'fac86520-9904-4d0b-822a-15249aef80c7',
            'collection_name' => 'default',
            'name' => 'book8',
            'file_name' => '01J9R9PA98QQDXGDKJDT1P0G4M.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 197666,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 10:27:49',
            'updated_at' => '2024-10-09 10:27:49',
        ]);

        DB::table('media')->insert([
            'id' => 15,
            'model_type' => 'module',
            'model_id' => 23,
            'uuid' => '063a2ed3-b3c7-4fd0-a38b-acedcab27091',
            'collection_name' => 'default',
            'name' => 'book9',
            'file_name' => '01J9RKB6WFWA1T06E2T6293D5E.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 202333,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 13:16:31',
            'updated_at' => '2024-10-09 13:16:31',
        ]);

        DB::table('media')->insert([
            'id' => 16,
            'model_type' => 'module',
            'model_id' => 24,
            'uuid' => 'b89fbed2-d435-4615-b183-a169ffde1b95',
            'collection_name' => 'default',
            'name' => 'book10',
            'file_name' => '01J9RKBY7S5QZ1G4AVCBJXPCNG.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 215860,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 13:16:55',
            'updated_at' => '2024-10-09 13:16:55',
        ]);

        DB::table('media')->insert([
            'id' => 17,
            'model_type' => 'module',
            'model_id' => 25,
            'uuid' => 'a0553f51-0c55-4e17-9ef3-87197217911d',
            'collection_name' => 'default',
            'name' => 'book11',
            'file_name' => '01J9RKDBGXVZG2Y01E8NB71N5Z.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 228306,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 13:17:41',
            'updated_at' => '2024-10-09 13:17:41',
        ]);

        DB::table('media')->insert([
            'id' => 18,
            'model_type' => 'module',
            'model_id' => 26,
            'uuid' => '89d89952-72a6-4bdb-b8a2-0f28476aaeeb',
            'collection_name' => 'default',
            'name' => 'book12',
            'file_name' => '01J9RKE5XQ55CD3V6KVCR8WPZG.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 218468,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 13:18:08',
            'updated_at' => '2024-10-09 13:18:08',
        ]);

        DB::table('media')->insert([
            'id' => 20,
            'model_type' => 'course',
            'model_id' => 2,
            'uuid' => '7989b3e7-ac7c-4fd3-a807-c936b71c93f6',
            'collection_name' => 'default',
            'name' => '01J9RM6WM0DNXS82XAJ8X0K9NW-v1',
            'file_name' => '01J9RMN1C4S400ARWZY8R609JC.jpg',
            'mime_type' => 'image/jpeg',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 384122,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '{"cover":true}',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 13:39:22',
            'updated_at' => '2024-10-09 13:39:22',
        ]);

        DB::table('media')->insert([
            'id' => 21,
            'model_type' => 'module',
            'model_id' => 9,
            'uuid' => 'd0342e0b-19a6-4e37-bb5f-b5fdd44ce940',
            'collection_name' => 'default',
            'name' => 'book5',
            'file_name' => '01J9SMHEX1HJ2ZEPPNF0SXV5EN.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 193445,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 22:56:39',
            'updated_at' => '2024-10-09 22:56:39',
        ]);

        DB::table('media')->insert([
            'id' => 22,
            'model_type' => 'module',
            'model_id' => 10,
            'uuid' => '8d88f1d4-d446-4f87-81bc-52bff7dd8d85',
            'collection_name' => 'default',
            'name' => 'book6',
            'file_name' => '01J9SMJAX3DX8HVTDZ7ENW2ETN.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 190816,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 22:57:08',
            'updated_at' => '2024-10-09 22:57:08',
        ]);

        DB::table('media')->insert([
            'id' => 23,
            'model_type' => 'module',
            'model_id' => 11,
            'uuid' => '75d03d98-f8fc-46b8-8fe8-b08a4b9a8732',
            'collection_name' => 'default',
            'name' => 'book7',
            'file_name' => '01J9SMJZNGAVA6QCGZ19MWEEJA.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 202772,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 22:57:29',
            'updated_at' => '2024-10-09 22:57:29',
        ]);

        DB::table('media')->insert([
            'id' => 24,
            'model_type' => 'module',
            'model_id' => 12,
            'uuid' => '3b26ad15-b8c5-41b4-ac54-066e355c4e71',
            'collection_name' => 'default',
            'name' => 'book8',
            'file_name' => '01J9SMKGAYNM7ADV7ARCF6BE8V.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 197666,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 22:57:46',
            'updated_at' => '2024-10-09 22:57:46',
        ]);

        DB::table('media')->insert([
            'id' => 25,
            'model_type' => 'module',
            'model_id' => 13,
            'uuid' => '681143f5-07a3-46f7-b7b3-296d03c386e2',
            'collection_name' => 'default',
            'name' => 'book9',
            'file_name' => '01J9SMMX6RZ96CCMTC230BEH7P.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 202333,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 22:58:32',
            'updated_at' => '2024-10-09 22:58:32',
        ]);

        DB::table('media')->insert([
            'id' => 26,
            'model_type' => 'module',
            'model_id' => 14,
            'uuid' => 'c994525c-c5c7-48d4-852b-c103dba5fade',
            'collection_name' => 'default',
            'name' => 'book10',
            'file_name' => '01J9SMNS7GVEHHQ885V66N5HPJ.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 215860,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 22:59:01',
            'updated_at' => '2024-10-09 22:59:01',
        ]);

        DB::table('media')->insert([
            'id' => 27,
            'model_type' => 'module',
            'model_id' => 15,
            'uuid' => 'dfec2b45-2355-4dc0-89b6-65277bed805c',
            'collection_name' => 'default',
            'name' => 'book11',
            'file_name' => '01J9SMQ3RN9WNWBKTH9AW4J9HA.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 228306,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 22:59:44',
            'updated_at' => '2024-10-09 22:59:44',
        ]);

        DB::table('media')->insert([
            'id' => 28,
            'model_type' => 'module',
            'model_id' => 16,
            'uuid' => 'ea5cba90-e655-49a1-aba6-66f9ff0cdae9',
            'collection_name' => 'default',
            'name' => 'book12',
            'file_name' => '01J9SMQGJMVEZQJFF54TS97CG4.png',
            'mime_type' => 'image/png',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 218468,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2024-10-09 22:59:57',
            'updated_at' => '2024-10-09 22:59:57',
        ]);

        DB::table('media')->insert([
            'id' => 29,
            'model_type' => 'Modules\\Blog\\Models\\Post',
            'model_id' => 1,
            'uuid' => '1c780eb6-72f7-41e8-af33-0fa4a4f2ecba',
            'collection_name' => 'cover',
            'name' => '65d90f94c2512',
            'file_name' => '01JRRECY0PKQ5C06T8J88QBWVS.jpg',
            'mime_type' => 'image/jpeg',
            'disk' => 'public',
            'conversions_disk' => 'public',
            'size' => 235590,
            'manipulations' => '[]',
            'custom_properties' => '[]',
            'generated_conversions' => '[]',
            'responsive_images' => '[]',
            'order_column' => 1,
            'created_at' => '2025-04-13 20:45:32',
            'updated_at' => '2025-04-13 20:45:32',
        ]);

    }
}
