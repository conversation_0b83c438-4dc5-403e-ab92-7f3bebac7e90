<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ModulesSeeder extends Seeder
{
    public function run()
    {
        DB::table('modules')->insert([
            'id' => 1,
            'title' => '{"ar":"المستوى الأول (الكتاب الأول + الكتاب الثاني)","en":"Book No. 1"}',
            'slug' => 'alktab-alawal',
            'description' => '{"ar":" كتاب المعلم الأول","en":"Book no. 1"}',
            'visible' => 1,
            'course_id' => 1,
            'created_at' => '2024-08-09 18:01:39',
            'updated_at' => '2024-10-09 13:01:49',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 2,
            'title' => '{"ar":"المستوى الثانى  (الكتاب الأول + الكتاب الثاني)"}',
            'slug' => 'alktab-althan1',
            'description' => '{"ar":" كتاب المعلم الثانى\\n"}',
            'visible' => 1,
            'course_id' => 1,
            'created_at' => '2024-08-09 18:04:15',
            'updated_at' => '2024-10-09 13:02:33',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 3,
            'title' => '{"ar":"المستوى الثالث  (الكتاب الأول + الكتاب الثاني)"}',
            'slug' => 'alktab-althalth922',
            'description' => '{"ar":" كتاب المعلم الثالث"}',
            'visible' => 1,
            'course_id' => 1,
            'created_at' => '2024-08-09 18:05:13',
            'updated_at' => '2024-10-09 13:02:24',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 4,
            'title' => '{"ar":"المستوى الرابع  (الكتاب الأول + الكتاب الثاني)"}',
            'slug' => 'alktab-alrabee',
            'description' => '{"ar":" كتاب المعلم الرابع"}',
            'visible' => 1,
            'course_id' => 1,
            'created_at' => '2024-08-10 02:02:37',
            'updated_at' => '2024-10-09 13:02:18',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 5,
            'title' => '{"en":"الكتاب الأول","ar":"الكتاب الأول"}',
            'slug' => 'aalktab-alaol740',
            'description' => '{"en":"االكتاب الأول","ar":"االكتاب الأول"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-09-26 05:36:15',
            'updated_at' => '2025-01-29 22:46:42',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 6,
            'title' => '{"en":"الكتاب الثاني","ar":"الكتاب الثاني"}',
            'slug' => 'alktab-althan177',
            'description' => '{"en":"الكتاب الثانى","ar":"الكتاب الثانى"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-09-26 05:36:44',
            'updated_at' => '2024-09-26 05:40:24',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 7,
            'title' => '{"en":"الكتاب الثالث","ar":"الكتاب الثالث"}',
            'slug' => 'alktab-althalth278',
            'description' => '{"en":"الكتاب الثالث","ar":"الكتاب الثالث"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-09-26 05:39:22',
            'updated_at' => '2024-09-26 05:40:37',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 8,
            'title' => '{"en":"الكتاب الرابع","ar":"الكتاب الرابع"}',
            'slug' => 'alktab-alrabaa376',
            'description' => '{"en":"الكتاب الرابع","ar":"الكتاب الرابع"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-09-26 05:41:00',
            'updated_at' => '2024-09-26 05:41:11',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 9,
            'title' => '{"en":"الكتاب الخامس","ar":"الكتاب الخامس"}',
            'slug' => 'alktab-alkhams685',
            'description' => '{"en":"الكتاب الخامس","ar":"الكتاب الخامس"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-10-08 04:39:34',
            'updated_at' => '2024-10-08 04:42:02',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 10,
            'title' => '{"en":"الكتاب السادس","ar":"الكتاب السادس"}',
            'slug' => 'alktab-alsads113',
            'description' => '{"en":"الكتاب السادس","ar":"الكتاب السادس"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-10-08 04:39:55',
            'updated_at' => '2024-10-08 04:42:17',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 11,
            'title' => '{"en":"الكتاب السابع","ar":"الكتاب السابع"}',
            'slug' => 'alktab-alsabaa540',
            'description' => '{"en":"الكتاب السابع","ar":"الكتاب السابع"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-10-08 04:40:42',
            'updated_at' => '2024-10-08 04:42:29',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 12,
            'title' => '{"en":"الكتاب الثامن","ar":"الكتاب الثامن"}',
            'slug' => 'alktab-althamn216',
            'description' => '{"en":"الكتاب الثامن","ar":"الكتاب الثامن"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-10-08 04:41:05',
            'updated_at' => '2024-10-08 04:42:40',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 13,
            'title' => '{"ar":"الكتاب التاسع"}',
            'slug' => 'alktab-altasaa317',
            'description' => '{"ar":"الكتاب التاسع"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-10-09 13:16:31',
            'updated_at' => '2024-10-09 13:16:31',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 14,
            'title' => '{"en":"الكتاب العاشر","ar":"الكتاب العاشر"}',
            'slug' => 'alktab-alaaashr517',
            'description' => '{"en":"الكتاب العاشر","ar":"الكتاب العاشر"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-10-09 13:16:55',
            'updated_at' => '2024-10-09 22:59:01',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 15,
            'title' => '{"en":"الكتاب الحادي عشر","ar":"الكتاب الحادي عشر"}',
            'slug' => 'alktab-al746',
            'description' => '{"en":"الكتاب الحادي عشر","ar":"الكتاب الحادي عشر"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-10-09 13:17:41',
            'updated_at' => '2024-10-09 13:19:19',
            'sort' => 0,
        ]);

        DB::table('modules')->insert([
            'id' => 16,
            'title' => '{"en":"الكتاب الثاني عشر","ar":"الكتاب الثاني عشر"}',
            'slug' => 'alktab-althany-aashr563',
            'description' => '{"en":"الكتاب الثاني عشر","ar":"الكتاب الثاني عشر"}',
            'visible' => 1,
            'course_id' => 2,
            'created_at' => '2024-10-09 13:18:08',
            'updated_at' => '2024-10-09 13:19:29',
            'sort' => 0,
        ]);

    }
}
