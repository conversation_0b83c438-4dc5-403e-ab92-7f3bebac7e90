<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ExportTableSeeder extends Seeder
{
    /**
     * Export existing table data to a seeder file
     */
    public function exportTable($tableName)
    {
        // Get all data from the table
        $tableData = DB::table($tableName)->get();

        // Generate the seeder content
        $seederContent = "<?php\n\n";
        $seederContent .= "namespace Database\Seeders;\n\n";
        $seederContent .= "use Illuminate\Database\Seeder;\n";
        $seederContent .= "use Illuminate\Support\Facades\DB;\n\n";
        $seederContent .= 'class '.Str::studly($tableName)."Seeder extends Seeder\n";
        $seederContent .= "{\n";
        $seederContent .= "    public function run()\n";
        $seederContent .= "    {\n";

        foreach ($tableData as $row) {
            $data = get_object_vars($row);
            $seederContent .= "        DB::table('".$tableName."')->insert([\n";

            foreach ($data as $column => $value) {
                if (is_null($value)) {
                    $seederContent .= "            '".$column."' => null,\n";
                } elseif (is_numeric($value)) {
                    $seederContent .= "            '".$column."' => ".$value.",\n";
                } elseif ($this->isJson($value)) {
                    $seederContent .= "            '".$column."' => ".json_encode($value, JSON_UNESCAPED_UNICODE).",\n";
                } else {
                    $seederContent .= "            '".$column."' => '".addslashes($value)."',\n";
                }
            }

            $seederContent .= "        ]);\n\n";
        }

        $seederContent .= "    }\n";
        $seederContent .= "}\n";

        // Create the seeder file
        $fileName = database_path('seeders/'.Str::studly($tableName).'Seeder.php');
        file_put_contents($fileName, $seederContent);

        return 'Seeder created successfully: '.$fileName;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Example usage:
        $this->exportTable('audio');
        $this->exportTable('videos');
        $this->exportTable('courses');
        $this->exportTable('lessons');
        $this->exportTable('materials');
        $this->exportTable('media');
        $this->exportTable('modules');
        $this->exportTable('pdfs');
        $this->exportTable('pdf_pages');
        $this->exportTable('units');
        $this->exportTable('users');
        $this->exportTable('blog_posts');
        $this->exportTable('blog_categories');
        $this->exportTable('navigations');
    }

    public function isJson($string)
    {
        json_decode($string);

        return json_last_error() === JSON_ERROR_NONE;
    }
}
