<?php

namespace Modules\Billing\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\Billing\Models\Invoice;

class InvoiceNotification extends Notification
{
    use Queueable;

    protected Invoice $invoice;

    protected string $pdfPath;

    /**
     * Create a new notification instance.
     */
    public function __construct(Invoice $invoice, string $pdfPath)
    {
        $this->invoice = $invoice;
        $this->pdfPath = $pdfPath;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $dueDate = $this->invoice->due_date->format('F j, Y');
        $amount = $this->invoice->currency.' '.number_format($this->invoice->total_amount, 2);

        return (new MailMessage)
            ->subject('Invoice #'.$this->invoice->invoice_number)
            ->greeting('Hello '.$notifiable->name.',')
            ->line('Thank you for your business. Your invoice has been created.')
            ->line('Invoice #: '.$this->invoice->invoice_number)
            ->line('Amount: '.$amount)
            ->line('Due Date: '.$dueDate)
            ->action('View Invoice Online', route('billing.invoices.pdf', ['invoice' => $this->invoice->id]))
            ->line('You can find your invoice attached to this email.')
            ->attach($this->pdfPath, [
                'as' => 'invoice_'.$this->invoice->invoice_number.'.pdf',
                'mime' => 'application/pdf',
            ]);
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'invoice_id' => $this->invoice->id,
            'invoice_number' => $this->invoice->invoice_number,
            'amount' => $this->invoice->total_amount,
            'due_date' => $this->invoice->due_date->format('Y-m-d'),
        ];
    }
}
