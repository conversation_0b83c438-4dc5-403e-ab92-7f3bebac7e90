<?php

namespace Modules\Billing\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Transaction extends Model
{
    use HasFactory;

    // Transaction statuses
    const STATUS_PENDING = 'pending';

    const STATUS_COMPLETED = 'completed';

    const STATUS_FAILED = 'failed';

    const STATUS_REFUNDED = 'refunded';

    const STATUS_CANCELLED = 'cancelled';

    protected $fillable = [
        'user_id',
        'payable_id',
        'payable_type',
        'amount',
        'paid_amount',
        'currency',
        'reference',
        'gateway',
        'gateway_response',
        'status',
        'completed_at',
        'meta',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'completed_at' => 'datetime',
        'meta' => 'array',
    ];

    /**
     * Get the related item (order or invoice)
     */
    public function payable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user associated with this transaction
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for failed transactions
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * Scope for refunded transactions
     */
    public function scopeRefunded($query)
    {
        return $query->where('status', self::STATUS_REFUNDED);
    }

    /**
     * Mark the transaction as completed
     */
    public function markCompleted(): self
    {
        $this->status = self::STATUS_COMPLETED;
        $this->completed_at = now();
        $this->save();

        return $this;
    }

    /**
     * Mark the transaction as failed
     */
    public function markFailed($reason = null): self
    {
        $this->status = self::STATUS_FAILED;

        if ($reason) {
            $meta = $this->meta ?? [];
            $meta['failure_reason'] = $reason;
            $this->meta = $meta;
        }

        $this->save();

        return $this;
    }
}
