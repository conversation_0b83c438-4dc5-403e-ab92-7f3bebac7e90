<?php

namespace Modules\Billing\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class InvoiceItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'name',
        'description',
        'price',
        'quantity',
        'product_id',
        'product_type',
        'meta',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'quantity' => 'integer',
        'meta' => 'array',
    ];

    /**
     * Get the invoice this item belongs to
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the product this invoice item is for
     */
    public function product(): MorphTo
    {
        return $this->morphTo('product');
    }

    /**
     * Get the subtotal for this item (price × quantity)
     */
    public function getSubtotalAttribute()
    {
        return $this->price * $this->quantity;
    }
}
