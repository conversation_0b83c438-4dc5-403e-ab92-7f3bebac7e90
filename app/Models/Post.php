<?php

namespace Modules\Blog\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Tags\HasTags;

class Post extends Model implements HasMedia
{
    use HasTags, InteractsWithMedia;

    protected $table = 'blog_posts';

    protected $fillable = [
        'title',
        'slug',
        'content',
        'is_published',
        'blog_category_id',
        'is_featured',
    ];

    protected $casts = [
        'content' => 'array',
        'is_published' => 'boolean',
    ];

    public function getBlocksAttribute()
    {
        return json_decode(
            collect($this->content ?? [])->toJson()
        );
    }

    public function excerpt(): string
    {
        $content = $this->content; // Assuming $this->content contains the HTML content
        if (empty($content)) {
            return '';
        }

        $dom = new \DOMDocument();
        libxml_use_internal_errors(true); // Suppress warnings for invalid HTML
        $dom->loadHTML($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        libxml_clear_errors();

        $paragraphs = $dom->getElementsByTagName('p');
        if ($paragraphs->length > 0) {
            return trim($paragraphs->item(0)->textContent);
        }

        return '';

    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeDrafts($query)
    {
        return $query->where('is_published', false);
    }

    public function categories(): BelongsTo
    {
        // Return the post category using the belongsTo relationship
        return $this->belongsTo(Category::class, 'blog_category_id');
    }
}
