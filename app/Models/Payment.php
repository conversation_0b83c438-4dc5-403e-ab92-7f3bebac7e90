<?php

namespace Modules\Payment\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Payment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reference',
        'amount',
        'currency',
        'gateway',
        'gateway_payment_id',
        'gateway_data',
        'status',
        'payable_type',
        'payable_id',
        'customer_email',
        'customer_name',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'gateway_data' => 'array',
        'metadata' => 'array',
    ];

    /**
     * The possible payment statuses.
     */
    const STATUS_PENDING = 'pending';

    const STATUS_PROCESSING = 'processing';

    const STATUS_COMPLETED = 'completed';

    const STATUS_FAILED = 'failed';

    const STATUS_CANCELLED = 'cancelled';

    const STATUS_REFUNDED = 'refunded';

    protected static function booted(): void
    {
        static::updated(function (Payment $payment) {
            // Check if payment status was changed to completed
            if ($payment->status === Payment::STATUS_COMPLETED &&
                $payment->getOriginal('status') !== Payment::STATUS_COMPLETED) {

                // Get the related model
                $payableModel = $payment->payable;

                if ($payableModel) {
                    // Call the handler method
                    $payableModel->handlePaymentCompleted($payment);
                }
            }
            // Check if the payment status was changed to 'failed'
            if ($payment->status === Payment::STATUS_FAILED &&
                $payment->getOriginal('status') !== Payment::STATUS_FAILED) {

                // Get the related model
                $payableModel = $payment->payable;

                if ($payableModel) {
                    // Get the failure reason
                    $reason = $payment->metadata['error_message'] ?? 'Unknown error';
                    // Call the failure handler method
                    $payableModel->handlePaymentFailed($payment, $reason);
                }
            }

        });
    }

    /**
     * Get the payable entity that the payment belongs to.
     */
    public function payable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the refunds for this payment.
     */
    public function refunds(): HasMany
    {
        return $this->hasMany(PaymentRefund::class);
    }

    /**
     * Scope a query to only include payments with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Check if payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if payment is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * Check if payment is refunded.
     */
    public function isRefunded(): bool
    {
        return $this->status === self::STATUS_REFUNDED;
    }

    /**
     * Get the table associated with the model.
     *
     * @return string
     */
    public function getTable()
    {
        return 'payments';
    }
}
