<?php

namespace Modules\Billing\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'order_id',
        'invoice_number',
        'amount',
        'tax_amount',
        'total_amount',
        'status',
        'due_date',
        'paid_at',
        'currency',
        'notes',
        'billing_address',
        'meta',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'due_date' => 'date',
        'paid_at' => 'datetime',
        'billing_address' => 'array',
        'meta' => 'array',
    ];

    const STATUS_DRAFT = 'draft';

    const STATUS_PENDING = 'pending';

    const STATUS_PAID = 'paid';

    const STATUS_OVERDUE = 'overdue';

    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get the user that owns the invoice
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order associated with this invoice
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the invoice items
     */
    public function items(): HasMany
    {
        return $this->hasMany(InvoiceItem::class);
    }

    /**
     * Get the transactions for this invoice
     */
    public function transactions()
    {
        return $this->morphMany(Transaction::class, 'payable');
    }

    /**
     * Generate an invoice number
     */
    public static function generateInvoiceNumber(): string
    {
        $prefix = 'INV-';
        $year = date('Y');
        $month = date('m');

        $lastInvoice = self::where('invoice_number', 'like', "{$prefix}{$year}{$month}%")
            ->orderBy('id', 'desc')
            ->first();

        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix.$year.$month.str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate totals
     */
    public function calculateTotals()
    {
        $amount = $this->items->sum(function ($item) {
            return $item->price * $item->quantity;
        });

        $this->amount = $amount;
        $this->total_amount = $amount + ($this->tax_amount ?? 0);
        $this->save();

        return $this;
    }

    /**
     * Create an invoice from an order
     */
    public static function createFromOrder(Order $order): Invoice
    {
        // Create the invoice
        $invoice = self::create([
            'user_id' => $order->user_id,
            'order_id' => $order->id,
            'invoice_number' => self::generateInvoiceNumber(),
            'amount' => $order->subtotal,
            'tax_amount' => $order->tax,
            'total_amount' => $order->total,
            'status' => $order->isPaid() ? self::STATUS_PAID : self::STATUS_PENDING,
            'due_date' => now()->addDays(config('billing.invoice_due_days', 7)),
            'paid_at' => $order->isPaid() ? $order->paid_at : null,
            'currency' => $order->currency,
            'billing_address' => $order->billing_address,
        ]);

        // Create invoice items from order items
        foreach ($order->items as $orderItem) {
            $invoice->items()->create([
                'name' => $orderItem->name,
                'description' => '',
                'price' => $orderItem->price,
                'quantity' => $orderItem->quantity,
                'product_id' => $orderItem->product_id,
                'product_type' => $orderItem->product_type,
            ]);
        }

        return $invoice;
    }

    /**
     * Mark invoice as paid
     */
    public function markAsPaid($transactionReference = null, $paymentMethod = null)
    {
        $this->status = self::STATUS_PAID;
        $this->paid_at = now();

        $meta = $this->meta ?? [];

        if ($transactionReference) {
            $meta['transaction_reference'] = $transactionReference;
        }

        if ($paymentMethod) {
            $meta['payment_method'] = $paymentMethod;
        }

        $this->meta = $meta;
        $this->save();

        return $this;
    }

    /**
     * Get the full invoice PDF file path
     */
    public function getPdfPath()
    {
        $directory = storage_path('app/invoices');

        if (! file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        return $directory.'/invoice_'.$this->invoice_number.'.pdf';
    }

    /**
     * Generate PDF for this invoice
     */
    public function generatePdf()
    {
        // Using Laravel's PDF generation capabilities
        // This is a placeholder - implement with your preferred PDF package

        $data = [
            'invoice' => $this,
            'user' => $this->user,
            'items' => $this->items,
            'company' => [
                'name' => config('app.name'),
                'address' => config('billing.company_address'),
                'phone' => config('billing.company_phone'),
                'email' => config('billing.company_email'),
                'logo' => config('billing.company_logo'),
            ],
        ];

        // Example with a generic PDF facade
        // PDF::loadView('billing::invoices.pdf', $data)
        //    ->save($this->getPdfPath());

        return $this->getPdfPath();
    }
}
