<?php

namespace Modules\Billing\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'name',
        'price',
        'quantity',
        'product_id',
        'product_type',
        'options',
        'meta',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'quantity' => 'integer',
        'options' => 'array',
        'meta' => 'array',
    ];

    /**
     * Get the order this item belongs to
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the actual product this item represents
     */
    public function product(): MorphTo
    {
        return $this->morphTo('product');
    }

    /**
     * Get the subtotal for this item (price × quantity)
     */
    public function getSubtotalAttribute()
    {
        return $this->price * $this->quantity;
    }

    /**
     * Update the quantity of this item
     */
    public function updateQuantity(int $quantity)
    {
        // Don't allow negative quantities
        if ($quantity < 0) {
            $quantity = 0;
        }

        $this->quantity = $quantity;
        $this->save();

        // Recalculate the order totals
        $this->order->calculateTotals();

        return $this;
    }
}
