<?php

namespace Modules\Billing\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Billing\Events\OrderCancelled;
use Modules\Billing\Events\OrderCompleted;
use Modules\Billing\Events\OrderPaid;
use Modules\Billing\Notifications\OrderPaidNotification;
use Modules\Payment\Models\Payment;
use Modules\Payment\Traits\HasPayments;

class Order extends Model
{
    use HasPayments;

    protected $fillable = [
        'orderable_id',
        'orderable_type',
        'user_id',
        'order_number',
        'currency',
        'subtotal',
        'discount_total',
        'tax',
        'total',
        'note',
        'payment_gateway',
        'status',
        'paid_at',
        'billing_address',
        'shipping_address',
        'meta',
    ];

    protected $casts = [
        'status' => \Modules\Billing\Enums\OrderStatus::class,
        'paid_at' => 'datetime',
        'subtotal' => 'decimal:2',
        'discount_total' => 'decimal:2',
        'tax' => 'decimal:2',
        'total' => 'decimal:2',
        'billing_address' => 'array',
        'shipping_address' => 'array',
        'meta' => 'array',
    ];

    protected $observables = [
        'confirmed',
        'failed',
        'cancelled',
        'refunded',
    ];

    /**
     * Get the orderable entity (course, module, product, subscription)
     */
    public function orderable()
    {
        return $this->morphTo();
    }

    /**
     * Set the order status and trigger appropriate events
     */
    public function setStatus($status): void
    {
        $oldStatus = $this->status;

        // Update the status
        if ($status instanceof \Modules\Billing\Enums\OrderStatus) {
            $this->status = $status;
        } else {
            $this->status = match ($status) {
                'pending' => \Modules\Billing\Enums\OrderStatus::PENDING,
                'processing' => \Modules\Billing\Enums\OrderStatus::PROCESSING,
                'completed' => \Modules\Billing\Enums\OrderStatus::COMPLETED,
                'cancelled' => \Modules\Billing\Enums\OrderStatus::CANCELLED,
                'refunded' => \Modules\Billing\Enums\OrderStatus::REFUNDED,
                default => $this->status,
            };
        }

        if ($oldStatus != $this->status) {
            $this->save();
        }

        // Fire model events based on the status
        match ($status) {
            'confirmed', 'completed', \Modules\Billing\Enums\OrderStatus::COMPLETED => $this->fireModelEvent('confirmed'),
            'failed' => $this->fireModelEvent('failed'),
            'cancelled', \Modules\Billing\Enums\OrderStatus::CANCELLED => $this->fireModelEvent('cancelled'),
            'refunded', \Modules\Billing\Enums\OrderStatus::REFUNDED => $this->fireModelEvent('refunded'),
            default => null,
        };
    }

    /**
     * Get active/pending orders
     */
    public function scopeActive($query)
    {
        return $query->with('user')
            ->where('status', \Modules\Billing\Enums\OrderStatus::PENDING->value);
    }

    /**
     * Get completed orders
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', \Modules\Billing\Enums\OrderStatus::COMPLETED->value);
    }

    /**
     * Relationship to the customer
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order's line items
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the order's transactions
     */
    public function transactions()
    {
        return $this->morphMany(Transaction::class, 'payable');
    }

    /**
     * Generate a unique order number
     */
    public static function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'ORD-'.strtoupper(uniqid());
        } while (self::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Calculate totals for the order
     */
    public function calculateTotals()
    {
        // Calculate subtotal from items
        $subtotal = $this->items->sum(function ($item) {
            return $item->price * $item->quantity;
        });

        $this->subtotal = $subtotal;
        $this->total = $subtotal - ($this->discount_total ?? 0) + ($this->tax ?? 0);
        $this->save();

        return $this;
    }

    /**
     * Create an order for a single item
     */
    public static function createOrder(User $user, $item, $options = []): Order
    {
        $order = self::create([
            'user_id' => $user->id,
            'orderable_type' => get_class($item),
            'orderable_id' => $item->id,
            'order_number' => self::generateOrderNumber(),
            'currency' => $options['currency'] ?? config('billing.default_currency', 'USD'),
            'subtotal' => $item->price ?? 0,
            'discount_total' => $options['discount'] ?? 0,
            'tax' => $options['tax'] ?? 0,
            'total' => ($item->price ?? 0) - ($options['discount'] ?? 0) + ($options['tax'] ?? 0),
            'status' => \Modules\Billing\Enums\OrderStatus::PENDING,
            'note' => $options['note'] ?? 'Order created for '.($item->title ?? $item->name ?? 'product'),
        ]);

        // Create an order item
        $order->items()->create([
            'name' => $item->title ?? $item->name ?? 'Product',
            'price' => $item->price ?? 0,
            'quantity' => 1,
            'product_id' => $item->id,
            'product_type' => get_class($item),
        ]);

        return $order;
    }

    /**
     * Create a complete order with multiple items
     */
    public static function createCompleteOrder(User $user, array $items, array $options = []): Order
    {
        $order = self::create([
            'user_id' => $user->id,
            'order_number' => self::generateOrderNumber(),
            'currency' => $options['currency'] ?? config('billing.default_currency', 'USD'),
            'subtotal' => 0, // Will be calculated from items
            'discount_total' => $options['discount'] ?? 0,
            'tax' => $options['tax'] ?? 0,
            'total' => 0, // Will be calculated from items
            'status' => \Modules\Billing\Enums\OrderStatus::PENDING,
            'note' => $options['note'] ?? null,
            'billing_address' => $options['billing_address'] ?? null,
            'shipping_address' => $options['shipping_address'] ?? null,
        ]);

        // Create order items
        foreach ($items as $item) {
            $product = $item['product'];
            $quantity = $item['quantity'] ?? 1;
            $price = $item['price'] ?? $product->price ?? 0;

            $order->items()->create([
                'name' => $product->title ?? $product->name ?? 'Product',
                'price' => $price,
                'quantity' => $quantity,
                'product_id' => $product->id,
                'product_type' => get_class($product),
            ]);
        }

        // Calculate the totals based on items
        $order->calculateTotals();

        return $order;
    }

    /**
     * Add a new item to the order
     */
    public function addItem($product, $quantity = 1, $price = null)
    {
        $price = $price ?? $product->price ?? 0;

        $this->items()->create([
            'name' => $product->title ?? $product->name ?? 'Product',
            'price' => $price,
            'quantity' => $quantity,
            'product_id' => $product->id,
            'product_type' => get_class($product),
        ]);

        $this->calculateTotals();

        return $this;
    }

    /**
     * Handle payment completion for an order
     */
    public function handlePaymentCompleted(Payment $payment): void
    {
        // Update order status
        $this->status = \Modules\Billing\Enums\OrderStatus::COMPLETED;
        $this->paid_at = now();
        $this->payment_gateway = $payment->gateway ?? $payment->provider ?? null;
        $this->save();

        // Process inventory for products if needed
        $this->processInventory();

        // Create transaction record
        $this->transactions()->create([
            'user_id' => $this->user_id,
            'amount' => $payment->amount,
            'reference' => $payment->reference ?? $payment->id,
            'gateway' => $payment->gateway ?? $payment->provider ?? 'unknown',
            'status' => Transaction::STATUS_COMPLETED,
            'completed_at' => now(),
        ]);

        // Trigger order completion process
        event(new OrderPaid($this));
        event(new OrderCompleted($this));

        // Send notification
        if (method_exists($this, 'generateInvoice')) {
            $invoice = $this->generateInvoice();
            $this->user->notify(new OrderPaidNotification($this, $invoice));
            //        } else {
            //            $this->user->notify(new OrderPaidNotification($this));
        }
    }

    /**
     * Process inventory for physical products
     */
    protected function processInventory()
    {
        // Decrease inventory for ordered items that are physical products
        foreach ($this->items as $item) {
            $product = $item->product;

            // Check if product has a decreaseStock method
            if ($product && method_exists($product, 'decreaseStock')) {
                $product->decreaseStock($item->quantity);
            }
        }
    }

    /**
     * Cancel an order
     */
    public function cancel($reason = null)
    {
        $this->status = \Modules\Billing\Enums\OrderStatus::CANCELLED;

        if ($reason) {
            $meta = $this->meta ?? [];
            $meta['cancellation_reason'] = $reason;
            $meta['cancelled_at'] = now()->toDateTimeString();
            $this->meta = $meta;
        }

        $this->save();

        event(new OrderCancelled($this));

        return $this;
    }

    /**
     * Check if the order has been paid
     */
    public function isPaid(): bool
    {
        return $this->paid_at !== null || $this->status === \Modules\Billing\Enums\OrderStatus::COMPLETED;
    }
}
