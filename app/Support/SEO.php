<?php

namespace Modules\Core\Support;

use Filament\Forms;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class SEO
{
    public static function make(array $only = ['title', 'author', 'description']): array
    {
        return [Forms\Components\Section::make(
            Arr::only([
                'title' => Forms\Components\TextInput::make('title')

                    ->columnSpan(2),
                'author' => Forms\Components\TextInput::make('author')

                    ->columnSpan(2),
                'tags' => Forms\Components\TagsInput::make('tags')

                    ->columnSpan(2),
                'description' => Forms\Components\Textarea::make('description')

                    ->helperText(function (?string $state): string {
                        return (string) Str::of(strlen($state))
                            ->append(' / ')
                            ->append(160 .' ')
                            ->append(Str::of(__('Characters'))->lower());
                    })
                    ->reactive()
                    ->columnSpan(2),
            ], $only)
        )
            ->afterStateHydrated(function (Forms\Components\Section $component, ?Model $record) use ($only): void {
                $component->getChildComponentContainer()->fill(
                    $record?->seo?->only($only) ?: []
                );
            })
            ->description('The SEO of the tour, If you leave empty, it will be generated automatically.')
            ->statePath('seo')
            ->dehydrated(false)
            ->saveRelationshipsUsing(function (Model $record, array $state) use ($only): void {
                $state = collect($state)->only($only)->map(fn ($value) => $value ?: null)->all();

                if ($record->seo && $record->seo->exists) {
                    $record->seo->update($state);
                } else {
                    $record->seo()->create($state);
                }
            })];
    }
}
