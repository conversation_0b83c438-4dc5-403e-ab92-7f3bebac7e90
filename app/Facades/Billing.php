<?php

namespace Modules\Billing\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \Modules\Billing\Models\Order createOrder(\App\Models\User $user, \Illuminate\Database\Eloquent\Model $product, array $options = [])
 * @method static \Modules\Billing\Models\Order createCompleteOrder(\App\Models\User $user, array $items, array $options = [])
 * @method static \Modules\Billing\Models\Order processPayment(\Modules\Billing\Models\Order $order, \Modules\Payment\Models\Payment $payment)
 * @method static \Modules\Billing\Models\Invoice generateInvoice(\Modules\Billing\Models\Order $order)
 * @method static \Modules\Billing\Models\Transaction recordTransaction(\App\Models\User $user, \Illuminate\Database\Eloquent\Model $payable, float $amount, string $status = 'completed', array $attributes = [])
 * @method static \Illuminate\Database\Eloquent\Collection getUserTransactions(\App\Models\User $user)
 * @method static \Illuminate\Database\Eloquent\Collection getUserOrders(\App\Models\User $user)
 * @method static \Illuminate\Database\Eloquent\Collection getUserInvoices(\App\Models\User $user)
 * @method static \Illuminate\Database\Eloquent\Collection getOrdersByProductType(string $productType)
 * @method static \Modules\Billing\Models\Order processRefund(\Modules\Billing\Models\Order $order, float $amount = null, string $reason = null)
 * @method static float calculateTax($subtotal, $country = null, $state = null)
 *
 * @see \Modules\Billing\Services\BillingService
 */
class Billing extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'billing';
    }
}
