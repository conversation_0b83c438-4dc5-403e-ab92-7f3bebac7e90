<?php

namespace Modules\Payment\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\Payment\Models\Payment;

class PaymentFailed
{
    use Dispatchable, SerializesModels;

    /**
     * The payment instance.
     *
     * @var Payment
     */
    public $payment;

    /**
     * The failure reason.
     *
     * @var string
     */
    public $reason;

    /**
     * Create a new event instance.
     */
    public function __construct(Payment $payment, string $reason = '')
    {
        $this->payment = $payment;
        $this->reason = $reason;
    }
}
