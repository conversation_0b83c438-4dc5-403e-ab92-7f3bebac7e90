<?php

namespace App\Livewire;

use App\Models\User;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Livewire\Attributes\Locked;
use Livewire\Component;
use Modules\Affiliate\Services\CouponService;
use Modules\Billing\Models\Order;
use Modules\Course\Models\Course;

class Checkout extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [
        'customer_notes' => '',
        'institute_id' => null,
        'course_id' => null,
        'user_id' => null,
    ];

    public ?Course $course = null;

    //    public float $tax = 0;

    public $couponCode = '';

    public $discount = 0;

    public $subtotal = 0;

    #[Locked]
    public ?float $totalPrice = 0;

    public function mount($course = null)
    {
        $this->course = $course;
        //        $this->tax = $this->course->price * 0.15;
        $this->subtotal = $this->course->price;
        $this->calculateTotal();
        $this->form->fill();
    }

    //    public function updated(string $statePath): void
    //    {
    //      //if this user is owner of institute todo
    //        if ($statePath === 'data.institute_id')
    //        {
    //        }
    //
    //    }

    public function calculateTotal()
    {
        $this->totalPrice = $this->subtotal - $this->discount;
    }

    /**
     * @throws \Exception
     */
    public function applyCoupon(): void
    {
        $this->validate([
            'couponCode' => 'required|min:3',
        ]);

        $result = (new CouponService)->processOrderWithCoupon($this->couponCode, $this->subtotal, auth()->id());

        //        $coupon = Coupon::where('code', $this->couponCode)
        //            ->where('expires_at', '>', now())
        //            ->where('is_active', true)
        //            ->with('affiliate')
        //            ->first();

        if (! $result) {
            $this->dispatch('toast', message: 'Invalid or expired coupon code.', data: [
                'position' => 'top-right', 'type' => 'danger',
            ]);
            //            $this->discount = 0;
        } else {
            // Calculate discount based on coupon type
            //            if ($coupon->type === 'percentage') {
            //                $this->discount = ($this->subtotal * $coupon->value) / 100;
            //            } else {
            //                $this->discount = $coupon->value;
            //            }

            $this->dispatch('toast', message: 'Coupon applied successfully.', data: [
                'position' => 'top-right', 'type' => 'success',
            ]);
            $this->totalPrice = $result['final_amount'];
            $this->discount = $result['discount_amount'];

        }

        //        $this->calculateTotal();
    }

    public function removeCoupon(): void
    {
        $this->couponCode = '';
        $this->discount = 0;
        $this->calculateTotal();

    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                Section::make(__('Customer Details'))
                    ->schema([

                        Textarea::make('customer_notes')
                            ->columnSpanFull()
                            ->default(''),
                    ]),

            ])
//            ->model($this->course)
            ->statePath('data');
    }

    public function create(): void
    {
        DB::transaction(function () {
            $user = auth()->user();

            $order = Order::create([
                'order_number' => rand(100000, 999999),
                'user_id' => $user->id,
                'orderable_id' => $this->course->id,
                'orderable_type' => Course::class,
                'subtotal' => $this->subtotal,
                'discount_total' => $this->discount,
                'total' => $this->totalPrice,
                'payment_gateway' => '2co',
                'tax' => 0,
                'note' => $this->data['customer_notes'],
            ]);

            $order->transactions()->create([
                'reference' => Str::random(10),
                'user_id' => $user->id,
                'amount' => $this->totalPrice,
                'gateway_name' => '2co',
                'description' => 'Course Payment',
                'status' => 'pending',
            ]);

            Notification::make()->title('New Booking')->body('You have a new course booking.')->success()->sendToDatabase(User::find(1));
            session()->flash('message', 'Booking successfully created, we will get back to you shortly.');
            $this->dispatch('toast', message: 'successfully created.', data: [
                'position' => 'top-right', 'type' => 'info',
            ]);

            // TODO: Gateways based on Countries

            $this->redirect(url(path: 'payment/capture/2co-card/'.$order->order_number));

        });

    }

    public function render(): \Illuminate\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|\Illuminate\View\View
    {
        return view('livewire.checkout');
    }
}
