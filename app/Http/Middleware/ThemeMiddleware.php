<?php

namespace Modules\Theme\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ThemeMiddleware
{
    public function handle(Request $request, Closure $next, $theme = null)
    {
        if ($request->expectsJson() || app()->runningInConsole()) {
            return $next($request);
        }
        if (! is_null($theme)) {
            \Theme::init($theme);
        } else {
            \Theme::init('default');
        }

        return $next($request);
    }
}
