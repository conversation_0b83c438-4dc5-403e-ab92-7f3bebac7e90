<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SingleUseSignedRouteMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $signature = $request->query('signature');

        if (Cache::has("used_signature:$signature")) {
            abort(403, 'This link has already been used.');
        }

        Cache::put("used_signature:$signature", true, now()->addDay());

        return $next($request);
    }
}
