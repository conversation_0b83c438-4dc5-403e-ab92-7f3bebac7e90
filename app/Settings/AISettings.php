<?php

namespace Modules\AI\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class AISettings extends Settings
{
    public ?string $default_ai_service = 'groq';

    public ?string $openai_api_key;

    public ?string $groq_api_key;

    public ?string $geminai_api_key;

    public ?string $perplexai_api_key;

    public ?string $together_api_key;

    public static function encrypted(): array
    {
        return [
            'openai_api_key',
            'groq_api_key',
            'geminai_api_key',
            'perplexai_api_key',
            'together_api_key',
        ];
    }

    public static function group(): string
    {
        return 'module-ai';
    }
}
