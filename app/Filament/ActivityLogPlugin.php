<?php

namespace Modules\ActivityLogs\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class ActivityLogPlugin implements Plugin
{
    public function getId(): string
    {
        return 'activity-logs';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverResources(
            in: __DIR__.'/Resources',
            for: 'Modules\\ActivityLogs\\Filament\\Resources'
        );
    }

    public function boot(Panel $panel): void {}
}
