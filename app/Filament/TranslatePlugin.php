<?php

namespace Modules\Translate\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;
use Filament\SpatieLaravelTranslatablePlugin;
use Modules\Setting\Settings\GeneralSettings;

class TranslatePlugin implements Plugin
{
    public function getId(): string
    {
        return 'translate';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverResources(
            in: __DIR__.'/Resources',
            for: 'Modules\\Translate\\Filament\\Resources'
        );
        $panel->discoverPages(
            in: __DIR__.'/Pages',
            for: 'Modules\\Translate\\Filament\\Pages'
        );

        $panel->bootUsing(fn () => $panel->plugin(
            SpatieLaravelTranslatablePlugin::make()
                ->defaultLocales(app(GeneralSettings::class)->site_languages)
        ));
    }

    public function boot(Panel $panel): void {}
}
