<?php

namespace Modules\Setting\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;
use Filament\SpatieLaravelTranslatablePlugin;

class SettingPlugin implements Plugin
{
    public function getId(): string
    {
        return 'setting';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverPages(__DIR__.'/Pages', 'Modules\\Setting\\Filament\\Pages');
        $panel->plugin(SpatieLaravelTranslatablePlugin::make());
    }

    public function boot(Panel $panel): void {}
}
