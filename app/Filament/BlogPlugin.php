<?php

namespace Modules\Blog\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class BlogPlugin implements Plugin
{
    public function getId(): string
    {
        return 'blog';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverResources(
            in: __DIR__.'/Resources',
            for: 'Modules\\Blog\\Filament\\Resources'
        );

    }

    public function boot(Panel $panel): void {}
}
