<?php

namespace Modules\AI\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class AIPlugin implements Plugin
{
    public function getId(): string
    {
        return 'ai';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverPages(
            in: __DIR__.'/Pages',
            for: 'Modules\\AI\\Filament\\Pages'
        );

    }

    public function boot(Panel $panel): void {}
}
