<?php

namespace Modules\Setting\Filament\Pages;

use Filament\Forms;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\Core\Forms\Components\Picker;
use Modules\Core\Models\Localization\Currency;
use Modules\Core\Models\Localization\Language;
use Modules\Setting\Settings\GeneralSettings as General;
use Nwidart\Modules\Facades\Module;

class GeneralSettings extends SettingsPage
{
    public static function getNavigationGroup(): ?string
    {
        return __('Settings');
    }

    /**
     * @return string|null
     */
    public static function getNavigationLabel(): string
    {
        return __('General Settings');
    }

    protected static string $settings = General::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->columnSpanFull()
                    ->tabs([
                        Tabs\Tab::make('Basics')
                            ->schema([
                                Forms\Components\Section::make(__('General Settings'))
                                    ->aside()
                                    ->columns(1)
                                    ->schema([
                                        TextInput::make('site_name')
                                            ->label(__('Site Name'))
                                            ->required(),

                                        Picker::make('site_theme')
                                            ->inlineLabel()
                                            ->label(__('Site Theme'))
                                            ->options(\Theme::getSelectThemesOptions('name'))
                                            ->imageSize(100)
                                            ->images(\Theme::getSelectThemesOptions())
                                            ->default('ship'),

                                        Forms\Components\FileUpload::make('site_logo')
                                            ->label(__('Site Logo'))
                                            ->required(),

                                        Forms\Components\FileUpload::make('site_favicon')
                                            ->label(__('Site Favicon'))
                                            ->required(),

                                        Forms\Components\ColorPicker::make('site_color')
                                            ->label(__('Site Color'))
                                            ->required(),

                                        Forms\Components\Select::make('currency')
                                            ->label(__('Currency'))
                                            ->hint(__('When changed will update the prices on the site based on base Currency'))
                                            ->options(fn () => Currency::pluck('name', 'iso')->toArray())
                                            ->searchable()
                                            ->required(),

                                        Forms\Components\Placeholder::make('base_currency')
                                            ->label(__('Base Currency: ').config('localization.base_currency')),

                                        Forms\Components\Select::make('site_languages')
                                            ->label(__('Site Languages'))
                                            ->options(fn () => Language::activated()->pluck('name', 'iso')->toArray())
                                            ->multiple()
                                            ->required(),

                                        Forms\Components\Select::make('site_locale')
                                            ->label(__('Default Language'))
                                            ->options(fn () => Language::activated()->pluck('name', 'iso')->toArray())
                                            ->required(),

                                        Forms\Components\ToggleButtons::make('site_active')
                                            ->label(__('Site Status'))
                                            ->inlineLabel()
                                            ->helperText(__('Enable or disable the site'))
                                            ->boolean(),

                                        TextInput::make('admin_prefix')
                                            ->label(__('Admin URL Prefix'))
                                            ->placeholder('admin')
                                            ->helperText(__('The URL prefix for the admin panel (default: admin)')),
                                    ]),
                            ]),

                        Tabs\Tab::make('Content')
                            ->schema([
                                Forms\Components\Section::make(__('Content Elements'))
                                    ->aside()
                                    ->columns(1)
                                    ->schema([
                                        Forms\Components\Repeater::make('slider')
                                            ->label(__('Slider Items'))
                                            ->collapsed()
                                            ->labelBetweenItems(__('Slider'))
                                            ->schema([
                                                Forms\Components\FileUpload::make('image')
                                                    ->label(__('Image')),
                                                TextInput::make('headline')
                                                    ->label(__('Headline')),
                                                TextInput::make('sub_headline')
                                                    ->label(__('Sub Headline')),
                                            ]),

                                        Forms\Components\Repeater::make('faqs')
                                            ->label(__('FAQs'))
                                            ->collapsed()
                                            ->labelBetweenItems(__('FAQ'))
                                            ->schema([
                                                TextInput::make('question')
                                                    ->label(__('Question')),
                                                TextInput::make('answer')
                                                    ->label(__('Answer')),
                                            ]),

                                        Forms\Components\Repeater::make('brands')
                                            ->label(__('Brands'))
                                            ->collapsed()
                                            ->labelBetweenItems(__('Brand'))
                                            ->schema([
                                                TextInput::make('name')
                                                    ->label(__('Name')),
                                                TextInput::make('url')
                                                    ->label(__('URL')),
                                                Forms\Components\FileUpload::make('logo')
                                                    ->label(__('Logo')),
                                            ]),

                                        Forms\Components\Repeater::make('testimonials')
                                            ->label(__('Testimonials'))
                                            ->collapsed()
                                            ->labelBetweenItems(__('Testimonial'))
                                            ->schema([
                                                Forms\Components\Textarea::make('body')
                                                    ->label(__('Content'))
                                                    ->required(),
                                                TextInput::make('who')
                                                    ->label(__('Person')),
                                                TextInput::make('company')
                                                    ->label(__('Company')),
                                            ]),
                                    ]),
                            ]),

                        Tabs\Tab::make('Email Settings')
                            ->schema([
                                Forms\Components\Section::make(__('SMTP Configuration'))
                                    ->aside()
                                    ->columns(1)
                                    ->schema([
                                        TextInput::make('smtp_host')
                                            ->label(__('SMTP Host')),

                                        TextInput::make('smtp_port')
                                            ->label(__('SMTP Port')),

                                        TextInput::make('smtp_username')
                                            ->label(__('SMTP Username')),

                                        TextInput::make('smtp_password')
                                            ->label(__('SMTP Password'))
                                            ->password(),

                                        Forms\Components\Select::make('smtp_encryption')
                                            ->label(__('SMTP Encryption'))
                                            ->options([
                                                'tls' => 'TLS',
                                                'ssl' => 'SSL',
                                                '' => 'None',
                                            ]),

                                        TextInput::make('smtp_from_address')
                                            ->label(__('From Email Address')),

                                        TextInput::make('smtp_from_name')
                                            ->label(__('From Name')),
                                    ]),

                                Forms\Components\Section::make(__('Mailchimp Integration'))
                                    ->aside()
                                    ->collapsed()
                                    ->schema([
                                        TextInput::make('mailchimp_api_key')
                                            ->label(__('API Key')),

                                        TextInput::make('mailchimp_list_id')
                                            ->label(__('List ID')),

                                        TextInput::make('mailchimp_email')
                                            ->label(__('Email')),

                                        TextInput::make('mailchimp_name')
                                            ->label(__('Name')),
                                    ]),
                            ]),

                        Tabs\Tab::make('3rd Party Services')
                            ->schema([
                                Forms\Components\Section::make(__('OpenAI Settings'))
                                    ->aside()
                                    ->schema([
                                        TextInput::make('openai_key')
//                                            ->formatStateUsing(function ($state) {
//                                                if (!$state) return $state;
//                                                return substr($state, 0, 4) . str_repeat('*', strlen($state) - 4);
//                                            })
                                            ->label(__('OpenAI API Key')),
                                    ]),

                                Forms\Components\Section::make(__('Zoom API Settings'))
                                    ->aside()
                                    ->schema([
                                        TextInput::make('zoom_client_id')
                                            ->label(__('Client ID')),

                                        TextInput::make('zoom_client_secret')
                                            ->label(__('Client Secret')),

                                        TextInput::make('zoom_account_id')
                                            ->label(__('Account ID')),
                                    ]),
                            ]),

                        Tabs\Tab::make('Social Media')
                            ->schema([
                                Forms\Components\Section::make(__('Social Media Links'))
                                    ->aside()
                                    ->schema([
                                        TextInput::make('site_social.facebook')
                                            ->label(__('Facebook URL')),

                                        TextInput::make('site_social.twitter')
                                            ->label(__('Twitter URL')),

                                        TextInput::make('site_social.instagram')
                                            ->label(__('Instagram URL')),

                                        TextInput::make('site_social.linkedin')
                                            ->label(__('LinkedIn URL')),
                                    ]),
                            ]),

                        Tabs\Tab::make('Analytics & Tracking')
                            ->schema([
                                Forms\Components\Section::make(__('Tracking Codes'))
                                    ->aside()
                                    ->schema([
                                        TextInput::make('google_analytics_tracking_id')
                                            ->label(__('Google Analytics Tracking ID')),

                                        TextInput::make('google_tag_manager_id')
                                            ->label(__('Google Tag Manager ID')),

                                        TextInput::make('facebook_pixel_id')
                                            ->label(__('Facebook Pixel ID')),
                                    ]),
                            ]),

                        Tabs\Tab::make('Watermark Settings')
                            ->schema([
                                Forms\Components\Section::make(__('Watermark Configuration'))
                                    ->aside()
                                    ->schema([
                                        Forms\Components\Toggle::make('watermark_enabled')
                                            ->label(__('Enable Watermark'))
                                            ->onIcon('heroicon-o-check')
                                            ->offIcon('heroicon-o-x-mark')
                                            ->default(false),

                                        TextInput::make('watermark_text')
                                            ->label(__('Watermark Text')),

                                        Forms\Components\Select::make('watermark_position')
                                            ->label(__('Position'))
                                            ->options([
                                                'top-left' => __('Top Left'),
                                                'top-center' => __('Top Center'),
                                                'top-right' => __('Top Right'),
                                                'middle-left' => __('Middle Left'),
                                                'middle-center' => __('Middle Center'),
                                                'middle-right' => __('Middle Right'),
                                                'bottom-left' => __('Bottom Left'),
                                                'bottom-center' => __('Bottom Center'),
                                                'bottom-right' => __('Bottom Right'),
                                            ])
                                            ->default('bottom-right'),

                                        TextInput::make('watermark_font')
                                            ->label(__('Font Name'))
                                            ->placeholder('Arial, sans-serif'),
                                    ]),
                            ]),

                        Tabs\Tab::make('Modules')
                            ->schema([
                                Forms\Components\Section::make(__('Module Management'))
                                    ->aside()
                                    ->schema([
                                        ToggleButtons::make('disabled_modules')
                                            ->label(__('Disabled Modules'))
                                            ->helperText(__('Select the modules you want to disable'))
                                            ->options($this->getModulesOptions())
                                            ->colors(array_fill_keys($this->getModulesOptions(), 'danger'))
                                            ->multiple()
                                            ->columns(2)
                                            ->gridDirection('row'),
                                    ]),
                            ]),
                    ]),
            ]);
    }

    private function getModulesOptions(): array
    {
        $modules = array_keys(Arr::except(Module::scan(), ['core', 'setting', 'navigation', 'theme', 'translate']));
        $titleCaseModules = array_map(function ($modules) {
            return Str::title($modules);
        }, $modules);

        return array_combine($titleCaseModules, $titleCaseModules);
    }
}
