<?php

namespace Modules\Translate\Filament\Pages;

use Exception;
use Filament\Actions\Action;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;
use JetBrains\PhpStorm\NoReturn;
use Stichoza\GoogleTranslate\GoogleTranslate;

class EditLanguageTranslations extends Page
{
    use interactsWithForms;

    public ?string $language = 'ar';

    protected static string $view = 'translate::filament.pages.edit-language-translations';

    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];

    public static function getNavigationLabel(): string
    {
        return __('Translates');
    }

    public function getHeading(): string|Htmlable
    {
        return __('Translates');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('auto_translate')->color('success')->icon('heroicon-o-language')->action('auto_translate'),
            Action::make('submit')->color('success')->icon('heroicon-o-check')->action('submit'),
        ];
    }

    public function mount(): void
    {
        $this->language = request()->get('language');
        $this->form->fill();
    }

    //    public function updatedInteractsWithForms(string $statePath): void
    //    {
    //        if($statePath == 'language') {
    //            $this->form->fill();
    //        }
    //    }
    //

    public function form(Form $form): Form
    {
        $jsonString = [];
        $path = lang_path("{$this->language}.json");
        if (File::exists($path)) {
            $json_string = preg_replace_callback(
                '/"([^"]+?)":/', // Match all keys (assumes well-formed JSON)
                function ($matches) {
                    $key = str_replace('.', '', $matches[1]); // Remove dots from the key

                    return '"'.$key.'":'; // Reconstruct the key
                },
                File::get($path)
            );
            //            dd($json_string);
            $translations = collect(json_decode($json_string, false));
            $jsonString = $translations->map(fn ($value, $key) => TextInput::make($key)->default($value)
            )
                ->flatten()
                ->toArray();
        }

        return $form
            ->schema([
                Section::make(__('Translations Keys (Language: :language)', ['language' => $this->language]))
                    ->schema([
                        KeyValue::make('keys')

                            ->schema($jsonString)
                            ->deletable(false)
                            ->valueLabel(__('Translation'))
                            ->editableKeys(false),
                    ])
                    ->columns(1),

            ])->statePath('data');
    }

    #[NoReturn]
    public function submit(): void
    {
        File::put(
            lang_path("$this->language.json"),
            json_encode($this->data['keys'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );

        Notification::make()->title(__('Translation keys saved'))->success()->send();
    }

    public function auto_translate(): void
    {

        try {
            //            set_time_limit(16000);
            //            $translated = [];
            //            collect(Arr::except($this->form->getState(), ['keys']))->chunk(20)->each(function ($chunk) use (&$translated){
            //                $chunk->map(function ($value, $key) use (&$translated){
            //                    $translated[$key] = (new GoogleTranslate)->setTarget($this->language)->translate($value);
            //                    $this->form->fill($translated);
            //                });
            //            });
            //
            //            $values = Arr::except($this->form->getState(), ['keys']);
            collect($this->data)->chunk(20)->each(function ($chunk) {
                dispatch(function () use ($chunk) {
                    $chunk->map(function ($value, $key) {
                        $translated = (new GoogleTranslate)->setTarget($this->language)->translate($key);
                        $values = $this->data;
                        $values[$key] = $translated;
                        File::put(base_path('lang/'.$this->language.'.json'), json_encode($values, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
                    });
                });
            });
        } catch (Exception $exception) {
            Notification::make()->title($exception->getMessage())->danger()->send();
        }

        Notification::make()->title('keys will be translated in background')->success()->send();

    }
}
