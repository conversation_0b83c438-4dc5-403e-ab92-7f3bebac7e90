<?php

namespace Modules\Setting\Filament\Pages;

use Exception;
use Filament\Actions\Action;
use Filament\Actions\LocaleSwitcher;
use Filament\Forms\Components;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Resources\Concerns\Translatable;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Modules\Setting\Contracts\SettingsInterface;
use Modules\Setting\Models\Setting as SettingModel;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class Setting extends Page
{
    use interactsWithForms, Translatable;

    protected SettingsInterface $settings;

    public function boot(SettingsInterface $settings): void
    {
        $this->settings = $settings;
    }

    public ?string $activeLocale = null;

    public function __construct()
    {
        $this->activeLocale = app()->getLocale();
    }

    protected static string $view = 'setting::filament.pages.settings';

    public static function getNavigationGroup(): ?string
    {
        return __('Settings');
    }

    public static function getNavigationLabel(): string
    {
        return __('Settings');
    }

    public function getHeading(): string|Htmlable
    {
        return __('Settings');
    }

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make()->placeholder('Language'),
            Action::make('submit')->color('success')->icon('heroicon-o-check')->action('submit'),
        ];
    }

    public ?array $data = [];

    public function mount(SettingModel $setting): void
    {
        $this->form->fill($setting->pluck('value', 'key')->toArray());
    }

    public function updatedInteractsWithForms(string $statePath): void
    {
        $results = SettingModel::all(); // Replace YourModel with your actual model class

        $keyValuePairs = $results->mapWithKeys(function ($item) {
            $item->setLocale($this->activeLocale ?? app()->getLocale());

            return [$item->key => $item->value];
        });

        if ($statePath == 'activeLocale') {
            $this->form->fill($keyValuePairs->toArray());
        }
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function form(Form $form): Form
    {
        $settings_default = config('setting.settings_fields', []);
        $setting_config = config('settings_fields', []);
        $settings = array_merge($settings_default, $setting_config);
        $mapped = Arr::mapWithKeys($settings ?? [], function (array $value, string $key) {
            $types = Arr::map($value, function (array $element, string $key) {
                return $this->types_match($key, $element);
            });

            return [
                $key => Tabs\Tab::make(__(Str::headline($key)))->schema($types)->icon($value['icon'] ?? 'heroicon-o-cog-6-tooth'),
            ];
        });

        return $form
            ->schema([
                Tabs::make('Tabs')->tabs($mapped ?? []),
            ])
            ->statePath('data');

        //        }

        //        return $form->schema($formSchema);

        //        $mapped = Arr::mapWithKeys($settings_fields ?? [], function (array $value, string $key) {
        //            $types = Arr::map($value['elements'], function (array $element) {
        //                return $this->types_match($element);
        //            });
        //            return [
        //                $key => Tabs\Tab::make(__(Str::title($key)))->schema($types)->icon($value['icon'] ?? 'heroicon-o-cog-6-tooth'),
        //            ];
        //        });
        //
        //        return $form
        //            ->schema([
        //                Tabs::make('Tabs')->tabs($mapped ?? []),
        //            ])
        //            ->statePath('data');
    }

    public function submit(): void
    {
        try {
            foreach ($this->form->getState() as $key => $value) {
                $this->settings->set($key, $value, $this->activeLocale);
            }
        } catch (Exception $exception) {
            Notification::make()->title($exception->getMessage())->danger()->send();
        }

        Notification::make()->title(__('Settings Updated'))->success()->send();

    }

    private function types_match($key, $element)
    {
        return match ($element['field_type']) {
            'select' => Components\Select::make($key)
                ->options(array_key_exists('class', $element) ? ((new $element['class'])->pluck($element['valueCol'], $element['keyCol'])->toArray()) : $element['options'])
                ->helperText(new HtmlString(__($element['help'] ?? '')))
                ->multiple($element['multiple'] ?? false)
                ->searchable()
                ->inlineLabel(),
            'toggleButtons' => Components\ToggleButtons::make($key)

                ->options($element['options'])
                ->inline()
                ->inlineLabel(),
            'textarea' => Components\Textarea::make($key)

                ->helperText(new HtmlString(__($element['help'] ?? '')))
                ->inlineLabel(),
            'checkbox' => Components\Toggle::make($key)

                ->helperText(new HtmlString(__($element['help'] ?? '')))
                ->inlineLabel(),
            'upload' => Components\FileUpload::make($key)

                ->image()
                ->imageEditor()
                ->helperText(new HtmlString(__($element['help'] ?? '')))
                ->inlineLabel(),
            'mdeditor' => Components\MarkdownEditor::make($key)

                ->helperText(new HtmlString(__($element['help'] ?? '')))
                ->inlineLabel(),
            'color' => Components\ColorPicker::make($key)

                ->helperText(new HtmlString(__($element['help'] ?? '')))
                ->inlineLabel(),
            'editor' => Components\RichEditor::make($key)

                ->helperText(new HtmlString(__($element['help'] ?? '')))
                ->disableToolbarButtons(['codeBlock', 'undo', 'redo'])
                ->inlineLabel(),
            'tagsInput' => Components\TagsInput::make($key)

                ->helperText(new HtmlString(__($element['help'] ?? '')))
                ->inlineLabel(),
            default => Components\TextInput::make($key)

                ->helperText(new HtmlString(__($element['help'] ?? '')))
                ->placeholder(__($element['placeholder'] ?? ''))
                ->inlineLabel(),
        };
    }
}
