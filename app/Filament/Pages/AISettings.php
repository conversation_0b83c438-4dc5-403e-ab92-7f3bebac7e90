<?php

namespace Modules\AI\Filament\Pages;

use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;

class AISettings extends SettingsPage
{
    public static function getNavigationGroup(): ?string
    {
        return __('Settings');
    }

    protected static ?int $navigationSort = 2;

    /**
     * @return string|null
     */
    public static function getNavigationLabel(): string
    {
        return __('AI Settings');
    }

    protected static string $settings = \Modules\AI\Settings\AISettings::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Set Default AI Service'))
                    ->aside()
                    ->columns(1)
                    ->schema([
                        Forms\Components\Select::make('default_ai_service')
                            ->options([
                                'openai' => 'OpenAI',
                                'groq' => 'Groq',
                                'together' => 'Together',
                                'geminai' => 'Geminai',
                                'perplexai' => 'Perplexai',
                            ])
                            ->default('groq'),
                    ]),
                Forms\Components\Section::make(__('OpenAI Settings'))
                    ->aside()
                    ->columns(1)
                    ->schema([
                        TextInput::make('openai_api_key'),
                    ]),
                Forms\Components\Section::make(__('Groq.ai Settings'))
                    ->aside()
                    ->columns(1)
                    ->schema([
                        TextInput::make('groq_api_key'),
                    ]),
                Forms\Components\Section::make(__('Together.ai Settings'))
                    ->aside()
                    ->columns(1)
                    ->schema([
                        TextInput::make('together_api_key'),
                    ]),
            ]);
    }
}
