<?php

namespace Modules\Translate\Filament\Resources;

use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Modules\Translate\Filament\Pages\EditLanguageTranslations;
use Modules\Translate\Filament\Resources\LanguageResource\Pages;

class LanguageResource extends Resource
{
    protected static ?string $model = \Modules\Core\Models\Localization\Language::class;

    public static function getNavigationGroup(): ?string
    {
        return __('Settings');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->formatStateUsing(function ($state) {
                        return \Modules\Core\Models\Localization\Language::where('iso', $state)->first()?->name ?? $state;
                    })

                    ->sortable()
                    ->searchable(),
                TextColumn::make('iso')->alignRight()->badge(),
                // activate
                Tables\Columns\ToggleColumn::make('is_activated'),
            ])
            ->actions([
                Tables\Actions\Action::make('edit')

                    ->url(fn ($record) => EditLanguageTranslations::getUrl(['language' => $record->iso])),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLanguages::route('/'),
            'create' => Pages\CreateLanguage::route('/create'),
            'edit' => Pages\EditLanguage::route('/{record}/edit'),
        ];
    }
}
