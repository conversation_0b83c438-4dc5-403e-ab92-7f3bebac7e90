<?php

namespace Modules\Billing\Filament\Resources\OrderResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Billing\Enums\OrderStatus;
use Modules\Billing\Filament\Resources\OrderResource;

class EditOrder extends EditRecord
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\Action::make('mark_as_completed')
                ->action(fn () => $this->record->setStatus(OrderStatus::COMPLETED))
                ->visible(fn () => $this->record->status === OrderStatus::PENDING)
                ->color('success')
                ->icon('heroicon-s-check-circle'),
            Actions\Action::make('mark_as_cancelled')
                ->action(fn () => $this->record->cancel('Cancelled by admin'))
                ->visible(fn () => $this->record->status === OrderStatus::PENDING)
                ->color('danger')
                ->icon('heroicon-s-x-circle')
                ->requiresConfirmation(),
            Actions\Action::make('generate_invoice')
                ->action(function () {
                    app(\Modules\Billing\Services\BillingService::class)->generateInvoice($this->record);
                    $this->notify('success', 'Invoice generated successfully');
                })
                ->visible(fn () => $this->record->isPaid())
                ->color('gray')
                ->icon('heroicon-s-document-text'),
        ];
    }
}
