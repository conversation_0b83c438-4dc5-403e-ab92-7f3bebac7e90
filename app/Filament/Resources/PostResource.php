<?php

namespace Modules\Blog\Filament\Resources;

use Amid<PERSON><PERSON>hani\FilamentTinyEditor\TinyEditor;
use Filament\Forms;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\SpatieTagsInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Modules\Blog\Filament\Resources\PostResource\Pages;
use Modules\Blog\Models\Post;

class PostResource extends Resource
{
    protected static ?string $model = Post::class;

    protected static ?string $slug = 'posts';

    public static function getLabel(): string
    {
        return __('Post');
    }

    public static function getPluralLabel(): string
    {
        return __('Posts');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Blog');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make()
                    ->columns(3)
                    ->schema([
                        Forms\Components\Section::make()
                            ->columnSpan(2)
                            ->schema([
                                Forms\Components\TextInput::make('title')
                                    ->placeholder('Enter a title')
                                    ->live(debounce: '750ms')
                                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set, string $operation, ?string $old, ?string $state) {
                                        if (($get('slug') ?? '') !== Str::slug($old) || $operation !== 'create') {
                                            return;
                                        }

                                        $set('slug', Str::slug($state));
                                    })
                                    ->required()
                                    ->maxLength(255)
                                    ->autofocus(),

                                Forms\Components\TextInput::make('slug')
                                    ->placeholder('Enter a slug')
                                    ->alphaDash()
                                    ->required()
                                    ->unique(ignoreRecord: true)
                                    ->maxLength(255),

                                TinyEditor::make('content')
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsVisibility('public')
                                    ->fileAttachmentsDirectory('uploads')
                                    ->profile('default|simple|full|minimal|none|custom')
                                    ->resize('both')
                                    ->columnSpan('full')
                                    ->required(),

                                //                                Forms\Components\Builder::make('content')
                                //                                    ->required()
                                //                                    ->columnSpanFull()
                                //                                    ->default([
                                //                                        ['type' => 'markdown'],
                                //                                    ])
                                //                                    ->collapsible()
                                //                                    ->blocks([
                                //                                        Forms\Components\Builder\Block::make('markdown')
                                //                                            ->label(function (?array $state): string {
                                //                                                if ($state === null) {
                                //                                                    return 'Heading';
                                //                                                }
                                //
                                //                                                return $state['headline'] ?? 'Untitled heading';
                                //                                            })
                                //                                            ->schema([
                                //                                                Forms\Components\TextInput::make('headline')->live(),
                                //                                                Forms\Components\MarkdownEditor::make('content')
                                //                                                    ->hintAction(
                                //                                                        Forms\Components\Actions\Action::make('generate')->icon('heroicon-o-sparkles')
                                //                                                            ->disabled(fn (callable $get) => ! $get('headline'))
                                //                                                            ->action(function (Forms\Get $get, Forms\Set $set) {
                                // //                                                                $set('content', generate(
                                // //                                                                    'I would like you to act as an Writer, Generate a post in markdown format  based on the provided data',
                                // //                                                                    $get('headline')
                                // //                                                                ));
                                //                                                            })
                                //                                                    )
                                //                                                    ->required(),
                                //                                            ]),
                                //
                                // //                                        Forms\Components\Builder\Block::make('figure')
                                // //                                            ->schema([
                                // //                                                Forms\Components\Fieldset::make()
                                // //
                                // //                                                    ->schema([
                                // //                                                        Forms\Components\TextInput::make('alt')
                                // //
                                // //                                                            ->placeholder('Enter alt text')
                                // //                                                            ->required()
                                // //                                                            ->maxLength(255),
                                // //
                                // //                                                        Forms\Components\TextInput::make('caption')
                                // //                                                            ->placeholder('Enter a caption')
                                // //                                                            ->maxLength(255),
                                // //                                                    ]),
                                // //
                                // //                                            ]),
                                //                                    ]),
                            ]),

                        Forms\Components\Section::make()
                            ->columnSpan(1)
                            ->schema([
                                Forms\Components\Select::make('blog_category_id')
                                    ->relationship('categories', 'name')
                                    ->searchable()

                                    ->preload()
                                    ->createOptionForm([
                                        Forms\Components\TextInput::make('name')
                                            ->required()
                                            ->maxLength(255),
                                    ])
                                    ->required(),

                                Forms\Components\SpatieMediaLibraryFileUpload::make('cover')
                                    ->imageResizeTargetWidth(config('globals.images.post.width', '1000px'))
                                    ->imageResizeTargetHeight(config('globals.images.post.height', '200px'))
                                    ->collection(collection: 'cover')
                                    ->imageEditor()
                                    ->image()
                                    ->required(),

                                Forms\Components\Toggle::make('is_published')

                                    ->required(),

                                SpatieTagsInput::make('tags'),

                            ]),

                    ]),
            ]);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                SpatieMediaLibraryImageColumn::make('cover')->circular(),
                TextColumn::make('title')->grow(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_published')

                    ->boolean()
                    ->trueLabel('Only Published Posts')
                    ->falseLabel('Only Draft Posts')
                    ->native(false),
                Tables\Filters\SelectFilter::make('blog_category_id')
                    ->relationship('categories', 'name')

                    ->searchable()
                    ->multiple(),

            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPosts::route('/'),
            'create' => Pages\CreatePost::route('/create'),
            'edit' => Pages\EditPost::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
