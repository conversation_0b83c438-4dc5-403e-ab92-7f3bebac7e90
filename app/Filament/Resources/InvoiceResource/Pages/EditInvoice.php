<?php

namespace Modules\Billing\Filament\Resources\InvoiceResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Billing\Filament\Resources\InvoiceResource;
use Modules\Billing\Models\Invoice;

class EditInvoice extends EditRecord
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\Action::make('mark_as_paid')
                ->action(fn () => $this->record->markAsPaid(null, 'manual'))
                ->visible(fn () => $this->record->status === Invoice::STATUS_PENDING)
                ->color('success')
                ->icon('heroicon-s-check-circle'),
            Actions\Action::make('mark_as_overdue')
                ->action(function () {
                    $this->record->status = Invoice::STATUS_OVERDUE;
                    $this->record->save();
                })
                ->visible(fn () => $this->record->status === Invoice::STATUS_PENDING)
                ->color('danger')
                ->icon('heroicon-s-exclamation'),
            Actions\Action::make('download_pdf')
                ->url(fn () => route('billing.invoices.pdf', ['invoice' => $this->record->id]))
                ->color('gray')
                ->icon('heroicon-s-document-download')
                ->openUrlInNewTab(),
        ];
    }
}
