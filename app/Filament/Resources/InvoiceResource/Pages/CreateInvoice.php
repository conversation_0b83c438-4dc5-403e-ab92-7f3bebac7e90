<?php

namespace Modules\Billing\Filament\Resources\InvoiceResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\Billing\Filament\Resources\InvoiceResource;
use Modules\Billing\Models\Invoice;

class CreateInvoice extends CreateRecord
{
    protected static string $resource = InvoiceResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Generate an invoice number if one wasn't provided
        if (! isset($data['invoice_number']) || empty($data['invoice_number'])) {
            $data['invoice_number'] = Invoice::generateInvoiceNumber();
        }

        return $data;
    }
}
