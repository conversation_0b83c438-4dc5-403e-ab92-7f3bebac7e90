<?php

namespace Modules\Navigation\Filament\Resources\NavigationResource\Pages;

use Filament\Actions\CreateAction;
use Filament\Actions\LocaleSwitcher;
use Filament\Resources\Pages\ListRecords;
use Modules\Navigation\Filament\Resources\NavigationResource;

class ListNavigations extends ListRecords
{
    use ListRecords\Concerns\Translatable;

    protected static string $resource = NavigationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),
            CreateAction::make(),
        ];
    }
}
