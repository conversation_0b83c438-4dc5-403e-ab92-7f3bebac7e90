<?php

namespace Modules\Navigation\Filament\Resources\NavigationResource\Pages;

use Filament\Actions\LocaleSwitcher;
use Filament\Resources\Pages\CreateRecord;
use Modules\Navigation\Filament\Resources\NavigationResource;

class CreateNavigation extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = NavigationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            LocaleSwitcher::make(),

        ];
    }
}
