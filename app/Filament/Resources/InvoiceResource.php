<?php

namespace Modules\Billing\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Modules\Billing\Filament\Resources\InvoiceResource\Pages;
use Modules\Billing\Models\Invoice;

class InvoiceResource extends Resource
{
    protected static ?string $model = Invoice::class;

    protected static ?string $slug = 'billing/invoices';

    protected static ?string $recordTitleAttribute = 'invoice_number';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Billing';

    protected static ?int $navigationSort = 20;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Invoice Information')
                            ->schema([
                                Forms\Components\TextInput::make('invoice_number')
                                    ->required()
                                    ->disabled()
                                    ->unique(ignoreRecord: true),
                                Forms\Components\Select::make('status')
                                    ->options([
                                        Invoice::STATUS_DRAFT => 'Draft',
                                        Invoice::STATUS_PENDING => 'Pending',
                                        Invoice::STATUS_PAID => 'Paid',
                                        Invoice::STATUS_OVERDUE => 'Overdue',
                                        Invoice::STATUS_CANCELLED => 'Cancelled',
                                    ])
                                    ->required(),
                                Forms\Components\Select::make('user_id')
                                    ->relationship('user', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                                Forms\Components\Select::make('order_id')
                                    ->relationship('order', 'order_number')
                                    ->searchable()
                                    ->preload(),
                                Forms\Components\TextInput::make('currency')
                                    ->required()
                                    ->length(3),
                                Forms\Components\DatePicker::make('due_date')
                                    ->label('Due Date')
                                    ->required(),
                                Forms\Components\DateTimePicker::make('paid_at')
                                    ->label('Payment Date'),
                            ]),

                        Forms\Components\Section::make('Financial Details')
                            ->schema([
                                Forms\Components\TextInput::make('amount')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0),
                                Forms\Components\TextInput::make('tax_amount')
                                    ->numeric()
                                    ->minValue(0),
                                Forms\Components\TextInput::make('total_amount')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0),
                            ]),
                    ]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Notes')
                            ->schema([
                                Forms\Components\Textarea::make('notes')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),

                        Forms\Components\Section::make('Billing Address')
                            ->schema([
                                Forms\Components\Textarea::make('billing_address')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),

                        Forms\Components\Section::make('Metadata')
                            ->schema([
                                Forms\Components\Textarea::make('meta')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('invoice_number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('order.order_number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        Invoice::STATUS_DRAFT => 'gray',
                        Invoice::STATUS_PENDING => 'warning',
                        Invoice::STATUS_PAID => 'success',
                        Invoice::STATUS_OVERDUE => 'danger',
                        Invoice::STATUS_CANCELLED => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('total_amount')
                    ->sortable()
                    ->money(fn ($record) => $record->currency),
                Tables\Columns\TextColumn::make('due_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('paid_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        Invoice::STATUS_DRAFT => 'Draft',
                        Invoice::STATUS_PENDING => 'Pending',
                        Invoice::STATUS_PAID => 'Paid',
                        Invoice::STATUS_OVERDUE => 'Overdue',
                        Invoice::STATUS_CANCELLED => 'Cancelled',
                    ]),
                Tables\Filters\Filter::make('paid')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('paid_at')),
                Tables\Filters\Filter::make('unpaid')
                    ->query(fn (Builder $query): Builder => $query->whereNull('paid_at')),
                Tables\Filters\Filter::make('overdue')
                    ->query(fn (Builder $query): Builder => $query->where('status', Invoice::STATUS_PENDING)
                        ->whereDate('due_date', '<', now())
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('mark_as_paid')
                        ->icon('heroicon-s-check-circle')
                        ->color('success')
                        ->action(fn (Invoice $record) => $record->markAsPaid(null, 'manual'))
                        ->visible(fn (Invoice $record) => $record->status === Invoice::STATUS_PENDING),
                    Tables\Actions\Action::make('download_pdf')
                        ->icon('heroicon-s-document-download')
                        ->url(fn (Invoice $record) => route('billing.invoices.pdf', ['invoice' => $record->id]))
                        ->openUrlInNewTab(),
                    Tables\Actions\Action::make('send_to_customer')
                        ->icon('heroicon-s-paper-airplane')
                        ->action(function (Invoice $record) {
                            // Implementation for sending invoice to customer
                            // Would connect to a notification service
                        }),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'edit' => Pages\EditInvoice::route('/{record}/edit'),
            'view' => Pages\ViewInvoice::route('/{record}'),
        ];
    }
}
