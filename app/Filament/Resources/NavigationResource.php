<?php

namespace Modules\Navigation\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Modules\Navigation\Models\Navigation;

class NavigationResource extends Resource
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static ?string $model = Navigation::class;

    protected static ?string $slug = 'navigations';

    public static function getNavigationGroup(): ?string
    {
        return __('Settings');
    }

    public static function form(Form $form): Form
    {

        $routeList = app('hook')->apply('navItems', [url('/') => 'Home']);

        // $routeCollection = \Route::getRoutes();
        // foreach ($routeCollection as $key => $route) {

        //        foreach (TourType::all() as $tour_types) {
        //            $routeList[route('tour_types.show', ['tour_types' => $tour_types])] = $tour_types->title;
        //        }

        return $form
            ->schema([
                Forms\Components\Grid::make(['default' => 1])->schema([
                    Forms\Components\TextInput::make('key')

                        ->required()
                        ->maxLength(255),

                    Forms\Components\Repeater::make('items')

                        ->schema([
                            Forms\Components\Select::make('route')

                                ->reactive()
                                ->searchable()
                                ->afterStateUpdated(function (Forms\Components\Select $component, Forms\Get $get, Forms\Set $set, $state) {
                                    $set('title', $component->getOptions()[$state] ?? null);
                                    $set('url', $state);
                                })
                                ->options($routeList),
                            Forms\Components\TextInput::make('title')

                                ->required()
                                ->maxLength(255),
                            Forms\Components\TextInput::make('url')

                                ->maxLength(255),
                            Forms\Components\Repeater::make('items')
                                ->default([])
                                ->schema([
                                    Forms\Components\Select::make('route')

                                        ->reactive()
                                        ->searchable()
                                        ->afterStateUpdated(function (Forms\Components\Select $component, Forms\Get $get, Forms\Set $set, $state) {
                                            $set('title', $component->getOptions()[$state] ?? null);
                                            $set('url', $state);
                                        })
                                        ->options($routeList),
                                    Forms\Components\TextInput::make('title')

                                        ->required()
                                        ->maxLength(255),
                                    Forms\Components\TextInput::make('url')

                                        ->maxLength(255),
                                ])
                                ->collapsible()
                                ->itemLabel(fn (array $state): ?string => $state['title'] ?? null),
                            Forms\Components\Toggle::make('blank')

                                ->required(),
                        ])->reorderable()
                        ->collapsible()
                        ->collapsed()
                        ->itemLabel(fn (array $state): ?string => $state['title'] ?? null),

                    Forms\Components\Select::make('location')
                        ->options([
                            'header' => 'Header',
                            'footer' => 'Footer',
                        ])
                        ->required(),

                    Forms\Components\Toggle::make('activated')

                        ->required(),

                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')->sortable(),
                Tables\Columns\ToggleColumn::make('activated')->alignRight(),
            ])
            ->filters([
                Tables\Filters\Filter::make('activated')
                    ->query(fn (Builder $query): Builder => $query->where('activated', true)),

            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\Navigation\Filament\Resources\NavigationResource\Pages\ListNavigations::route('/'),
            'create' => \Modules\Navigation\Filament\Resources\NavigationResource\Pages\CreateNavigation::route('/create'),
            'edit' => \Modules\Navigation\Filament\Resources\NavigationResource\Pages\EditNavigation::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
