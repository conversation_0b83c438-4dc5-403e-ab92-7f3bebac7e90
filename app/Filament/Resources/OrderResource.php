<?php

namespace Modules\Billing\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Modules\Billing\Enums\OrderStatus;
use Modules\Billing\Filament\Resources\OrderResource\Pages;
use Modules\Billing\Models\Order;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $slug = 'billing/orders';

    protected static ?string $recordTitleAttribute = 'order_number';

    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';

    protected static ?string $navigationGroup = 'Billing';

    protected static ?int $navigationSort = 10;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Order Information')
                            ->schema([
                                Forms\Components\TextInput::make('order_number')
                                    ->required()
                                    ->disabled()
                                    ->unique(ignoreRecord: true),
                                Forms\Components\Select::make('status')
                                    ->options([
                                        OrderStatus::PENDING->value => 'Pending',
                                        OrderStatus::PROCESSING->value => 'Processing',
                                        OrderStatus::COMPLETED->value => 'Completed',
                                        OrderStatus::CANCELLED->value => 'Cancelled',
                                        OrderStatus::REFUNDED->value => 'Refunded',
                                    ])
                                    ->required(),
                                Forms\Components\Select::make('user_id')
                                    ->relationship('user', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required(),
                                Forms\Components\TextInput::make('currency')
                                    ->required()
                                    ->length(3),
                                Forms\Components\DateTimePicker::make('paid_at')
                                    ->label('Payment Date'),
                                Forms\Components\TextInput::make('payment_gateway')
                                    ->label('Payment Method'),
                            ]),

                        Forms\Components\Section::make('Financial Details')
                            ->schema([
                                Forms\Components\TextInput::make('subtotal')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0),
                                Forms\Components\TextInput::make('discount_total')
                                    ->numeric()
                                    ->minValue(0),
                                Forms\Components\TextInput::make('tax')
                                    ->numeric()
                                    ->minValue(0),
                                Forms\Components\TextInput::make('total')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0),
                            ]),
                    ]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Notes')
                            ->schema([
                                Forms\Components\Textarea::make('note')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),

                        Forms\Components\Section::make('Addresses')
                            ->schema([
                                Forms\Components\Textarea::make('billing_address')
                                    ->columnSpanFull(),
                                Forms\Components\Textarea::make('shipping_address')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),

                        Forms\Components\Section::make('Metadata')
                            ->schema([
                                Forms\Components\Textarea::make('meta')
                                    ->columnSpanFull(),
                            ])
                            ->collapsible(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('order_number')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->sortable()
                    ->badge(),
                Tables\Columns\TextColumn::make('total')
                    ->sortable()
                    ->money(fn ($record) => $record->currency),
                Tables\Columns\TextColumn::make('paid_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'processing' => 'Processing',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                        'refunded' => 'Refunded',
                    ]),
                Tables\Filters\Filter::make('paid')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('paid_at')),
                Tables\Filters\Filter::make('unpaid')
                    ->query(fn (Builder $query): Builder => $query->whereNull('paid_at')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('mark_as_paid')
                        ->icon('heroicon-s-check-circle')
                        ->color('success')
                        ->action(fn (Order $record) => $record->setStatus(OrderStatus::COMPLETED))
                        ->visible(fn (Order $record) => $record->status === OrderStatus::PENDING),
                    Tables\Actions\Action::make('generate_invoice')
                        ->icon('heroicon-s-document-text')
                        ->action(function (Order $record) {
                            app(\Modules\Billing\Services\BillingService::class)->generateInvoice($record);
                        })
                        ->visible(fn (Order $record) => $record->isPaid()),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
            'view' => Pages\ViewOrder::route('/{record}'),
        ];
    }
}
