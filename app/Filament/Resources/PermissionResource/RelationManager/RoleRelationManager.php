<?php

namespace App\Filament\Resources\PermissionResource\RelationManager;

use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class RoleRelationManager extends RelationManager
{
    protected static string $relationship = 'roles';

    protected static ?string $recordTitleAttribute = 'name';

    protected static function getModelLabel(): string
    {
        return __('Role');
    }

    protected static function getPluralModelLabel(): string
    {
        return __('Roles');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label(__('Name')),
                TextInput::make('guard_name')
                    ->label(__('Guard Name')),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            // Support changing table heading by translations.
            ->heading(__('Roles'))
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->label(__('Name')),
                TextColumn::make('guard_name')
                    ->searchable()
                    ->label(__('Guard Name')),
            ])
            ->filters([
                //
            ]);
    }
}
