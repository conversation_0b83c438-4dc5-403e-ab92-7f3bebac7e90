<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Modules\Core\Models\Localization\Area;
use Modules\Core\Models\Localization\City;
use Modules\Core\Models\Localization\Country;
use Modules\Institute\Concerns\UserTypes;
use Modules\Institute\Models\Institute;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getPluralLabel(): ?string
    {
        return __('Users');
    }

    public static function getLabel(): ?string
    {
        return __('User');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('Roles and Permissions');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Personal Information')
                    ->label(__('Personal Information'))
                    ->aside()
                    ->schema([
                        Forms\Components\Group::make()
                            ->columns(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->required(),
                                Forms\Components\TextInput::make('username')
                                    ->required(),
                                Forms\Components\TextInput::make('email')
                                    ->email()
                                    ->required()
                                    ->unique(User::class, 'email', fn (?User $record) => $record),
                                Forms\Components\Select::make('type')->options(UserTypes::class),
                                Forms\Components\TextInput::make('phone')
                                    ->required()
                                    ->label('Contact No.')
                                    ->tel()
                                    ->maxLength(255),
                                Forms\Components\Select::make('institute_id')
                                    ->placeholder('Not associated with any institute')
                                    ->options(Institute::pluck('name', 'id')),
                            ]),
                    ]),
                Forms\Components\Section::make('Authentication')
                    ->aside()
                    ->schema([
                        Forms\Components\Group::make()
                            ->columns(2)

                            ->label('Account Details')
                            ->schema([
                                Forms\Components\TextInput::make('password')
                                    ->password()
                                    ->required(fn (string $operation): bool => $operation === 'create')
                                    ->maxLength(255)
                                    ->dehydrated(fn (?string $state): bool => filled($state))
                                    ->rule(Password::default()),
                            ]),
                    ]),
                Forms\Components\Section::make('Address')
                    ->aside()
                    ->schema([
                        Forms\Components\Group::make()
                            ->columns(3)
                            ->label('Account Details')
                            ->schema([
                                Forms\Components\Select::make('country')
                                    ->reactive()
                                    ->dehydrated()
                                    ->options(Country::all()->pluck('name', 'id')->toArray())
                                    ->afterStateUpdated(fn (callable $set) => $set('parent_id', null))
                                    ->searchable(),

                                Forms\Components\Select::make('state_id')
                                    ->reactive()
                                    ->dehydrated()
                                    ->options(fn (callable $get) => City::whereCountryId($get('country'))?->pluck('name', 'id')->toArray())
                                    ->disabled(fn (callable $get) => ! $get('country')),

                                Forms\Components\Select::make('city_id')
                                    ->reactive()
                                    ->options(fn (callable $get) => Area::whereCityId($get('state_id'))?->pluck('name', 'id')->toArray())
                                    ->disabled(fn (callable $get) => ! $get('state_id')),
                                Forms\Components\TextInput::make('about_me')
                                    ->columnSpan(2)
                                    ->required(),
                                Forms\Components\Select::make('gender')
                                    ->options(['male' => __('Male'), 'female' => __('Female')])
                                    ->columnSpan(2)
                                    ->required(),
                            ]),
                    ]),

            ]);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('role.name'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime(),
            ])
            ->filters([
                Tables\Filters\Filter::make('verified')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('email_verified_at')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),

                Action::make('changePassword')
                    ->action(function (User $record, array $data): void {
                        $record->update([
                            'password' => Hash::make($data['new_password']),
                        ]);
                        Notification::make()->title('Password changed successfully.')->sendToDatabase($record);
                    })
                    ->form([
                        Forms\Components\TextInput::make('new_password')
                            ->password()
                            ->label(__('New Password'))
                            ->required()
                            ->rule(Password::default()),
                        Forms\Components\TextInput::make('new_password_confirmation')
                            ->password()
                            ->label(__('Confirm New Password'))
                            ->rule('required', fn ($get) => (bool) $get('new_password'))
                            ->same('new_password'),
                    ])
                    ->icon('heroicon-o-key'),
                Action::make('delete')
                    ->color('danger')
                    ->icon('heroicon-o-trash')
                    ->action(fn (User $record) => $record->delete()),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
