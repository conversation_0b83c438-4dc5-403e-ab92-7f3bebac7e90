<?php

namespace Modules\ActivityLogs\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Table;
use Modules\ActivityLogs\Models\ActivityLog;

class ActivityLogResource extends Resource
{
    protected static ?string $model = ActivityLog::class;

    protected static ?string $slug = 'activity-logs';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __('Settings');
    }

    public static function getLabel(): ?string
    {
        return __('Activity Log');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Activity Logs');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make([
                    Forms\Components\Grid::make()->schema([
                        Forms\Components\TextInput::make('causer_type')->disabled()->label('User Type'),
                        Forms\Components\TextInput::make('event')->disabled(),
                        Forms\Components\TextInput::make('model_type')->disabled()->label('Model'),
                    ]),
                    Forms\Components\KeyValue::make('old_values')->addable(false)->deletable(false)->label('Previous Values'),
                    Forms\Components\KeyValue::make('new_values')->addable(false)->deletable(false)->label('Current Values'),
                ]),

                Forms\Components\Placeholder::make('created_at')
                    ->label('Created Date')
                    ->content(fn (?ActivityLog $record): string => $record?->created_at?->diffForHumans() ?? '-'),

                Forms\Components\Placeholder::make('updated_at')
                    ->label('Last Modified Date')
                    ->content(fn (?ActivityLog $record): string => $record?->updated_at?->diffForHumans() ?? '-'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('causer.name')->label('User'),
                Tables\Columns\TextColumn::make('event'),
                Tables\Columns\TextColumn::make('model_type')->label('Model'),
                Tables\Columns\TextColumn::make('created_at')->dateTime(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('event')
                    ->options([
                        'created' => 'Created',
                        'updated' => 'Updated',
                        'deleted' => 'Deleted',
                    ]),
                //
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\ActivityLogs\Filament\Resources\ActivityLogResource\Pages\ListActivityLogs::route('/'),
            'create' => \Modules\ActivityLogs\Filament\Resources\ActivityLogResource\Pages\CreateActivityLog::route('/create'),
            'edit' => \Modules\ActivityLogs\Filament\Resources\ActivityLogResource\Pages\EditActivityLog::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
