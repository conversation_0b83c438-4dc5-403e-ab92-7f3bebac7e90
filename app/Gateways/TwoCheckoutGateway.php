<?php

namespace Modules\Payment\Gateways;

use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TwoCheckoutGateway extends AbstractPaymentGateway
{
    private Client $client;

    /**
     * Constructor.
     */
    public function __construct()
    {
        parent::__construct(config('payment.gateways.2checkout'));

        // Set up TwoCheckout API key
        if ($this->isEnabled()) {
            $this->client = new Client([
                'base_uri' => 'https://api.2checkout.com/rest/6.0/',
            ]);

        }
    }

    /**
     * Get the display name of the payment gateway
     */
    public function getName(): string
    {
        return 'TwoCheckout';
    }

    /**
     * Get the unique identifier of the payment gateway
     */
    public function getIdentifier(): string
    {
        return 'twocheckout';
    }

    /**
     * Get the description of the payment gateway
     */
    public function getDescription(): string
    {
        return 'Pay securely with your credit card via TwoCheckout';
    }

    /**
     * Process the payment
     */
    public function processPayment(array $paymentData): array
    {

        //        try {

        //            dd($paymentData);
        $data = [
            'Country' => 'us',
            'Currency' => 'usd',
            'CustomerIP' => $paymentData['ip'] ?? request()->ip(),
            'ExternalReference' => 'REST_API_AVANGTE',
            'Language' => 'en',
            'Source' => url('/'),
            'Affiliate' => [],
            'BillingDetails' => (object) [
                'FirstName' => $paymentData['customer_name'],
                'LastName' => $paymentData['customer_name'],
                'Address1' => $paymentData['address']['address1'] ?? 'No address',
                'City' => $paymentData['address']['city'] ?? 'LA',
                'State' => $paymentData['address']['state'] ?? 'California',
                'Zip' => '12312',
                'CountryCode' => 'US',
                'Email' => $paymentData['customer_email'],
                'Phone' => $paymentData['address']['phone'] ?? '',
            ],
            'Items' => [
                (object) [
                    'Name' => 'Dynamic product',
                    'Description' => 'Payment for invoice '.$paymentData['reference'],
                    'Quantity' => 1,
                    'IsDynamic' => true,
                    'Tangible' => false,
                    'PurchaseType' => 'PRODUCT',
                    'Price' => (object) [
                        'Amount' => $paymentData['amount'],
                        'Type' => 'CUSTOM',
                    ],
                ],
            ],
            'PaymentDetails' => (object) [
                'Currency' => 'sar',
                'CustomerIP' => $paymentData['ip'] ?? request()->ip(),
                'PaymentMethod' => (object) [
                    'EesToken' => $paymentData['token'],
//                    'Vendor3DSReturnURL' => route('payment.success'),
                    'Vendor3DSCancelURL' => route('payment.cancelled'),
                ],
                'Type' => 'EES_TOKEN_PAYMENT',
            ],
        ];

        $api_url = 'https://api.2checkout.com/rest/6.0/orders';
        $merchant_id = '494135';
        $secret_key = '4kSzB0[F1]Zrul=!Tt+(';
        $gm_date = gmdate('Y-m-d H:i:s');
        $hash_data = strlen($merchant_id).$merchant_id.strlen($gm_date).$gm_date;
        $hash = hash_hmac('md5', $hash_data, $secret_key);
        $response = $this->client->request('POST', 'orders/', [
            'json' => $data,
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Avangate-Authentication' => "code='{$merchant_id}' date='{$gm_date}' hash='{$hash}'",
            ],
        ]);

        dd($response);

        // Check payment intent status
        if ($paymentIntent->status === 'succeeded') {
            return [
                'success' => true,
                'message' => 'Payment processed successfully',
                'transaction_id' => $paymentIntent->id,
                'data' => $paymentIntent->toArray(),
                'status' => 'completed',
            ];
        }

        // Handle other payment intent statuses
        if ($paymentIntent->status === 'requires_action' && $paymentIntent->next_action &&
            $paymentIntent->next_action->type === 'redirect_to_url') {
            return [
                'success' => true,
                'message' => 'Redirect required to complete payment',
                'transaction_id' => $paymentIntent->id,
                'redirect_url' => $paymentIntent->next_action->redirect_to_url->url,
                'data' => $paymentIntent->toArray(),
                'status' => 'processing',
            ];
        }

        return [
            'success' => false,
            'message' => 'Payment processing failed',
            'transaction_id' => $paymentIntent->id ?? null,
            'data' => $paymentIntent->toArray(),
            'status' => 'failed',
        ];
        //        } catch (\Exception $e) {
        //            Log::error('Payment Gateway Error: ' . $e->getMessage(), [
        //                'gateway' => $this->getIdentifier(),
        //                'payment_data' => $paymentData // Consider removing sensitive data
        //            ]);
        //
        //            return [
        //                'success' => false,
        //                'message' => $e->getMessage(),
        //                'status' => 'failed',
        //            ];
        //        }
    }

    /**
     * Handle webhook notifications from the payment gateway
     */
    public function handleWebhook(Request $request): array
    {
        $payload = $request->getContent();
        $sigHeader = $request->header('TwoCheckout-Signature');

        try {
            // $event = \TwoCheckout\Webhook::constructEvent(
            //     $payload, $sigHeader, $this->config['webhook_secret'] ?? ''
            // );
            // Handle the event
            switch ($event->type) {
                case 'payment_intent.succeeded':
                    $paymentIntent = $event->data->object;

                    // Handle successful payment
                    return [
                        'success' => true,
                        'message' => 'Payment succeeded',
                        'transaction_id' => $paymentIntent->id,
                        'data' => $event->toArray(),
                        'status' => $this->mapStatus($paymentIntent->status),
                    ];

                case 'payment_intent.payment_failed':
                    $paymentIntent = $event->data->object;

                    // Handle failed payment
                    return [
                        'success' => false,
                        'message' => 'Payment failed',
                        'transaction_id' => $paymentIntent->id,
                        'data' => $event->toArray(),
                        'status' => $this->mapStatus($paymentIntent->status),
                    ];

                default:
                    // Handle other event types
                    return [
                        'success' => true,
                        'message' => 'Webhook received',
                        'data' => $event->toArray(),
                        'status' => 'processing',
                    ];
            }
        } catch (\Exception $e) {
            Log::error('TwoCheckout Webhook Error: '.$e->getMessage());

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'status' => 'failed',
            ];
        }
    }

    /**
     * Verify the payment status
     */
    public function verifyPayment(string $paymentId): array
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentId);

            if ($paymentIntent->status === 'succeeded') {
                return [
                    'success' => true,
                    'message' => 'Payment verified',
                    'transaction_id' => $paymentId,
                    'data' => $paymentIntent->toArray(),
                    'status' => 'completed',
                ];
            }

            return [
                'success' => false,
                'message' => 'Payment not completed',
                'transaction_id' => $paymentId,
                'data' => $paymentIntent->toArray(),
                'status' => $paymentIntent->status,
            ];
        } catch (ApiErrorException $e) {
            Log::error('TwoCheckout Verification Error: '.$e->getMessage());

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'status' => 'failed',
            ];
        }
    }

    /**
     * Get frontend scripts required by this gateway
     */
    public function getScripts(): array
    {
        return [
            'https://2pay-js.2checkout.com/v1/2pay.js',
        ];
    }
}
