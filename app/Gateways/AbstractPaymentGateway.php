<?php

namespace Modules\Payment\Gateways;

use Illuminate\Http\Request;
use Modules\Payment\Contracts\PaymentGatewayInterface;
use Modules\Payment\Models\Payment;

abstract class AbstractPaymentGateway implements PaymentGatewayInterface
{
    /**
     * Configuration for the payment gateway
     */
    protected array $config;

    /**
     * AbstractPaymentGateway constructor.
     */
    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * Check if the payment gateway is enabled
     */
    public function isEnabled(): bool
    {
        return $this->config['enabled'] ?? false;
    }

    /**
     * Get the icon/logo of the payment gateway
     */
    public function getIcon(): ?string
    {
        return $this->config['icon'] ?? null;
    }

    /**
     * Get frontend scripts required by this gateway
     */
    public function getScripts(): array
    {
        return $this->config['scripts'] ?? [];
    }

    /**
     * Get frontend styles required by this gateway
     */
    public function getStyles(): array
    {
        return $this->config['styles'] ?? [];
    }

    /**
     * Handle webhook notifications from the payment gateway
     */
    public function handleWebhook(Request $request): array
    {
        return [
            'success' => false,
            'message' => 'Webhook not implemented for this gateway',
        ];
    }

    protected function mapStatus(string $status): string
    {
        // Map the status from the payment gateway to a more generic status
        return match ($status) {
            'paid', 'SUCCESS', 'success', 'COMPLETE', 'PAID' => Payment::STATUS_COMPLETED,
            'unpaid', 'processing', 'PENDING' => Payment::STATUS_PENDING,
            'failed', 'cancelled', 'FAILED', 'CANCELLED' => Payment::STATUS_FAILED,
            default => $status,
        };
    }
}
