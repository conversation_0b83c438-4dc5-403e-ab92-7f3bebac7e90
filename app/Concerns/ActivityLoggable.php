<?php

namespace Modules\ActivityLogs\Concerns;

use Illuminate\Support\Facades\Auth;
use Modules\ActivityLogs\Models\ActivityLog;

trait ActivityLoggable
{
    public static function bootActivityLoggable()
    {
        static::created(function ($model) {
            static::logActivity($model, 'created');
        });

        static::updated(function ($model) {
            static::logActivity($model, 'updated', $model->getOriginal());
        });

        static::deleted(function ($model) {
            static::logActivity($model, 'deleted');
        });
    }

    protected static function logActivity($model, $event, $oldValues = null)
    {
        ActivityLog::create([
            'causer_id' => Auth::id(),
            'causer_type' => get_class(Auth::user()),
            'event' => $event,
            'model_type' => get_class($model),
            'model_id' => $model->id,
            'old_values' => $oldValues,
            'new_values' => $model->toArray(),
            'ip_address' => request()->ip(),
        ]);
    }
}
