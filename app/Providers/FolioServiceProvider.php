<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use <PERSON><PERSON>\Folio\Folio;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class FolioServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        if (! app()->runningInConsole()) {

            Folio::path(resource_path('views/pages'));
            try {
                Folio::path(resource_path('views/pages'))->middleware([
                    '*' => [
                        'locale:'.generalSetting()->site_locale,
                    ],
                ]);
                $theme_path_to_folio = config('theme.path', base_path('themes'))
                    .DIRECTORY_SEPARATOR
                    .config('app.site_theme')
                    .DIRECTORY_SEPARATOR
                    .'views'
                    .DIRECTORY_SEPARATOR
                    .'pages';
                Folio::path(dir($theme_path_to_folio)->path)
                    ->middleware([
                        '*' => [
                            'web',
                            'theme:'.config('app.site_theme'),
                            'locale:'.generalSetting()->site_locale,
                        ],
                    ]);
            } catch (NotFoundExceptionInterface|ContainerExceptionInterface $e) {
                dd($e->getMessage()); // todo Theme not found, skip it.
            }
        }

        //        dd(Folio::paths());
    }
}
