<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Livewire\Volt\Volt;

class VoltServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        if (! app()->runningInConsole()) {
            Volt::mount([
                config('livewire.view_path', resource_path('views/livewire')),
                resource_path('views/pages'),
                base_path('themes/'.config('app.site_theme').'/views/livewire'),
                base_path(path: 'themes/'.config('app.site_theme').'/views/pages'),
            ]);

            // Register the Volt routes for Modules
            $modules = base_path('Modules');
            $modules = array_filter(glob($modules.'/*'), 'is_dir');
            foreach ($modules as $module) {
                $moduleName = basename($module);
                $voltPath = $module.'/resources/views';
                Volt::mount([
                    $voltPath,
                ]);
            }
        }
    }
}
