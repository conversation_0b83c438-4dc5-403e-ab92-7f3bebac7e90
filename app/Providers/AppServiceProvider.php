<?php

namespace App\Providers;

use Carbon\Carbon;
use DateTime;
use Illuminate\Database\Connection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\ServiceProvider;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use URL;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void {}

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Schema::defaultStringLength(191);

        setlocale(LC_TIME, 'ar_SA');

        Carbon::setLocale('ar_SA');

        // temporary signed url for images and pdf's files
        Storage::disk('local')->buildTemporaryUrlsUsing(function ($path, DateTime $expiration, $options) {
            return URL::temporarySignedRoute(
                'local.temp',
                $expiration,
                array_merge($options, ['path' => $path])
            );
        });

        Model::preventAccessingMissingAttributes(! $this->app->isProduction());
        Model::preventSilentlyDiscardingAttributes(! $this->app->isProduction());
        Model::preventLazyLoading(! $this->app->isProduction());
        Model::shouldBeStrict(! $this->app->isProduction());
        if ($this->app->isProduction()) {
            Model::handleLazyLoadingViolationUsing(function (
                $model,
                $relation
            ) {
                $class = get_class($model);

                info(
                    "Attempted to lazy load [{$relation}] on model [{$class}]."
                );
            });
        }

        if ($this->app->isProduction()) {
            $this->app['request']->server->set('HTTPS', true);
            \URL::forceScheme('https');
        }

        // spatie media library, show default image if not exists

        // if ($this->app->isLocal() && ! $this->app->runningInConsole()) {
        //     DB::listen(function (QueryExecuted $event) {
        //         if ($event->time > 250) {
        //             throw new QueryException(
        //                 'db',
        //                 $event->sql,
        //                 $event->bindings,
        //                 new \Exception('Individual database query exceeded '.$event->time.'ms.')
        //             );
        //         }
        //     });

        //     DB::whenQueryingForLongerThan(2000, function (Connection $connection) {
        //         \Log::warning("Database queries exceeded 2 seconds on {$connection->getName()}");
        //     });
        // }
    }
}
