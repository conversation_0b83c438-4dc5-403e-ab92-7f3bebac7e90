<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Modules\Core\Http\Middleware\SetLocaleMiddleware;
use Modules\Institute\Filament\InstitutePluginFilament;
use Modules\Institute\Filament\Pages\Auth\RegisterInstitute;
use Modules\Institute\Filament\Pages\Auth\UserRegister;
use Modules\Institute\Models\Institute;

class InstitutePanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('institute')
            ->path('auth')
            ->tenantRoutePrefix('institute')
            ->tenant(Institute::class, 'slug', ownershipRelationship: 'institute')
            ->brandLogo(url('logo.svg'))->brandLogoHeight('3.5rem')
            ->passwordReset()
            ->emailVerification()
            ->topNavigation()
            ->darkMode(false)
            ->profile()
            ->login()
            ->homeUrl('/')
            ->font(config('app.font', 'IBM Plex Sans Arabic'))
            ->registration()
            ->tenantRegistration(RegisterInstitute::class)
            ->viteTheme('resources/css/filament/institute/theme.css')
            ->registration(UserRegister::class)
            ->login()
            ->tenantMenu(false)
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Institute/Resources'), for: 'App\\Filament\\Institute\\Resources')
            ->discoverPages(in: app_path('Filament/Institute/Pages'), for: 'App\\Filament\\Institute\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Institute/Widgets'), for: 'App\\Filament\\Institute\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
            ])
            ->plugin(new InstitutePluginFilament)
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                SetLocaleMiddleware::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
    }
}
