<?php

namespace App\Providers\Filament;

use App\Filament\FilamentModulesPlugin;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Field;
use Filament\Forms\Components\Placeholder;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Infolists\Components\Entry;
use Filament\Navigation\NavigationGroup;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\SpatieLaravelTranslatablePlugin;
use Filament\Support\Colors\Color;
use Filament\Support\Concerns\Configurable;
use Filament\Support\Facades\FilamentView;
use Filament\Tables\Columns\Column;
use Filament\Tables\Filters\BaseFilter;
use Filament\View\PanelsRenderHook;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Modules\Core\Filament\Auth\Login;
use Modules\Course\Filament\Widgets\StatsOverview;
use Modules\Course\Filament\Widgets\VisitCoursesChart;
use Modules\Setting\Settings\GeneralSettings;
use Throwable;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->font(config('app.font', 'IBM Plex Sans Arabic'))
            ->darkMode(false)
            ->id('admin')
            ->brandLogo(url('logo.svg'))->brandLogoHeight('3.5rem')
            ->path('admin')
            ->authGuard('admin')
            ->login(Login::class)
            ->passwordReset()
            ->brandName(__('Arabic for all'))
            ->emailVerification()
            ->databaseNotifications()
            ->profile()
//            ->breadcrumbs(false)
            ->sidebarCollapsibleOnDesktop()
            ->favicon(config('app.favicon'))
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                //                Widgets\AccountWidget::class,
                StatsOverview::class,
                VisitCoursesChart::class,
            ])
            ->plugins([
                SpatieLaravelTranslatablePlugin::make()->defaultLocales(array_keys(config('app.locales'))),
            ])

            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->plugin(new FilamentModulesPlugin)
            ->bootUsing(function (Panel $panel) {
                try {
                    $logotypeSetting = app(GeneralSettings::class)->get('site_logo');
                    //                    dd($logotypeSetting);
                    $logotypeUrl = ! blank($logotypeSetting) ? Storage::url($logotypeSetting) : asset('logo.svg');
                } catch (Throwable $_) {
                    $logotypeUrl = asset('logo.svg');
                }
                $panel->brandLogo($logotypeUrl);
            })
            ->navigationGroups([
                NavigationGroup::make(__('Subscriptions & Plans'))->collapsed()->icon('heroicon-o-map'),
                NavigationGroup::make(__('Payments'))->collapsed()->icon('heroicon-o-currency-dollar'),
                NavigationGroup::make(__('Blog'))->collapsed()->icon('heroicon-o-newspaper'),
                NavigationGroup::make(__('Settings'))->collapsed()->icon('heroicon-o-cog-6-tooth'),
                NavigationGroup::make(__('Roles and Permissions'))->collapsed()->icon('heroicon-o-shield-check'),
            ])
            ->databaseTransactions()

            ->authMiddleware([
                Authenticate::class,
            ]);
    }

    // boot
    public function boot(): void
    {
        FilamentView::registerRenderHook(
            PanelsRenderHook::USER_MENU_BEFORE,
            function (): string {
                return Blade::render('<a target="_blank" href="{{ url(\'/\') }}" class="block px-4 py-2 text-sm text-gray-500" role="menuitem"><x-heroicon-o-globe-alt class="h-5 w-5" /></a>');
            }
        );
        // // Translate all labels
        // Component::configureUsing(function (Component $component) {
        //     $this->addKeyToTranslateJsonFile($component->getLabel());
        //     $component->translateLabel();
        // });
        // // Translate all labels
        // Actions::configureUsing(function (Actions $component) {
        //     $this->addKeyToTranslateJsonFile($component->getLabel());
        //     $component->translateLabel();
        // });

        // Column::configureUsing(function (Column $column) {
        //     $this->addKeyToTranslateJsonFile($column->getLabel());
        //     $column->translateLabel();
        // });
        Pages\BasePage::stickyFormActions();

        foreach ([Field::class, BaseFilter::class, Placeholder::class, Column::class, Entry::class] as $component) {
            /* @var Configurable $component */
            $component::configureUsing(function ($translatable): void {
                /** @phpstan-ignore method.notFound */
                $this->addKeyToTranslateJsonFile($translatable->getLabel());
                $translatable->translateLabel();
            });
        }

    }

    private function addKeyToTranslateJsonFile($getLabel): void
    {
        // loop through all the files in the resources/lang directory
        $files = glob(base_path('lang/*.json'));
        foreach ($files as $file) {

            if (File::exists($file)) {
                // Read the existing file and decode JSON to an array
                $words = json_decode(File::get($file), true);

                // Ensure $words is a valid array
                if (! is_array($words)) {
                    $words = [];
                }
            } else {
                // Initialize an empty array if the file doesn't exist
                $words = [];
            }

            if (array_key_exists($getLabel, $words)) {
                continue;
            }
            $words[$getLabel] = $getLabel;
            File::put($file, json_encode($words, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        }

    }
}
