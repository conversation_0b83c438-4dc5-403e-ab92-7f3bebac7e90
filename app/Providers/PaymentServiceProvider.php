<?php

namespace Modules\Payment\Providers;

use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider;
use Livewire\Livewire;
use Modules\Payment\Contracts\PaymentGatewayInterface;
use Modules\Payment\Livewire\PaymentMethodSelector;
use Modules\Payment\Services\PaymentGatewayManager;

class PaymentServiceProvider extends ServiceProvider
{
    /**
     * @var string
     */
    protected $moduleName = 'Payment';

    /**
     * @var string
     */
    protected $moduleNameLower = 'payment';

    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path($this->moduleName, 'database/migrations'));

        // Register payment gateways
        $this->registerPaymentGateways();
        // Register payment gateway plugins
        $this->registerPaymentGatewayPlugins();
        // Register payment gateway routes
        Livewire::component('payment::payment-method-selector', PaymentMethodSelector::class);

    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);

        $this->app->singleton(PaymentGatewayManager::class, function ($app) {
            return new PaymentGatewayManager($app);
        });

        $this->app->alias(PaymentGatewayManager::class, 'payment.gateway');
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/'.$this->moduleNameLower);

        $sourcePath = module_path($this->moduleName, 'resources/views');

        $this->publishes([
            $sourcePath => $viewPath,
        ], ['views', $this->moduleNameLower.'-module-views']);

        $this->loadViewsFrom(array_merge($this->getPublishableViewPaths(), [$sourcePath]), $this->moduleNameLower);
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/'.$this->moduleNameLower);

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, $this->moduleNameLower);
        } else {
            $this->loadTranslationsFrom(module_path($this->moduleName, 'lang'), $this->moduleNameLower);
        }
    }

    /**
     * Register config.
     *
     * @return void
     */
    public function registerConfig()
    {
        $this->publishes([
            module_path($this->moduleName, 'config/config.php') => config_path($this->moduleNameLower.'.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path($this->moduleName, 'config/config.php'), $this->moduleNameLower
        );

        // register configs in plugins that have a payment.php file inside config folder
        $pluginsPath = base_path('plugins');
        if (! File::exists($pluginsPath)) {
            return;
        }
        $directories = File::directories($pluginsPath);
        foreach ($directories as $directory) {
            $configPath = $directory.'/config/payment.php';
            if (! File::exists($configPath)) {
                continue;
            }
            $pluginName = basename($directory);
            $this->mergeConfigFrom($configPath, 'payment.gateways');
        }

    }

    /**
     * Register the built-in payment gateways.
     */
    protected function registerPaymentGateways(): void
    {
        $gatewayPath = module_path($this->moduleName, 'app/Gateways');
        $namespace = 'Modules\\Payment\\Gateways\\';

        $this->registerGatewaysFromPath($gatewayPath, $namespace);
    }

    /**
     * Register payment gateway plugins.
     *
     * @return void
     */
    protected function registerPaymentGatewayPlugins()
    {
        $pluginsPath = base_path('plugins');

        if (! File::exists($pluginsPath)) {
            return;
        }

        $directories = File::directories($pluginsPath);
        foreach ($directories as $directory) {
            $gatewayPath = $directory.'/src/Gateways';

            if (! File::exists($gatewayPath)) {
                continue;
            }

            $pluginName = basename($directory);

            $namespace = "Plugins\\{$pluginName}\\Gateways\\";
            //            dd($namespace);
            $this->registerGatewaysFromPath($gatewayPath, $namespace);
        }
    }

    /**
     * Register gateways from a specific path.
     *
     * @return void
     */
    protected function registerGatewaysFromPath(string $path, string $namespace)
    {
        if (! File::exists($path)) {
            return;
        }

        $files = File::files($path);

        foreach ($files as $file) {
            if ($file->getExtension() !== 'php') {
                continue;
            }

            $className = $namespace.$file->getFilenameWithoutExtension();

            // exclude abstract classes
            if (str_starts_with($file->getFilenameWithoutExtension(), 'Abstract')) {
                continue;
            }
            //            if ($path == "/Users/<USER>/Herd/arabic/plugins/PayPal/src/Gateways")   {
            //                dd(class_exists($className));
            //            }

            if (class_exists($className) &&
                is_subclass_of($className, PaymentGatewayInterface::class) &&
                ! is_a($className, 'Abstract', true)) {

                $this->app->tag($className, ['payment.gateway']);
            }
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }

    private function getPublishableViewPaths(): array
    {
        $paths = [];
        foreach (config('view.paths') as $path) {
            if (is_dir($path.'/modules/'.$this->moduleNameLower)) {
                $paths[] = $path.'/modules/'.$this->moduleNameLower;
            }
        }

        return $paths;
    }
}
