<?php

// Modules/AI/Services/TogetherAIService.php

namespace Modules\AI\Services\AI;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Modules\AI\Contracts\AIServiceInterface;

class TogetherAIService implements AIServiceInterface
{
    private $client;

    public function __construct(Client $client)
    {
        $this->client = $client;
    }

    public function chat(string $message, ?string $model = 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free'): mixed
    {

        // deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free
        // meta-llama/Llama-Vision-Free
        // meta-llama/Llama-3.3-70B-Instruct-Turbo-Free

        try {
            $apiKey = config('services.together.api_key');
            $response = $this->client->post('https://api.together.xyz/v1/chat/completions', [
                'headers' => [
                    'Authorization' => 'Bearer '.$apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => $model,
                    'messages' => [
                        ['role' => 'user', 'content' => $message],
                    ],
                ],
            ]);
            $data = json_decode($response->getBody()->getContents(), true);

            return $data['choices'][0]['message']['content'];
        } catch (GuzzleException $e) {
            // Handle the exception as needed
            // For example, log the error or return a default response
            \Log::error('Error in Together AI API: '.$e->getMessage());
            abort(500, 'Error processing your request.');
        } catch (\Exception $e) {
            \Log::error('General error in Together AI API: '.$e->getMessage());
            abort(500, 'Error processing your request.');
        }
    }

    public function completion(array $options): mixed
    {
        $apiKey = config('services.together.api_key');

        $response = $this->client->post('https://api.together.xyz/v1/completions', [
            'headers' => [
                'Authorization' => 'Bearer '.$apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => $options,
        ]);

        $data = json_decode($response->getBody()->getContents(), true);

        return $data['choices'][0]['text'];
    }
}
