<?php

namespace Modules\AI\Services\AI;

use Illuminate\Support\Facades\Log;
use Modules\AI\Contracts\AIServiceInterface;
use OpenAI\Exceptions\ErrorException;
use OpenAI\Laravel\Facades\OpenAI;

class OpenAIService implements AIServiceInterface
{
    public function chat(string $message, ?string $model = 'gpt-3.5-turbo'): mixed
    {
        try {
            $result = OpenAI::chat()->create([
                'model' => $model ?? 'gpt-3.5-turbo',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are an expert tour and travel guide specializing in Egypt.Your objective is to provide detailed and engaging information to travelers seeking to explore the rich history, culture, and attractions of Egypt.',
                    ],
                    ['role' => 'user', 'content' => $message],
                ],
            ]);

            return $result->choices[0]->message->content;
        } catch (ErrorException $e) {
            Log::error('Error in OpenAI API: '.$e->getMessage());
            abort(500, 'Error processing your request.');
        }

    }

    public function completion(array $options): mixed
    {
        return OpenAI::completions()->create($options);
    }
}
