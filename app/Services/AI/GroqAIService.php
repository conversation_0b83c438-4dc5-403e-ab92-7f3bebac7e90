<?php

namespace Modules\AI\Services\AI;

use Log;
use LucianoTonet\GroqLaravel\Facades\Groq;
use LucianoTonet\GroqPHP\GroqException;
use Modules\AI\Contracts\AIServiceInterface;

class GroqAIService implements AIServiceInterface
{
    public function chat(string $message, ?string $model = 'llama-3.3-70b-versatile'): mixed
    {
        try {
            $response = Groq::chat()->completions()->create([
                'model' => $model ?? 'llama-3.3-70b-versatile',  // Check available models at console.groq.com/docs/models
                'messages' => [
                    ['role' => 'system', 'content' => 'You are an expert tour and travel guide specializing in Egypt.Your objective is to provide detailed and engaging information to travelers seeking to explore the rich history, culture, and attractions of Egypt.'],
                    ['role' => 'user', 'content' => $message],
                ],
            ]);

            return $response['choices'][0]['message']['content'];
        } catch (GroqException $e) {
            Log::error('Error in Groq API: '.$e->getMessage());
            abort(500, 'Error processing your request.');
        }
    }

    public function completion(array $options): mixed
    {
        // TODO: Implement completion() method.
    }
}
