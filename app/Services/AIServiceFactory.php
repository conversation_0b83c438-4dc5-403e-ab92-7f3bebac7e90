<?php

namespace Modules\AI\Services;

use Illuminate\Support\Facades\App;
use InvalidArgumentException;
use Modules\AI\Contracts\AIServiceInterface;
use Modules\AI\Services\AI\DeepseekAIService;
use Modules\AI\Services\AI\GeminiAIService;
use Modules\AI\Services\AI\GroqAIService;
use Modules\AI\Services\AI\OpenAIService;
use Modules\AI\Services\AI\PerplexityAIService;
use Modules\AI\Services\AI\TogetherAIService;

class AIServiceFactory
{
    public static function make(?string $provider = null): AIServiceInterface
    {
        $provider = $provider ?? config('services.ai.default_provider');

        return match ($provider) {
            'openai' => App::make(OpenAIService::class),
            'groq' => App::make(GroqAIService::class),
            'together' => App::make(TogetherAIService::class),
            'deepseek' => App::make(DeepseekAIService::class),
            'gemini' => App::make(GeminiAIService::class),
            'perplexity' => App::make(PerplexityAIService::class),
            default => throw new InvalidArgumentException("Unsupported AI provider: {$provider}"),
        };
    }
}
