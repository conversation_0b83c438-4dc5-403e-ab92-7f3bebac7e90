<?php

namespace Modules\Core\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Modules\Core\Models\Localization\Currency;

readonly class CurrencyService
{
    public function formatPrice(float $amount, string $fromCurrency = 'USD'): string
    {
        $fromCurrency = config('core.localization.base_currency') ?? $fromCurrency;
        $siteCurrency = generalSetting()->currency;
        $convertedAmount = $this->convertCurrency(
            $amount,
            $fromCurrency,
            $siteCurrency
        );

        return $this->format($convertedAmount, $siteCurrency);
    }

    private function convertCurrency(float $amount, string $fromCurrency, string $toCurrency): ?float
    {
        //        dd($amount, $fromCurrency, $toCurrency);

        if ($fromCurrency === $toCurrency) {
            return $amount;
        }

        $rates = self::getExchangeRates($fromCurrency);

        if (! $rates || ! isset($rates[$toCurrency])) {
            return null;
        }

        return ($amount / $rates[$toCurrency]['crate']) * 1.12;
    }

    private static function getExchangeRates(string $baseCurrency = 'USD', int $cacheDuration = 60): ?array
    {
        // Create a unique cache key
        $cacheKey = "2checkout_exchange_rates_{$baseCurrency}";

        // Check if rates are cached
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        try {
            // Fetch exchange rates from 2Checkout
            $response = Http::get('https://secure.2checkout.com/content/exchange-json.php', [
                'CURRENCY' => $baseCurrency,
            ]);

            // Check if request was successful
            if ($response->successful()) {
                $rates = $response->json();

                // Cache the rates
                Cache::put($cacheKey, $rates, now()->addMinutes($cacheDuration));

                return $rates;
            }

            // Log error if request fails
            \Log::error('2Checkout Exchange Rate Fetch Failed', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);

            return null;
        } catch (\Exception $e) {
            // Log any exceptions
            \Log::error('2Checkout Exchange Rate Fetch Error', [
                'message' => $e->getMessage(),
            ]);

            return null;
        }
    }

    private function format(float $amount, string $currency): string
    {
        $format = Currency::where('iso', $currency)->first();

        $formatted = number_format($amount, 2);

        return $format->position === 'before'
            ? "{$format->symbol}{$formatted}"
            : "{$formatted}{$format->symbol}";
    }

    private function getExchangeRate(string $fromCurrency, string $toCurrency): ?float
    {
        $rates = self::getExchangeRates($fromCurrency);

        return $rates[$toCurrency]['crate'] ?? null;
    }
}
