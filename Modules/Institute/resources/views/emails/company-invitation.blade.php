<x-mail::message>
    {{ __('You have been invited to join the :company company!', ['company' => $invitation->institute->name]) }}

    @if (filament()->getRegistrationUrl())
        {{ __('If you do not have an account, you may create one by clicking the button below. After creating an account, you may click the invitation acceptance button in this email to accept the company invitation:') }}

        <x-mail::button :url="url(filament()->getRegistrationUrl())">
            {{ __('Create Account') }}
        </x-mail::button>


        {{ __('If you already have an account, you may accept this invitation by clicking the button below:') }}

    @else
        {{ __('You may accept this invitation by clicking the button below:') }}
    @endif

    <x-mail::button :url="$url">
        {{ __('Accept Invitation') }}
    </x-mail::button>

    {{ __('If you did not expect to receive an invitation to this company, you may discard this email.') }}

    Thanks,<br>
    {{ config('app.name') }}
</x-mail::message>

