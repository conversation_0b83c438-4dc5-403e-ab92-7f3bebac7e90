<?php

namespace Modules\Institute\Concerns;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum UserTypes: int implements HasColor, HasLabel
{
    case STUDENT = 1;
    case TEACHER = 2;
    case INSTITUTION = 3;

    public function getLabel(): string
    {
        return match ($this) {
            self::STUDENT => __('Student'),
            self::TEACHER => __('Teacher'),
            self::INSTITUTION => __('Institution'),
        };
    }

    public function getColor(): string|array|null
    {
        return 'danger';
    }
}
