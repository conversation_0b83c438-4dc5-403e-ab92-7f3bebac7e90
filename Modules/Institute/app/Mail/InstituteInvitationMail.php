<?php

namespace Modules\Institute\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\URL;
use Modules\Institute\Models\InstituteInvitation;

class InstituteInvitationMail extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(
        public InstituteInvitation $invitation
    ) {}

    //    public function envelope(): Envelope
    //    {
    //        return new Envelope(
    //            subject: 'Institute Invitation',
    //        );
    //    }

    public function content(): Content
    {
        $acceptUrl = URL::signedRoute('filament.institute.invitations.accept', ['invitationId' => $this->invitation->id]);

        return new Content(
            markdown: 'acl::emails.company-invitation',
            with: [
                'invitation' => $this->invitation,
                'url' => $acceptUrl,
            ]
        );
    }

    //    public function attachments(): array
    //    {
    //        return [];
    //    }
}
