<?php

namespace Modules\Institute\Filament\Institute\Resources\StudentResource\Pages;

use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Mail;
use Modules\Institute\Concerns\UserTypes;
use Modules\Institute\Filament\Institute\Resources\StudentResource;
use Modules\Institute\Mail\InstituteInvitationMail;

class ListStudents extends ListRecords
{
    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //            Actions\CreateAction::make(),
            Actions\Action::make('invite')
                ->label(__('Invite'))
                ->form([
                    TextInput::make('email')
                        ->label(__('User Email'))
                        ->required(),
                    Select::make('type')
                        ->label(__('User Type'))
                        ->options(UserTypes::class)
                        ->required(),
                ])
                ->action(function (array $data): void {
                    $invitation = Filament::getTenant()->institute_invitations()->create([
                        'email' => $data['email'],
                        'type' => $data['type'],
                    ]);
                    Mail::to($data['email'])->send(new InstituteInvitationMail($invitation));
                }),
        ];
    }

    public function getTabs(): array
    {
        return [
            'Students' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->whereType(UserTypes::STUDENT->value)),
            'Teachers' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->whereType(UserTypes::TEACHER->value)),
        ];
    }
}
