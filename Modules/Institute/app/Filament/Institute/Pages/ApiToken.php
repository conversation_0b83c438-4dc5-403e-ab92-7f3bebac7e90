<?php

namespace Modules\Institute\Filament\Institute\Pages;

use Filament\Actions\Action;
use Filament\Forms;
use Filament\Pages\Page;
use Filament\Tables;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class ApiToken extends Page implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;

    public ?string $token = null;

    protected static ?string $navigationIcon = 'heroicon-o-finger-print';

    protected static string $view = 'filament.institute.pages.api-token';

    protected function getTableQuery(): Builder
    {
        session()->flash('sanctum-token', $this->token);

        return Auth::user()->tokens()->getQuery();
    }

    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('name')
                ->label(trans('Name'))
                ->sortable()
                ->searchable(),
            Tables\Columns\TextColumn::make('abilities')->badge()
                ->label(trans('Abilities')),
            Tables\Columns\TextColumn::make('last_used_at')
                ->label(trans('Last used at'))
                ->dateTime()
                ->sortable(),
            Tables\Columns\TextColumn::make('created_at')
                ->label(trans('Created at'))
                ->dateTime()
                ->sortable(),
        ];
    }

    protected function getActions(): array
    {
        return [
            Action::make('new')
                ->label(trans('Create a new Token'))
                ->action(function (array $data) {
                    $user = Auth::user();
                    $token = $user->createToken($data['name'], $data['abilities'])->plainTextToken;
                    $this->token = $token;
                    session()->flash('sanctum-token', $token);
                })
                ->form([
                    Forms\Components\TextInput::make('name')
                        ->label(trans('Token Name'))
                        ->required(),
                    Forms\Components\CheckboxList::make('abilities')
                        ->label(trans('Abilities'))
                        ->options([
                            'test' => 'Test',
                        ])
                        ->columns(4),
                ]),
        ];
    }

    protected function getTableBulkActions(): array
    {
        return [
            BulkAction::make('revoke')
                ->label(trans('Revoke'))
                ->action(fn (Collection $records) => $records->each->delete())
                ->deselectRecordsAfterCompletion()
                ->requiresConfirmation()
                ->color('danger')
                ->icon('heroicon-o-trash'),
        ];
    }
}
