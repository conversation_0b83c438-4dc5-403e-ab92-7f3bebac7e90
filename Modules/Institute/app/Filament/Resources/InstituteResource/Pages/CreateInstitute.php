<?php

namespace Modules\Institute\Filament\Resources\InstituteResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Modules\Institute\Filament\Resources\InstituteResource;

class CreateInstitute extends CreateRecord
{
    protected static string $resource = InstituteResource::class;

    protected function getHeaderActions(): array
    {
        return [

        ];
    }

    protected function afterCreate(): void
    {
        $this->record->user->institute()->attach($this->record->id);
    }
}
