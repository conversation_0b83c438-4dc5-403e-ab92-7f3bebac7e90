<?php

namespace Modules\Institute\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class InstitutePluginFilament implements Plugin
{
    public function getId(): string
    {
        return 'institute';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverResources(
            in: __DIR__.'/Resources',
            for: 'Modules\\Institute\\Filament\\Institute\\Resources'
        );
    }

    public function boot(Panel $panel): void {}
}
