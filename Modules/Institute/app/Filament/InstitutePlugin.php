<?php

namespace Modules\Institute\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class InstitutePlugin implements Plugin
{
    public function getId(): string
    {
        return 'institute';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverResources(in: __DIR__.'/Resources', for: 'Modules\\Institute\\Filament\\Resources');
        $panel->discoverPages(in: __DIR__.'/Pages', for: 'Modules\\Institute\\Filament\\Pages');
    }

    public function boot(Panel $panel): void {}
}
