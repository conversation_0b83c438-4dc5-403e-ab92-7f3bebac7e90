<?php

namespace Modules\Institute\Filament\Pages\Auth;

use App\Models\User;
use Filament\Forms;
use Filament\Pages\Auth\Register;
use Modules\Affiliate\Services\AffiliateService;
use Modules\Core\Models\Localization\City;
use Modules\Core\Models\Localization\Country;
use Modules\Institute\Concerns\UserTypes;
use Modules\Subscription\Services\SubscriptionService;

class UserRegister extends Register
{
    public ?string $type = null;

    public function beforeFill(): void
    {
        $this->type = request()->get('type');
    }

    //    protected static string $view = 'acl::filament.pages.auth.user-register';
    protected ?string $maxWidth = '2xl';

    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([

                        Forms\Components\Group::make()
                            ->columns(1)
                            ->schema([
                                Forms\Components\TextInput::make('username')

                                    ->required(),
                                $this->getNameFormComponent(),
                                $this->getEmailFormComponent(),
                                $this->getPasswordFormComponent(),
                                $this->getPasswordConfirmationFormComponent(),
                                Forms\Components\TextInput::make('phone')
                                    ->required()

                                    ->tel()
                                    ->maxLength(255),
                            ]),
                        Forms\Components\Group::make()
                            ->columns(2)
                            ->schema([
                                Forms\Components\Select::make('country')
                                    ->reactive()
                                    ->dehydrated()
                                    ->columnSpanFull()
                                    ->options(Country::all()->pluck('name', 'name')->toArray())
                                    ->afterStateUpdated(fn (callable $set) => $set('parent_id', null))
                                    ->searchable(),

                                //                                Forms\Components\Select::make('city_id')
                                //
                                //                                    ->reactive()
                                //                                    ->options(fn (callable $get) => City::whereStateId($get('country'))?->pluck('name', 'id')->toArray())
                                //                                    ->disabled(fn (callable $get) => !$get('country')),
                                //                                Forms\Components\TextInput::make('about_me')
                                //                                    ->columnSpan(2),

                                Forms\Components\Select::make('gender')

                                    ->options(['male' => __('Male'), 'female' => __('Female')])
                                    ->columnSpan(2)
                                    ->required(),
                            ]),

                    ])->statePath('data')
            )];
    }

    protected function mutateFormDataBeforeRegister(array $data): array
    {
        $type = $this->type === 'institute' ? UserTypes::INSTITUTION->value : null;
        //        dd($type);

        return [
            ...$data,
            'type' => $type,
        ];
    }

    public function afterRegister()
    {

        if (session()->has('affiliate_code')) {
            //            dd('here');
            app(AffiliateService::class)->trackReferral(
                session('affiliate_code'),
                $this->form->getModelInstance()->id
            );
        }
        // Assign free plan
        app(SubscriptionService::class)->assignFreePlan($this->form->getModelInstance());

    }
}
