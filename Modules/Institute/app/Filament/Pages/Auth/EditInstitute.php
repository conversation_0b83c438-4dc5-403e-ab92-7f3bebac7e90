<?php

namespace Modules\Institute\Filament\Pages\Auth;

use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Facades\Filament;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

class EditInstitute extends Page implements HasActions, HasForms
{
    use InteractsWithActions, InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'acl::filament.pages.auth.edit-institute';

    protected static bool $shouldRegisterNavigation = false;

    public function mount()
    {
        $this->form->fill(Filament::getTenant()->toArray());
    }

    protected function getFormStatePath(): ?string
    {
        return 'institute';
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make('General')
                ->inlineLabel()
                ->schema([
                    TextInput::make('name'),
                ]),
        ];
    }

    public function submitAction(): Action
    {
        return Action::make('submit')
            ->label(__('Save'))
            ->action('submit');
    }

    public function submit()
    {
        Filament::getTenant()->update($this->form->getState());

        Notification::make()
            ->success()
            ->title('Institute')
            ->body('Changes saved')
            ->send();
    }
}
