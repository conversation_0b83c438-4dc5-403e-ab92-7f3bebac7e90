<?php

namespace Modules\Institute\Http\Controllers;

use App\Models\User;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Livewire\Features\SupportRedirects\Redirector;
use Modules\Institute\Models\InstituteInvitation;

class InstituteInvitationController
{
    public function accept(Request $request, int $invitationId): Redirector|RedirectResponse|null
    {

        $invitation = InstituteInvitation::whereKey($invitationId)->firstOrFail();
        $user = User::where('email', $invitation->email)->first();

        if ($user) {
            $user->institute_id = $invitation->institute_id;
            $user->save();
        } else {
            $user = User::create([
                'name' => $invitation->name,
                'email' => $invitation->email,
                'password' => bcrypt(Str::random(8)),
                'institute_id' => $invitation->institute_id,
            ]);
        }
        $invitation->delete();

        $notification = Notification::make()->title(Str::inlineMarkdown(__('Accepted')))->success()->persistent()->send();

        if ($user) {
            Filament::auth()->login($user);

            return redirect(url(filament()->getHomeUrl()))->with('notification.success.institute_invitation_accepted', $notification);
        }

        return redirect(url(filament()->getLoginUrl()));
    }
}
