<?php

namespace Modules\Institute\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InstituteUser extends Model
{
    protected $fillable = [
        'user_id',
        'institute_id',
    ];

    //    public function user(): BelongsTo
    //    {
    //        return $this->belongsTo(User::class);
    //    }
    //
    //    public function institute(): BelongsTo
    //    {
    //        return $this->belongsTo(Institute::class);
    //    }
}
