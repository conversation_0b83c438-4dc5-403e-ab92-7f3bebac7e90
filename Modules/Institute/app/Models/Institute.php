<?php

namespace Modules\Institute\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;

class Institute extends Model
{
    protected $fillable = [
        'name',
        'slug',
        'city_id',
        'phone',
        'email',
        'address2',
        'address1',
        'default_lang',
        'web',
        'photo',
        'user_id',
        'meta',
    ];

    protected $casts = [
        'meta' => 'array',
    ];

    // Owner user
    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function members(): \Illuminate\Database\Eloquent\Relations\belongsToMany
    {
        return $this->belongsToMany(User::class, 'institute_user');
    }

    public function classRooms(): \Illuminate\Database\Eloquent\Relations\hasMany
    {
        return $this->hasMany(ClassRoom::class);
    }

    public function institute_invitations(): \Illuminate\Database\Eloquent\Relations\hasMany
    {
        return $this->hasMany(InstituteInvitation::class);
    }
}
