<?php

namespace Modules\Institute\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\belongsToMany;

class ClassRoom extends Model
{
    protected $fillable = [
        'name',
        'institute_id',
    ];

    public function users(): belongsToMany
    {
        return $this->belongsToMany(User::class);
    }

    public function institute()
    {
        return $this->belongsTo(Institute::class, 'institute_id');
    }
}
