<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('institute_invitations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('institute_id')->constrained()->cascadeOnDelete();
            $table->string('email');
            $table->string('type')->default(\Modules\Institute\Concerns\UserTypes::STUDENT->value);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('institute_invitations');
    }
};
