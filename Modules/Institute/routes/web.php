<?php

use Illuminate\Support\Facades\Route;
use Modules\Institute\Http\Controllers\InstituteInvitationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/invitations/{invitationId}', [InstituteInvitationController::class, 'accept'])
    ->middleware(['signed'])
    ->name('filament.institute.invitations.accept');
