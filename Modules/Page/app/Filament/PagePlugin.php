<?php

namespace Modules\Page\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class PagePlugin implements Plugin
{
    public function getId(): string
    {
        return 'page';
    }

    public function register(Panel $panel): void
    {
        if (\Module::isEnabled('Page')) {
            $panel->discoverResources(
                in: __DIR__.'/Resources',
                for: 'Modules\\Page\\Filament\\Resources'
            );
        }
    }

    public function boot(Panel $panel): void {}
}
