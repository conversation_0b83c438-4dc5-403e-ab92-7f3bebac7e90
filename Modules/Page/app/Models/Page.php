<?php

namespace Modules\Page\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\ActivityLogs\Concerns\ActivityLoggable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Page extends Model implements HasMedia
{
    use ActivityLoggable;
    use interactsWithMedia;

    protected $table = 'pages';

    protected $fillable = [
        'title',
        'slug',
        'content',
        'is_published',
        'is_featured',
    ];

    protected $casts = [
        'content' => 'array',
        'is_published' => 'boolean',
    ];

    public function getBlocksAttribute()
    {
        return json_decode(
            collect($this->content ?? [])->toJson()
        );
    }

    public function excerpt(): string
    {
        $excerpt = collect($this->content)
            ->where('type', 'markdown')
            ->first() ?? [];

        $excerpt = collect(
            explode("\n", Arr::get($excerpt, 'data.content', ''))
        )->first();

        return Str::limit($excerpt, 160, '...');
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeDrafts($query)
    {
        return $query->where('is_published', false);
    }
}
