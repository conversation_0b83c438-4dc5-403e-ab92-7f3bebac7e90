<?php

namespace Modules\Shop\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Modules\Shop\Filament\Resources\ProductResource\Pages;
use Modules\Shop\Models\Product;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $slug = 'books';

    protected static ?string $navigationIcon = 'heroicon-o-shopping-bag';

    protected static ?string $recordTitleAttribute = 'name';

    public static function getLabel(): ?string
    {
        return __('Book');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Books');
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'slug', 'description'];
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make()
                    ->columns(3)
                    ->schema([
                        Forms\Components\Group::make()
                            ->columnSpan(2)
                            ->schema([
                                Forms\Components\Section::make()
                                    ->schema([
                                        Forms\Components\TextInput::make('name')
                                            ->required()
                                            ->live(debounce: '750ms')
                                            ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                                if ($operation !== 'create' && $operation !== 'createOption') {
                                                    return;
                                                }
                                                $set('slug', Str::slug($state));
                                            })
                                            ->afterStateHydrated(function (Forms\Components\TextInput $component, $state) {
                                                $component->state(ucwords($state));
                                            }),

                                        Forms\Components\TextInput::make('slug')
                                            ->disabled()
                                            ->dehydrated()
                                            ->required()
                                            ->unique(ignoreRecord: true),

                                        Forms\Components\MarkdownEditor::make('description')
                                            ->columnSpan('full'),
                                    ])->columns(2),

                                Forms\Components\Section::make('Pricing & Inventory')
                                    ->schema([
                                        Forms\Components\TextInput::make('sku')
                                            ->unique(ignoreRecord: true)
                                            ->label('SKU (Stock Keeping Unit)')
                                            ->required(),

                                        Forms\Components\TextInput::make('price')
                                            ->numeric()
                                            ->rules('regex:/^\d{1,6}(\.\d{0,2})?$/')
                                            ->required(),

                                        Forms\Components\TextInput::make('quantity')
                                            ->numeric()
                                            ->minValue(0)
                                            ->maxValue(100)
                                            ->required(),

                                        Forms\Components\Select::make('type')
                                            ->options([
                                                'downloadable' => 'Downloadable',
                                                'deliverable' => 'Deliverable',
                                            ])->default('deliverable')->required(),
                                    ])->columns(2),

                                Forms\Components\Section::make('Additional Information')
                                    ->schema([
                                        Forms\Components\KeyValue::make('additional')
                                            ->hiddenLabel()
                                            ->helperText('table for additional details for product like dimensions, weight, etc.')
                                            ->addActionLabel('Add detail')
                                            ->default(['Weight' => null, 'Size' => null])
                                            ->deletable(true)
                                            ->keyLabel('Detail Type')
                                            ->ValueLabel('Value'),
                                    ]),
                            ]),

                        Forms\Components\Group::make()
                            ->columnSpan(1)
                            ->schema([
                                Forms\Components\Section::make('Status')
                                    ->schema([
                                        Forms\Components\Toggle::make('is_visible')
                                            ->label(__('Visibility'))
                                            ->helperText('Enable or disable product visibility')
                                            ->default(true),

                                        Forms\Components\Toggle::make('is_featured')
                                            ->label(__('Featured'))
                                            ->helperText('Enable or disable products featured status'),

                                        //                                        Forms\Components\DatePicker::make('published_at')
                                        //                                            ->label(__('Availability'))
                                        //                                            ->default(now())
                                    ]),

                                //                                Forms\Components\Section::make('Image')
                                //                                    ->schema([
                                //                                        Forms\Components\FileUpload::make('image')
                                //                                            ->directory('form-attachments')
                                //                                            ->preserveFilenames()
                                //                                            ->required()
                                //                                            ->image()
                                //                                            ->imageEditor()
                                //                                    ])->collapsible(),

                                Forms\Components\FileUpload::make('images')
                                    ->reorderable()
                                    ->imageEditor()
                                    ->multiple(),

                                Forms\Components\Section::make('Associations')
                                    ->schema([
                                        Forms\Components\Select::make('product_categories')
                                            ->label(__('Categories'))
                                            ->relationship('product_categories', 'name')
                                            ->createOptionForm([
                                                Forms\Components\TextInput::make('name')
                                                    ->required()
                                                    ->maxLength(255),
                                            ])
                                            ->preload()
                                            ->multiple()
                                            ->required(),
                                    ]),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('images.0')->circular(),

                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_visible')
                    ->sortable()
                    ->toggleable()
                    ->label(__('Visibility'))
                    ->boolean(),

                Tables\Columns\TextColumn::make('price')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('quantity')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('type'),

            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_visible')
                    ->label(__('Visibility'))
                    ->boolean()
                    ->trueLabel('Only Visible Books')
                    ->falseLabel('Only Hidden Books')
                    ->native(false),

            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
