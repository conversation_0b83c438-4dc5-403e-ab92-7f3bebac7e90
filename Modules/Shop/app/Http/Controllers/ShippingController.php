<?php

namespace Modules\Shop\Http\Controllers;

use App\Http\Controllers\Controller;
use Modules\Billing\Models\Order;
use Modules\Shop\Services\DHLShippingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class ShippingController extends Controller
{
    private $dhlService;

    public function __construct(DHLShippingService $dhlService)
    {
        $this->dhlService = $dhlService;
    }

    /**
     * Calculate shipping cost for checkout
     */
    public function calculateShipping(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'destination.country_code' => 'required|string|size:2',
            'destination.postal_code' => 'required|string',
            'destination.city' => 'required|string',
            'destination.address_line_1' => 'required|string',
            'destination.address_line_2' => 'nullable|string',
            'destination.province_code' => 'nullable|string',
            'packages' => 'required|array|min:1',
            'packages.*.weight' => 'required|numeric|min:0.1',
            'packages.*.dimensions.length' => 'required|numeric|min:1',
            'packages.*.dimensions.width' => 'required|numeric|min:1',
            'packages.*.dimensions.height' => 'required|numeric|min:1',
            'declared_value.amount' => 'nullable|numeric|min:0',
            'declared_value.currency' => 'nullable|string|size:3',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $orderData = [
                'origin' => [
                    'country_code' => config('app.warehouse.country', 'US'),
                    'postal_code' => config('app.warehouse.postal_code', '10001'),
                    'city' => config('app.warehouse.city', 'New York'),
                    'address_line_1' => config('app.warehouse.address', '123 Warehouse St'),
                ],
                'destination' => $request->input('destination'),
                'packages' => $request->input('packages'),
                'service_type' => $request->input('service_type', 'N'),
                'is_international' => $request->input('destination.country_code') !== config('app.warehouse.country',
                        'US'),
                'declared_value' => $request->input('declared_value'),
            ];

            $rates = $this->dhlService->calculateShippingCost($orderData);

            return response()->json([
                'success' => true,
                'rates' => $rates,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate shipping for existing order
     */
    public function calculateForOrder(Request $request, $orderId): JsonResponse
    {
        try {
            // Assuming you have an Order model
            $order = Order::findOrFail($orderId);

            $rates = $this->dhlService->calculateForOrder($order);

            // Optionally update order with calculated rates
            $order->update([
                'shipping_rates' => json_encode($rates),
                'shipping_calculated_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'order_id' => $order->id,
                'rates' => $rates,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get cached shipping rates
     */
    public function getCachedRates(Request $request): JsonResponse
    {
        $cacheKey = 'shipping_rates_'.md5(json_encode($request->all()));

        $rates = cache()->get($cacheKey);

        if ($rates) {
            return response()->json([
                'success' => true,
                'rates' => $rates,
                'cached' => true,
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'No cached rates found',
        ], 404);
    }
}
