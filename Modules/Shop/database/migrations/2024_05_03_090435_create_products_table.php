<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('sku')->unique();
            $table->text('images');
            $table->longText('description')->nullable();
            $table->longText('additional')->nullable();
            $table->unsignedBigInteger('quantity')->default('1');
            $table->UnsignedBigInteger('price');
            $table->boolean('is_visible')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->string('type')->default('deliverable');
            $table->text('options')->nullable();
            $table->timestamps();

            $table->fullText(['name', 'description']);

        });

        Schema::create('product_category_product', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_category_id');
            $table->foreignId('product_id');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
