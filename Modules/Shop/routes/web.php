<?php

use Illuminate\Support\Facades\Route;
use Modules\Shop\Http\Controllers\ShippingController;
use Modules\Shop\Http\Controllers\ShopController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group([], function () {
    Route::prefix('shipping')->group(function () {
        Route::post('/calculate', [ShippingController::class, 'calculateShipping']);
        Route::post('/order/{orderId}/calculate', [ShippingController::class, 'calculateForOrder']);
        Route::get('/rates/cached', [ShippingController::class, 'getCachedRates']);
    });
//    Route::resource('shop', ShopController::class)->names('shop');
});
