<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckCourseEntitlementMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();
        $module = $request->route('module');

        if (! $user->hasEntitlement('module_access', [
            'module_id' => $module->id,
        ])) {
            return redirect()->route('subscription.upgrade');
        }

        return $next($request);
    }
}
