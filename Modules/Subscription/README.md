# Subscription Module Documentation

This module provides comprehensive subscription management functionality for the application. It handles subscription plans, entitlements, and user subscriptions with flexible access control.

## Key Features

- **Plan Management**: Create and manage subscription plans with customizable pricing, trial periods, and billing cycles
- **Entitlement Control**: Define granular access control via entitlements attached to plans
- **User Subscriptions**: Track and manage user subscriptions with automatic renewal and expiration handling
- **Access Control**: Easily check if users have access to specific features or content
- **Payment Integration**: Built-in support for payment processing with the Payment module

## Core Components

### Models

1. **Plan**: Defines subscription plans with pricing and period details
2. **Entitlement**: Defines features or access levels that can be assigned to plans
3. **Subscription**: Represents a user's subscription to a plan, with state pattern implementation for status transitions

### Services

1. **SubscriptionService**: Handles subscription management operations
2. **EntitlementService**: Manages entitlement access and checks

### State Pattern Implementation

The module uses a state pattern to manage subscription status transitions:

1. **SubscriptionState**: Abstract base class for all subscription states
2. **ActiveState**: Represents an active subscription
3. **GracePeriodState**: Represents a subscription with payment issues in grace period
4. **CanceledState**: Represents a canceled subscription
5. **ExpiredState**: Represents an expired subscription

See the [state pattern documentation](docs/state-pattern.md) for more details.

## Installation

The module is already integrated into the application. To ensure all database tables are created, run:

```
php artisan migrate
```

## Basic Usage

### Managing Plans and Entitlements

#### Creating a Plan

```php
use Modules\Subscription\Models\Plan;

$plan = Plan::create([
    'name' => 'Premium Plan',
    'slug' => 'premium',
    'description' => 'Access to all premium features',
    'price' => 99.99,
    'is_active' => true,
    'invoice_period' => 30,
    'invoice_interval' => 'day',
    'trial_period' => 7,
    'trial_interval' => 'day',
    'grace_period' => 3,
    'grace_interval' => 'day',
]);
```

#### Creating an Entitlement

```php
use Modules\Subscription\Models\Entitlement;

$entitlement = Entitlement::create([
    'name' => 'Course Access',
    'feature_key' => 'course_access',
    'type' => 'course',
    'description' => 'Allows access to premium courses'
]);
```

#### Adding Entitlements to a Plan

```php
use Modules\Subscription\Services\EntitlementService;

$entitlementService = app(EntitlementService::class);

// Simple feature access
$entitlementService->addEntitlementToPlan($plan, 'premium_feature');

// Specific course access
$entitlementService->addCourseAccessToPlan($plan, $course);

// Specific module access
$entitlementService->addModuleAccessToPlan($plan, $module);

// With custom access details
$plan->addEntitlement($entitlement, [
    'content_id' => 123,
    'allowed' => true,
    'max_usage' => 10
]);
```

### Managing User Subscriptions

#### Subscribing a User to a Plan

```php
use Modules\Subscription\Services\SubscriptionService;

$subscriptionService = app(SubscriptionService::class);

// Subscribe to a paid plan
$subscription = $subscriptionService->subscribeToPlan($user, $plan);

// Subscribe with trial disabled
$subscription = $subscriptionService->subscribeToPlan($user, $plan, false);

// Assign free plan
$subscription = $subscriptionService->assignFreePlan($user);
```

#### Switching Plans

```php
$subscription = $subscriptionService->switchPlan($user, $newPlan);

// Switch at the end of current period
$subscription = $subscriptionService->switchPlan($user, $newPlan, false);
```

#### Canceling a Subscription

```php
$subscription->cancel(); // Cancels at the end of the period

// Cancel immediately
$subscription->cancel(true);
// or
$subscriptionService->cancelSubscription($subscription, true);
```

### Checking Entitlements

#### User Access Checks

```php
// Check if user has a specific feature
if ($user->hasEntitlement('premium_content')) {
    // User has access to premium content
}

// Check detailed access rules
if ($user->hasEntitlement('module_access', ['module_id' => $moduleId])) {
    // User can access this specific module
}

// Using the entitlement service with caching
$entitlementService = app(EntitlementService::class);

if ($entitlementService->userHasFeature($user, 'premium_feature')) {
    // User has the feature
}

// Check module access
if ($entitlementService->canAccessModule($user, $module)) {
    // User can access this module
}

// Get all modules user can access in a course
$accessibleModules = $entitlementService->getAccessibleModules($user, $course);
```

### Handling Payments

```php
use Modules\Payment\Models\Payment;

// Process a successful payment
$payment = Payment::find($paymentId);
$subscription->handlePaymentCompleted($payment);

// Process a failed payment
$subscription->handlePaymentFailed($payment, 'Card declined');

// Using the subscription service
$subscriptionService->processPayment($subscription, $payment);
```

### Subscription Status Checks

```php
if ($subscription->isActive()) {
    // Subscription is active
}

if ($subscription->isInGracePeriod()) {
    // Subscription is in grace period
}

if ($subscription->isExpired()) {
    // Subscription has expired
}

if ($subscription->needsRenewal(5)) {
    // Subscription expires in 5 days or less
}
```

## Maintenance Tasks

### Expiration Notifications

Send expiration notifications to users whose subscriptions are expiring soon:

```php
$subscriptionService = app(SubscriptionService::class);
$notificationsSent = $subscriptionService->sendExpirationNotifications();
```

### Expiring Overdue Subscriptions

Mark expired subscriptions as expired:

```php
$subscriptionsExpired = $subscriptionService->expireOverdueSubscriptions();
```

## Using with Blade

```blade
@if(auth()->user()->hasEntitlement('premium_content'))
    <div class="premium-content">
        <!-- Premium content here -->
    </div>
@endif
```

## Advanced Configuration

### Setting Up a Command to Handle Subscription Lifecycle

Create an Artisan command to run daily and handle subscription lifecycle events:

```php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Modules\Subscription\Services\SubscriptionService;

class ProcessSubscriptions extends Command
{
    protected $signature = 'subscriptions:process';
    protected $description = 'Process subscription lifecycle events like expirations and renewals';

    public function handle(SubscriptionService $subscriptionService)
    {
        // Send reminders to subscriptions expiring in 3 days
        $notificationsSent = $subscriptionService->sendExpirationNotifications(3);
        $this->info("{$notificationsSent} expiration notifications sent");

        // Expire overdue subscriptions
        $expiredCount = $subscriptionService->expireOverdueSubscriptions();
        $this->info("{$expiredCount} overdue subscriptions expired");

        return Command::SUCCESS;
    }
}
```

Then register it in your `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    $schedule->command('subscriptions:process')->daily();
}
```

## Extending the Module

The Subscription module is designed to be extensible:

1. Add custom entitlement types by extending the Entitlement model
2. Create custom subscription plan types by extending the Plan model
3. Implement additional payment gateways by integrating with the Payment module
4. Add custom subscription states by creating new state classes that extend SubscriptionState
5. Modify transition logic by overriding methods in the appropriate state classes

### Adding a New Subscription State

To add a new subscription state:

1. Create a new class that extends `SubscriptionState`
2. Implement the required methods, focusing on valid transitions for that state
3. Update the `SubscriptionStateFactory` to recognize the new state
4. Add the new state to the subscription migration if needed

See the [subscription state diagram](docs/subscription-state-diagram.md) for more information about the state transitions.