<?php

namespace Modules\Subscription\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Modules\Subscription\Models\Subscription;

class SubscriptionExpiringNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected Subscription $subscription;

    /**
     * Create a new notification instance.
     */
    public function __construct(Subscription $subscription)
    {
        $this->subscription = $subscription;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $daysLeft = now()->diffInDays($this->subscription->ends_at);
        $planName = $this->subscription->plan->name;

        return (new MailMessage)
            ->subject("Your {$planName} subscription is expiring soon")
            ->greeting("Hello {$notifiable->name}!")
            ->line("Your subscription to the {$planName} plan will expire in {$daysLeft} ".($daysLeft === 1 ? 'day' : 'days').'.')
            ->line('To continue enjoying all the benefits of your subscription, please renew before it expires.')
            ->action('Renew Subscription', route('subscription.index'))
            ->line('Thank you for your continued support!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'subscription_id' => $this->subscription->id,
            'plan_name' => $this->subscription->plan->name,
            'ends_at' => $this->subscription->ends_at->toIso8601String(),
        ];
    }
}
