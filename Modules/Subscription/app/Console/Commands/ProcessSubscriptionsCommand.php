<?php

namespace Modules\Subscription\Console\Commands;

use Illuminate\Console\Command;
use Modules\Subscription\Services\SubscriptionService;

class ProcessSubscriptionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscriptions:process {--days=7 : Days before expiration to send notifications}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process subscription lifecycle events like expirations and renewals';

    /**
     * Execute the console command.
     */
    public function handle(SubscriptionService $subscriptionService)
    {
        $daysBeforeExpiration = $this->option('days');

        // Send notifications to users with expiring subscriptions
        $this->info("Sending notifications to subscriptions expiring in {$daysBeforeExpiration} days...");
        $notificationsSent = $subscriptionService->sendExpirationNotifications($daysBeforeExpiration);
        $this->info("{$notificationsSent} expiration notifications sent");

        // Expire overdue subscriptions
        $this->info('Expiring overdue subscriptions...');
        $expiredCount = $subscriptionService->expireOverdueSubscriptions();
        $this->info("{$expiredCount} overdue subscriptions expired");

        return Command::SUCCESS;
    }
}
