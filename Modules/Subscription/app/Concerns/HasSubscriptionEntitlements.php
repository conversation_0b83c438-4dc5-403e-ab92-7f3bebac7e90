<?php

namespace Modules\Subscription\Concerns;

use Modules\Subscription\Models\Subscription;

/**
 * Trait HasSubscriptionEntitlements
 * Provides methods for checking user entitlements and managing subscriptions.
 */
trait HasSubscriptionEntitlements
{
    // Check if user has a specific entitlement
    public function hasEntitlement(string $featureKey): bool
    {
        $activeSubscription = $this->activeSubscription();
        if (! $activeSubscription || ! $activeSubscription->plan) {
            return false;
        }

        return $activeSubscription->plan->hasEntitlement($featureKey);
    }

    // Get the user's active subscription
    public function activeSubscription()
    {
        return $this->subscriptions()
            ->where('status', 'active')
            ->where('ends_at', '>', now())
            ->first();
    }

    // Relationship to subscriptions
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }
}
