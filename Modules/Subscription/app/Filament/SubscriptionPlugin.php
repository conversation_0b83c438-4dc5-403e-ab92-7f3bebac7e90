<?php

namespace Modules\Subscription\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class SubscriptionPlugin implements Plugin
{
    public function getId(): string
    {
        return 'subscription';
    }

    public function register(Panel $panel): void
    {
        if (\Module::isEnabled('Subscription')) {
            $panel->discoverResources(
                in: __DIR__.'/Resources',
                for: 'Modules\\Subscription\\Filament\\Resources'
            );
        }
    }

    public function boot(Panel $panel): void {}
}
