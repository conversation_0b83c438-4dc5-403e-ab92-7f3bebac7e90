<?php

namespace Modules\Subscription\Filament\Resources\PlanResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Modules\Subscription\Models\Entitlement;

class EntitlementsRelationManager extends RelationManager
{
    protected static string $relationship = 'entitlements';

    protected static ?string $recordTitleAttribute = 'name';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('entitlement_id')
                    ->label('Entitlement')
                    ->options(Entitlement::pluck('name', 'id'))
                    ->searchable()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if (! $state) {
                            return;
                        }

                        $entitlement = Entitlement::find($state);
                        $set('is_module', $entitlement && $entitlement->type === 'module');
                    }),

                Forms\Components\Hidden::make('is_module')
                    ->default(false),

                // This will be conditionally shown when entitlement type is module
                Forms\Components\Select::make('modules')
                    ->label('Available Modules')
                    ->multiple()
                    ->options([
                        'test1' => 'test1 Module',
                        'test2' => 'test2 Module',
                        // Add your actual modules here
                    ])
                    ->hidden(fn (callable $get) => ! $get('is_module'))
                    ->helperText('Select which modules this plan has access to'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),

                Tables\Columns\BadgeColumn::make('type')
                    ->colors([
                        'secondary',
                        'primary' => 'feature',
                        'warning' => 'module',
                    ]),

                Tables\Columns\TextColumn::make('feature_key')
                    ->searchable(),

                Tables\Columns\TextColumn::make('pivot.access_details')
                    ->label('Access Details')
                    ->formatStateUsing(function ($state) {
                        if (empty($state)) {
                            return '-';
                        }

                        $modules = $state['modules'] ?? [];
                        if (count($modules)) {
                            return implode(', ', $modules);
                        }

                        return json_encode($state);
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'feature' => 'Feature',
                        'module' => 'Module',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\AttachAction::make()
                    ->preloadRecordSelect()
                    ->form(fn (Tables\Actions\AttachAction $action) => [
                        $action->getRecordSelect(),
                        // Only show module selector for module-type entitlements
                        Forms\Components\MultiSelect::make('modules')
                            ->label('Available Modules')
                            ->options([
                                'test1' => 'test1 Module',
                                'test2' => 'test2 Module',
                                // Add your actual modules here
                            ])
                            ->visible(function (callable $get) {
                                $entitlementId = $get('recordId');
                                if (! $entitlementId) {
                                    return false;
                                }

                                $entitlement = Entitlement::find($entitlementId);

                                return $entitlement && $entitlement->type === 'module';
                            })
                            ->helperText('Select which modules this plan has access to'),
                    ])
                    ->using(function (Model $recordToAttach, array $data) {
                        $accessDetails = [];

                        if ($recordToAttach->type === 'module' && isset($data['modules'])) {
                            $accessDetails['modules'] = $data['modules'];
                        }
                        $this->ownerRecord->entitlements()->attach(
                            $recordToAttach,
                            ['access_details' => $accessDetails]
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->form(function (Model $record) {
                        $isModule = $record->type === 'module';
                        $currentModules = [];

                        if ($isModule) {
                            // Get the current selected modules from the pivot
                            $pivotData = $record->pivot->access_details ?? [];
                            $currentModules = $pivotData['modules'] ?? [];
                        }

                        return [
                            Forms\Components\MultiSelect::make('modules')
                                ->label('Available Modules')
                                ->options([
                                    'test1' => 'test1 Module',
                                    'test2' => 'test2 Module',
                                    // Add your actual modules here
                                ])
                                ->visible($isModule)
                                ->default($currentModules)
                                ->helperText('Select which modules this plan has access to'),
                        ];
                    })
                    ->action(function (Model $record, array $data) {
                        $accessDetails = $record->pivot->access_details ?? [];

                        if ($record->type === 'module' && isset($data['modules'])) {
                            $accessDetails['modules'] = $data['modules'];
                        }

                        $record->pivot->update([
                            'access_details' => $accessDetails,
                        ]);
                    }),
                Tables\Actions\DetachAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\DetachBulkAction::make(),
            ]);
    }
}
