<?php

namespace Modules\Subscription\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Table;
use Modules\Subscription\Filament\Resources\EntitlementResource\Pages;
use Modules\Subscription\Models\Entitlement;

class EntitlementResource extends Resource
{
    protected static ?string $model = Entitlement::class;

    protected static ?string $slug = 'entitlements';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __('Subscriptions & Plans');
    }

    public static function getLabel(): ?string
    {
        return __('Entitlement');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Entitlements');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required(),
                Forms\Components\Textarea::make('description')
                    ->maxLength(1000)
                    ->rows(3),
                Forms\Components\Select::make('type')
                    ->options([
                        'feature' => 'Feature',
                        'module' => 'Module',
                    ])
                    ->required()
                    ->reactive(),

                Forms\Components\TextInput::make('feature_key')
                    ->maxLength(255)
                    ->helperText('Unique identifier used for checking entitlements in code')
                    ->visible(fn (callable $get) => $get('type') === 'feature')
                    ->required(fn (callable $get) => $get('type') === 'feature'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name'),
                Tables\Columns\BadgeColumn::make('type')
                    ->colors([
                        'secondary',
                        'primary' => 'feature',
                        'warning' => 'module',
                    ]),
                Tables\Columns\TextColumn::make('feature_key')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'feature' => 'Feature',
                        'module' => 'Module',
                    ]),
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEntitlements::route('/'),
            'create' => Pages\CreateEntitlement::route('/create'),
            'edit' => Pages\EditEntitlement::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
