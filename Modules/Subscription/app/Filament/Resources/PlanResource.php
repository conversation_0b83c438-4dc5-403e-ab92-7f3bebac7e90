<?php

namespace Modules\Subscription\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Modules\Subscription\Filament\Resources\PlanResource\Pages;
use Modules\Subscription\Filament\Resources\PlanResource\RelationManagers\EntitlementsRelationManager;
use Modules\Subscription\Models\Plan;

class PlanResource extends Resource
{
    protected static ?string $model = Plan::class;

    protected static ?string $slug = 'plans';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __('Subscriptions & Plans');
    }

    public static function getLabel(): ?string
    {
        return __('Plan');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Plans');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Forms\Get $get, Forms\Set $set, string $operation, ?string $old, ?string $state) {
                        if (($get('slug') ?? '') !== Str::slug($old) || $operation !== 'create') {
                            return;
                        }
                        $set('slug', Str::slug($state));
                    }),
                Forms\Components\Hidden::make('slug')
                    ->inlineLabel()
                    ->label(__('Slug'))
                    ->alphaDash()
                    ->required()
                    ->unique(ignoreRecord: true),

                Forms\Components\Textarea::make('description')
                    ->rows(3)
                    ->maxLength(1000),

                Forms\Components\TextInput::make('price')
                    ->numeric()
                    ->prefix('$')
                    ->required(),
                Forms\Components\Grid::make()
                    ->schema([
                        Forms\Components\TextInput::make('invoice_period')
                            ->numeric()
                            ->required()
                            ->default(1),

                        Forms\Components\Select::make('invoice_interval')
                            ->options([
                                'day' => 'Day',
                                'week' => 'Week',
                                'month' => 'Month',
                                'year' => 'Year',
                            ])
                            ->required()
                            ->default('month'),
                    ])->columns(2),
                Forms\Components\Grid::make()
                    ->schema([
                        Forms\Components\TextInput::make('trial_period')
                            ->numeric()
                            ->default(0),

                        Forms\Components\Select::make('trial_interval')
                            ->options([
                                'day' => 'Day',
                                'week' => 'Week',
                                'month' => 'Month',
                                'year' => 'Year',
                            ])
                            ->default('day'),
                    ])->columns(2),
                Forms\Components\Grid::make()
                    ->schema([
                        Forms\Components\TextInput::make('grace_period')
                            ->numeric()
                            ->default(0),

                        Forms\Components\Select::make('grace_interval')
                            ->options([
                                'day' => 'Day',
                                'week' => 'Week',
                                'month' => 'Month',
                                'year' => 'Year',
                            ])
                            ->default('day'),
                    ])->columns(2),
                Forms\Components\TextInput::make('active_subscribers_limit')
                    ->numeric()
                    ->nullable()
                    ->helperText('Leave empty for unlimited subscribers'),

                Forms\Components\Toggle::make('is_active')
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name'),
                Tables\Columns\TextColumn::make('price')->money('usd'),
                Tables\Columns\TextColumn::make('billing_interval'),
                Tables\Columns\TextColumn::make('invoice_period')
                    ->formatStateUsing(fn ($record) => "{$record->invoice_period} {$record->invoice_interval}(s)"),
                Tables\Columns\TextColumn::make('active_subscribers_count')
                    ->label('Subscribers'),
                Tables\Columns\ToggleColumn::make('is_active'),
            ])
            ->filters([
                Tables\Filters\Filter::make('active')
                    ->query(fn ($query) => $query->where('is_active', true)),
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            EntitlementsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlans::route('/'),
            'create' => Pages\CreatePlan::route('/create'),
            'edit' => Pages\EditPlan::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
