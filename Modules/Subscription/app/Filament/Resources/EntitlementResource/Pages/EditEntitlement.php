<?php

namespace Modules\Subscription\Filament\Resources\EntitlementResource\Pages;

use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Modules\Subscription\Filament\Resources\EntitlementResource;

class EditEntitlement extends EditRecord
{
    protected static string $resource = EntitlementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
