<?php

namespace Modules\Subscription\States;

use Modules\Payment\Models\Payment;
use Modules\Subscription\Models\Plan;
use Modules\Subscription\Models\Subscription;
use Modules\Subscription\Notifications\SubscriptionActivatedNotification;

class GracePeriodState extends SubscriptionState
{
    /**
     * Get the state identifier
     */
    public function getState(): string
    {
        return 'grace_period';
    }

    /**
     * Check if subscription is in grace period
     */
    public function isInGracePeriod(): bool
    {
        return $this->subscription->grace_period_ends_at &&
               now()->between($this->subscription->ends_at, $this->subscription->grace_period_ends_at);
    }

    /**
     * Handle subscription renewal
     */
    public function renew(int $days = 30): Subscription
    {
        $this->subscription->starts_at = now();
        $this->subscription->ends_at = now()->addDays($days);
        $this->subscription->grace_period_ends_at = null;
        $this->subscription->payment_status = 'paid';
        $this->subscription->last_payment_error = null;

        // Transition back to active state
        $this->subscription->setSubscriptionState(new ActiveState($this->subscription));

        $this->subscription->save();

        return $this->subscription;
    }

    /**
     * Handle subscription cancellation
     */
    public function cancel(bool $immediately = false): Subscription
    {
        if ($immediately) {
            $this->subscription->setSubscriptionState(new CanceledState($this->subscription));
            $this->subscription->ends_at = now();
        } else {
            $this->subscription->setSubscriptionState(new CanceledState($this->subscription));
            // Will be canceled at the end of the grace period
        }

        $this->subscription->save();

        return $this->subscription;
    }

    /**
     * Switch to a new plan
     */
    public function switchPlan(Plan $newPlan, bool $immediately = false): Subscription
    {
        if ($immediately) {
            // End current subscription
            $this->subscription->setSubscriptionState(new ExpiredState($this->subscription));
            $this->subscription->ends_at = now();
            $this->subscription->save();

            // Create new subscription
            return $this->subscription->user->subscriptions()->create([
                'plan_id' => $newPlan->id,
                'status' => 'active',
                'starts_at' => now(),
                'ends_at' => now()->addDays($newPlan->invoice_period),
                'was_switched' => true,
            ]);
        } else {
            // Mark the current subscription for switching at the end of period
            $this->subscription->was_switched = true;
            $this->subscription->save();

            // Schedule the creation of the new subscription
            return $this->subscription;
        }
    }

    /**
     * Handle completed payment
     */
    public function handlePaymentCompleted(Payment $payment): void
    {
        // Extend the subscription period
        $this->subscription->ends_at = now()->addDays($this->subscription->plan->invoice_period ?? 30);
        $this->subscription->payment_status = 'paid';
        $this->subscription->last_payment_error = null;
        $this->subscription->grace_period_ends_at = null;

        // Transition back to active state
        $this->subscription->setSubscriptionState(new ActiveState($this->subscription));

        $this->subscription->save();

        // Notify the subscriber
        $this->subscription->user->notify(new SubscriptionActivatedNotification($this->subscription));
    }

    /**
     * Handle failed payment
     */
    public function handlePaymentFailed(Payment $payment, string $reason): void
    {
        // Record the failure
        $this->subscription->payment_status = 'failed';
        $this->subscription->last_payment_error = $reason;

        // If grace period has passed, transition to expired
        if (now()->isAfter($this->subscription->grace_period_ends_at)) {
            $this->subscription->setSubscriptionState(new ExpiredState($this->subscription));
        }

        $this->subscription->save();

        // Record failed attempt
        $this->subscription->paymentAttempts()->create([
            'payment_id' => $payment->id,
            'status' => 'failed',
            'error_message' => $reason,
            'occurred_at' => now(),
        ]);
    }
}
