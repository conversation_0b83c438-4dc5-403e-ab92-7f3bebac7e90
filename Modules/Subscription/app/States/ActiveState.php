<?php

namespace Modules\Subscription\States;

use Modules\Payment\Models\Payment;
use Modules\Subscription\Models\Plan;
use Modules\Subscription\Models\Subscription;
use Modules\Subscription\Notifications\SubscriptionActivatedNotification;
use Modules\Subscription\Notifications\SubscriptionGracePeriodNotification;

class ActiveState extends SubscriptionState
{
    /**
     * Get the state identifier
     */
    public function getState(): string
    {
        return 'active';
    }

    /**
     * Check if subscription is active
     */
    public function isActive(): bool
    {
        return now()->between($this->subscription->starts_at, $this->subscription->ends_at);
    }

    /**
     * Handle subscription renewal
     */
    public function renew(int $days = 30): Subscription
    {
        $this->subscription->starts_at = now();
        $this->subscription->ends_at = now()->addDays($days);
        $this->subscription->grace_period_ends_at = null;
        $this->subscription->payment_status = 'paid';
        $this->subscription->last_payment_error = null;
        $this->subscription->save();

        return $this->subscription;
    }

    /**
     * Handle subscription cancellation
     */
    public function cancel(bool $immediately = false): Subscription
    {
        if ($immediately) {
            $this->subscription->setSubscriptionState(new CanceledState($this->subscription));
            $this->subscription->ends_at = now();
        } else {
            $this->subscription->setSubscriptionState(new CanceledState($this->subscription));
            // Will be canceled at the end of the period
        }

        $this->subscription->save();

        return $this->subscription;
    }

    /**
     * Switch to a new plan
     */
    public function switchPlan(Plan $newPlan, bool $immediately = false): Subscription
    {
        if ($immediately) {
            // End current subscription
            $this->subscription->setSubscriptionState(new ExpiredState($this->subscription));
            $this->subscription->ends_at = now();
            $this->subscription->save();

            // Create new subscription
            return $this->subscription->user->subscriptions()->create([
                'plan_id' => $newPlan->id,
                'status' => 'active',
                'starts_at' => now(),
                'ends_at' => now()->addDays($newPlan->invoice_period),
                'was_switched' => true,
            ]);
        } else {
            // Mark the current subscription for switching at the end of period
            $this->subscription->was_switched = true;
            $this->subscription->save();

            // Schedule the creation of the new subscription
            return $this->subscription;
        }
    }

    /**
     * Handle completed payment
     */
    public function handlePaymentCompleted(Payment $payment): void
    {
        // Extend the subscription period
        $this->subscription->ends_at = now()->addDays($this->subscription->plan->invoice_period ?? 30);
        $this->subscription->payment_status = 'paid';
        $this->subscription->last_payment_error = null;
        $this->subscription->grace_period_ends_at = null;
        $this->subscription->save();

        // Notify the subscriber
        $this->subscription->user->notify(new SubscriptionActivatedNotification($this->subscription));

        // Track subscription metrics
        // $this->subscription->plan->increment('active_subscribers_count');
    }

    /**
     * Handle failed payment
     */
    public function handlePaymentFailed(Payment $payment, string $reason): void
    {
        // Mark subscription status as problematic
        $this->subscription->payment_status = 'failed';
        $this->subscription->last_payment_error = $reason;

        // Set a grace period based on plan settings
        $gracePeriodDays = $this->subscription->plan->grace_period ?? 7;
        $this->subscription->grace_period_ends_at = now()->addDays($gracePeriodDays);

        // Transition to grace period state
        $this->subscription->setSubscriptionState(new GracePeriodState($this->subscription));

        // Notify user about a grace period
        $this->subscription->user->notify(new SubscriptionGracePeriodNotification($this->subscription, $reason));

        $this->subscription->save();

        // Record failed attempt
        $this->subscription->paymentAttempts()->create([
            'payment_id' => $payment->id,
            'status' => 'failed',
            'error_message' => $reason,
            'occurred_at' => now(),
        ]);
    }
}
