<?php

namespace Modules\Subscription\States;

use Modules\Subscription\Models\Subscription;

class SubscriptionStateFactory
{
    /**
     * Create the appropriate state object based on subscription status
     */
    public static function makeState(Subscription $subscription): SubscriptionState
    {
        return match ($subscription->status) {
            'active' => new ActiveState($subscription),
            'grace_period' => new GracePeriodState($subscription),
            'canceled' => new CanceledState($subscription),
            'expired' => new ExpiredState($subscription),
            default => new ExpiredState($subscription), // Default to expired for unknown states
        };
    }
}
