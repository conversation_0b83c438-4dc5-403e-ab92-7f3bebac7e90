<?php

namespace Modules\Subscription\States;

use Modules\Payment\Models\Payment;
use Modules\Subscription\Models\Plan;
use Modules\Subscription\Models\Subscription;

class ExpiredState extends SubscriptionState
{
    /**
     * Get the state identifier
     */
    public function getState(): string
    {
        return 'expired';
    }

    /**
     * Check if subscription is expired
     */
    public function isExpired(): bool
    {
        return true;
    }

    /**
     * Handle subscription renewal
     * Can reactivate an expired subscription
     */
    public function renew(int $days = 30): Subscription
    {
        // Reactivate the subscription
        $this->subscription->starts_at = now();
        $this->subscription->ends_at = now()->addDays($days);
        $this->subscription->grace_period_ends_at = null;
        $this->subscription->payment_status = 'paid';
        $this->subscription->last_payment_error = null;

        // Transition back to active state
        $this->subscription->setSubscriptionState(new ActiveState($this->subscription));

        $this->subscription->save();

        return $this->subscription;
    }

    /**
     * <PERSON>le completed payment
     * Can reactivate an expired subscription
     */
    public function handlePaymentCompleted(Payment $payment): void
    {
        // This can be used to reactivate an expired subscription
        $this->subscription->starts_at = now();
        $this->subscription->ends_at = now()->addDays($this->subscription->plan->invoice_period ?? 30);
        $this->subscription->payment_status = 'paid';
        $this->subscription->last_payment_error = null;
        $this->subscription->grace_period_ends_at = null;

        // Transition back to active state
        $this->subscription->setSubscriptionState(new ActiveState($this->subscription));

        $this->subscription->save();
    }

    /**
     * Handle failed payment
     */
    public function handlePaymentFailed(Payment $payment, string $reason): void
    {
        // Record the failure but remain expired
        $this->subscription->payment_status = 'failed';
        $this->subscription->last_payment_error = $reason;
        $this->subscription->save();

        // Record failed attempt
        $this->subscription->paymentAttempts()->create([
            'payment_id' => $payment->id,
            'status' => 'failed',
            'error_message' => $reason,
            'occurred_at' => now(),
        ]);
    }

    /**
     * Switch to a new plan
     * Allows switching even in expired state by creating a new subscription
     */
    public function switchPlan(Plan $newPlan, bool $immediately = true): Subscription
    {
        // Create new subscription
        return $this->subscription->user->subscriptions()->create([
            'plan_id' => $newPlan->id,
            'status' => 'active',
            'starts_at' => now(),
            'ends_at' => now()->addDays($newPlan->invoice_period),
            'was_switched' => true,
        ]);
    }
}
