<?php

namespace Modules\Subscription\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

class PlanEntitlement extends Pivot
{
    protected $casts = [
        'access_details' => 'json',
    ];

    public function hasModule(string $moduleName): bool
    {
        $modules = $this->access_details['modules'] ?? [];

        return in_array($moduleName, $modules);
    }

    public function getAvailableModules(): array
    {
        return $this->access_details['modules'] ?? [];
    }

    //
    public static function booted(): void
    {
        static::creating(function ($record) {
            // Automatically set the access details to an empty array if not provided
            if (empty($record->access_details)) {
                $record->access_details = json_encode([]);
            }
        });
    }
}
