<?php

namespace Modules\Subscription\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Entitlement extends Model
{
    protected $fillable = [
        'name',
        'description',
        'feature_key', // unique identifier for programmatic checks
        'type', // module, feature, etc.
    ];

    /**
     * Relationship to subscription plans
     */
    public function plans(): BelongsToMany
    {
        return $this->belongsToMany(Plan::class, 'plan_entitlements')
            ->using(PlanEntitlement::class)
            ->withPivot('access_details');
    }

    /**
     * Get plans with specific access details
     */
    public function plansWithAccess(array $accessDetails): BelongsToMany
    {
        return $this->plans()->whereJsonContains('plan_entitlements.access_details', $accessDetails);
    }

    /**
     * Scope for entitlements of a specific type
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for module entitlements
     */
    public function scopeModules(Builder $query): Builder
    {
        return $query->where('type', 'module');
    }

    /**
     * Scope for feature entitlements
     */
    public function scopeFeatures(Builder $query): Builder
    {
        return $query->where('type', 'feature');
    }

    /**
     * Get entitlements by feature key
     */
    public function scopeByFeatureKey(Builder $query, string $key): Builder
    {
        return $query->where('feature_key', $key);
    }

    /**
     * Find an entitlement by its feature key
     */
    public static function findByFeatureKey(string $key): ?Entitlement
    {
        return static::byFeatureKey($key)->first();
    }

    /**
     * Create or update an entitlement with the given feature key
     */
    public static function createOrUpdateByFeatureKey(string $featureKey, array $attributes): Entitlement
    {
        return static::updateOrCreate(
            ['feature_key' => $featureKey],
            $attributes
        );
    }
}
