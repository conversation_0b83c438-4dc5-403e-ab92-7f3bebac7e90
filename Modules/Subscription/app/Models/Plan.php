<?php

namespace Modules\Subscription\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plan extends Model
{
    protected $fillable = [
        'slug',
        'name',
        'description',
        'is_active',
        'price',
        'trial_period',
        'trial_interval',
        'invoice_period',
        'invoice_interval',
        'grace_period',
        'grace_interval',
        // 'active_subscribers_limit',
        // 'active_subscribers_count',
        'sort',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'price' => 'float',
        // 'active_subscribers_limit' => 'integer',
        // 'active_subscribers_count' => 'integer',
        'trial_period' => 'integer',
        'invoice_period' => 'integer',
        'grace_period' => 'integer',
        'sort' => 'integer',
    ];

    /**
     * Relationship to entitlements
     */
    public function entitlements(): BelongsToMany
    {
        return $this->belongsToMany(Entitlement::class, 'plan_entitlements')
            ->using(PlanEntitlement::class)
            ->withPivot('access_details');
    }

    /**
     * Relationship to user subscriptions
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get active subscriptions for this plan
     */
    public function activeSubscriptions(): HasMany
    {
        return $this->subscriptions()->active();
    }

    /**
     * Check if plan has a specific entitlement with optional access requirements
     */
    public function hasEntitlement(string $entitlementKey, ?array $accessKey = null): bool
    {
        /** todo: remove module type and add a feature key called module_access and in required_access add an array with accessible modules
             this will be used for another models like course_access and etc
         */
        return $this->entitlements()
            ->where('feature_key', $entitlementKey)
            ->when($accessKey, function ($query) use ($accessKey) {
                return $query->where(function ($q) use ($accessKey) {
                    // Complex access check logic
                    $q->whereJsonContains('plan_entitlements.access_details', $accessKey);
                });
            })
            ->exists();
    }

    /**
     * Get a specific entitlement with access details
     */
    public function getEntitlementAccess(string $entitlementKey): ?array
    {
        $entitlement = $this->entitlements()
            ->where('feature_key', $entitlementKey)
            ->first();

        return $entitlement ? [
            'entitlement' => $entitlement,
            'access_details' => $entitlement->pivot->access_details ?? null,
        ] : null;
    }

    /**
     * Check if this plan is free
     */
    public function isFree(): bool
    {
        return $this->price <= 0;
    }

    /**
     * Check if this plan has trial
     */
    public function hasTrial(): bool
    {
        return $this->trial_period > 0;
    }

    /**
     * Get trial days
     */
    public function getTrialDays(): int
    {
        if (! $this->hasTrial()) {
            return 0;
        }

        if ($this->trial_interval === 'day') {
            return $this->trial_period;
        } elseif ($this->trial_interval === 'week') {
            return $this->trial_period * 7;
        } elseif ($this->trial_interval === 'month') {
            return $this->trial_period * 30;
        } elseif ($this->trial_interval === 'year') {
            return $this->trial_period * 365;
        }

        return 0;
    }

    /**
     * Get grace period days
     */
    public function getGraceDays(): int
    {
        if ($this->grace_interval === 'day') {
            return $this->grace_period;
        } elseif ($this->grace_interval === 'week') {
            return $this->grace_period * 7;
        } elseif ($this->grace_interval === 'month') {
            return $this->grace_period * 30;
        }

        return 0;
    }

    /**
     * Get invoice period in days
     */
    public function getInvoiceDays(): int
    {
        if ($this->invoice_interval === 'day') {
            return $this->invoice_period;
        } elseif ($this->invoice_interval === 'week') {
            return $this->invoice_period * 7;
        } elseif ($this->invoice_interval === 'month') {
            return $this->invoice_period * 30;
        } elseif ($this->invoice_interval === 'year') {
            return $this->invoice_period * 365;
        }

        return 30; // Default to 30 days
    }

    /**
     * Scope for active plans
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if plan has reached subscriber limit
     */
    public function hasReachedSubscriberLimit(): bool
    {
        // if ($this->active_subscribers_limit <= 0) {
        //     return false; // No limit
        // }

        // return $this->active_subscribers_count >= $this->active_subscribers_limit;
    }

    /**
     * Sync entitlement access to this plan
     *
     * @param  array  $entitlementData  [entitlement_id => ['access_details' => [...]], ...]
     * @return $this
     */
    public function syncEntitlementAccess(array $entitlementData): self
    {
        $this->entitlements()->sync($entitlementData);

        return $this;
    }

    /**
     * Add entitlement with access details to this plan
     *
     * @param  Entitlement|int  $entitlement
     * @return $this
     */
    public function addEntitlement($entitlement, ?array $accessDetails = null): self
    {
        $entitlementId = $entitlement instanceof Entitlement ? $entitlement->id : $entitlement;

        $this->entitlements()->attach($entitlementId, [
            'access_details' => $accessDetails ? json_encode($accessDetails) : null,
        ]);

        return $this;
    }

    /**
     * Remove entitlement from this plan
     *
     * @param  Entitlement|int  $entitlement
     * @return $this
     */
    public function removeEntitlement($entitlement): self
    {
        $entitlementId = $entitlement instanceof Entitlement ? $entitlement->id : $entitlement;
        $this->entitlements()->detach($entitlementId);

        return $this;
    }
}
