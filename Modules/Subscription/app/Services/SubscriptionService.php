<?php

namespace Modules\Subscription\Services;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Modules\Payment\Models\Payment;
use Modules\Subscription\Models\Plan;
use Modules\Subscription\Models\Subscription;

class SubscriptionService
{
    /**
     * Assign a free plan to a user
     */
    public function assignFreePlan(User $user): Subscription
    {
        // Find the free plan
        $freePlan = Plan::where('is_active', true)
            ->where('price', 0)
            ->first();

        if (! $freePlan) {
            // Create free plan if it doesn't exist
            $freePlan = Plan::create([
                'name' => 'Free',
                'slug' => 'free',
                'price' => 0,
                'is_active' => true,
                'invoice_period' => 30,
                'invoice_interval' => 'day',
                'trial_period' => 0,
                'trial_interval' => 'day',
                'grace_period' => 0,
                'grace_interval' => 'day',
            ]);
        }

        // Check if user already has an active subscription
        $existingSubscription = $user->activeSubscription();

        if ($existingSubscription) {
            // If the existing subscription is for the free plan, just return it
            if ($existingSubscription->plan_id === $freePlan->id) {
                return $existingSubscription;
            }

            // Otherwise, cancel the existing subscription and create a new free one
            $existingSubscription->cancel(true);
        }

        // Create subscription record
        $subscription = $user->subscriptions()->create([
            'plan_id' => $freePlan->id,
            'status' => 'active',
            'payment_status' => 'paid',
            'starts_at' => Carbon::now(),
            'ends_at' => Carbon::now()->addDays($freePlan->getInvoiceDays()),
        ]);

        // Update plan subscribers count
        // $freePlan->increment('active_subscribers_count');

        return $subscription;
    }

    /**
     * Subscribe a user to a specific plan
     */
    public function subscribeToPlan(User $user, Plan $plan, bool $withTrial = true): Subscription
    {
        // Check if the plan has reached its subscriber limit
        // if ($plan->hasReachedSubscriberLimit()) {
        //     throw new \Exception('This plan has reached its maximum number of subscribers.');
        // }

        // Check if user already has an active subscription
        $existingSubscription = $user->activeSubscription();

        if ($existingSubscription) {
            // If the existing subscription is for the same plan, just return it
            if ($existingSubscription->plan_id === $plan->id) {
                return $existingSubscription;
            }

            // Otherwise, cancel the existing subscription
            $existingSubscription->cancel(true);
        }

        // Determine if trial should be applied
        $applyTrial = $withTrial && $plan->hasTrial();
        $startDate = Carbon::now();

        // Calculate end date based on trial or invoice period
        $endDate = $applyTrial
            ? $startDate->copy()->addDays($plan->getTrialDays())
            : $startDate->copy()->addDays($plan->getInvoiceDays());

        // Create the subscription
        $subscription = $user->subscriptions()->create([
            'plan_id' => $plan->id,
            'status' => 'active',
            'payment_status' => $plan->isFree() || $applyTrial ? 'paid' : 'pending',
            'starts_at' => $startDate,
            'ends_at' => $endDate,
        ]);

        // Update plan subscribers count
        // $plan->increment('active_subscribers_count');

        return $subscription;
    }

    /**
     * Renew a subscription
     */
    public function renewSubscription(Subscription $subscription, ?Payment $payment = null): Subscription
    {
        if (! $subscription->plan) {
            throw new \Exception('Subscription is not associated with a plan.');
        }

        // Set new subscription period
        $subscription->starts_at = Carbon::now();
        $subscription->ends_at = Carbon::now()->addDays($subscription->plan->getInvoiceDays());
        $subscription->status = 'active';
        $subscription->grace_period_ends_at = null;
        $subscription->payment_status = 'paid';
        $subscription->last_payment_error = null;
        $subscription->save();

        // Associate payment if provided
        if ($payment) {
            $subscription->payments()->attach($payment);
        }

        return $subscription;
    }

    /**
     * Cancel a subscription
     */
    public function cancelSubscription(Subscription $subscription, bool $immediately = false): Subscription
    {
        return $subscription->cancel($immediately);
    }

    /**
     * Switch a user's subscription to a new plan
     */
    public function switchPlan(User $user, Plan $newPlan, bool $immediately = true): Subscription
    {
        $currentSubscription = $user->activeSubscription();

        if (! $currentSubscription) {
            // If no active subscription, just create a new one
            return $this->subscribeToPlan($user, $newPlan);
        }

        return $currentSubscription->switchPlan($newPlan, $immediately);
    }

    /**
     * Process a subscription payment
     */
    public function processPayment(Subscription $subscription, Payment $payment): Subscription
    {
        if ($payment->status === 'completed') {
            $subscription->handlePaymentCompleted($payment);
        } elseif ($payment->status === 'failed') {
            $subscription->handlePaymentFailed($payment, $payment->error_message ?? 'Unknown payment error');
        }

        return $subscription;
    }

    /**
     * Find subscriptions that need renewal (expiring soon)
     */
    public function findSubscriptionsNeedingRenewal(int $daysBeforeExpiration = 7): Collection
    {
        return Subscription::where('status', 'active')
            ->where('ends_at', '<=', now()->addDays($daysBeforeExpiration))
            ->where('ends_at', '>', now())
            ->get();
    }

    /**
     * Find expired subscriptions
     */
    public function findExpiredSubscriptions(): Collection
    {
        return Subscription::where(function ($query) {
            $query->where('status', 'active')
                ->where('ends_at', '<', now());
        })->get();
    }

    /**
     * Send expiration notifications to users with expiring subscriptions
     *
     * @return int Number of notifications sent
     */
    public function sendExpirationNotifications(int $daysBeforeExpiration = 7): int
    {
        $count = 0;
        $expiringSubscriptions = $this->findSubscriptionsNeedingRenewal($daysBeforeExpiration);

        foreach ($expiringSubscriptions as $subscription) {
            try {
                $subscription->sendExpirationNotification();
                $count++;
            } catch (\Exception $e) {
                Log::error('Failed to send expiration notification', [
                    'subscription_id' => $subscription->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $count;
    }

    /**
     * Expire subscriptions that have passed their end date
     *
     * @return int Number of subscriptions expired
     */
    public function expireOverdueSubscriptions(): int
    {
        $count = 0;
        $expiredSubscriptions = $this->findExpiredSubscriptions();

        foreach ($expiredSubscriptions as $subscription) {
            try {
                $subscription->status = 'expired';
                $subscription->save();

                // Reduce active subscribers count for the plan
                // if ($subscription->plan) {
                // $subscription->plan->decrement('active_subscribers_count');
                // }

                $count++;
            } catch (\Exception $e) {
                Log::error('Failed to expire subscription', [
                    'subscription_id' => $subscription->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $count;
    }
}
