<?php

namespace Modules\Subscription\Services;

use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Modules\Course\Models\Course;
use Modules\Course\Models\Module;
use Modules\Subscription\Models\Entitlement;
use Modules\Subscription\Models\Plan;

class EntitlementService
{
    /**
     * Check if user can access a specific module
     */
    public function canAccessModule(User $user, Module $module): bool
    {
        // Check if user has entitlement for this specific module
        return $user->hasEntitlement('module_access', [
            'module_id' => $module->id,
            'allowed' => true,
        ]);
    }

    /**
     * Get all modules the user can access in a course
     */
    public function getAccessibleModules(User $user, Course $course): Collection
    {
        return $course->modules->filter(function ($module) use ($user) {
            return $this->canAccessModule($user, $module);
        });
    }

    /**
     * Create a new entitlement
     */
    public function createEntitlement(string $name, string $featureKey, string $type = 'feature', ?string $description = null): Entitlement
    {
        return Entitlement::create([
            'name' => $name,
            'feature_key' => $featureKey,
            'type' => $type,
            'description' => $description ?? '',
        ]);
    }

    /**
     * Check if a plan has access to a specific feature
     */
    public function planHasFeatureAccess(Plan $plan, string $featureKey, ?array $requiredAccess = null): bool
    {
        return $plan->hasEntitlement($featureKey, $requiredAccess);
    }

    /**
     * Add entitlement to a plan
     *
     * @param  Entitlement|string  $entitlement
     */
    public function addEntitlementToPlan(Plan $plan, $entitlement, ?array $accessDetails = null): Plan
    {
        // If the entitlement is a string, assume it's a feature key and find the entitlement
        if (is_string($entitlement)) {
            $entitlementModel = Entitlement::firstWhere('feature_key', $entitlement);

            if (! $entitlementModel) {
                // Create the entitlement if it doesn't exist
                $entitlementModel = Entitlement::create([
                    'name' => ucwords(str_replace('_', ' ', $entitlement)),
                    'feature_key' => $entitlement,
                    'type' => 'feature',
                ]);
            }

            $entitlement = $entitlementModel;
        }

        $plan->addEntitlement($entitlement, $accessDetails);

        return $plan;
    }

    /**
     * Add module access entitlement to a plan
     */
    public function addModuleAccessToPlan(Plan $plan, Module $module, bool $allowed = true): Plan
    {
        // Find or create module access entitlement
        $entitlement = Entitlement::firstOrCreate([
            'feature_key' => 'module_access',
            'type' => 'module',
        ], [
            'name' => 'Module Access',
            'description' => 'Controls access to specific course modules',
        ]);

        // Add the entitlement with specific module access
        $accessDetails = [
            'module_id' => $module->id,
            'allowed' => $allowed,
        ];

        return $this->addEntitlementToPlan($plan, $entitlement, $accessDetails);
    }

    /**
     * Add course access entitlement to a plan
     */
    public function addCourseAccessToPlan(Plan $plan, Course $course, bool $allowed = true): Plan
    {
        // Find or create course access entitlement
        $entitlement = Entitlement::firstOrCreate([
            'feature_key' => 'course_access',
            'type' => 'course',
        ], [
            'name' => 'Course Access',
            'description' => 'Controls access to specific courses',
        ]);

        // Add the entitlement with specific course access
        $accessDetails = [
            'course_id' => $course->id,
            'allowed' => $allowed,
        ];

        return $this->addEntitlementToPlan($plan, $entitlement, $accessDetails);
    }

    /**
     * Get all entitlements available to a user
     */
    public function getUserEntitlements(User $user): ?Collection
    {
        $subscription = $user->activeSubscription();
        if (! $subscription || ! $subscription->plan) {
            return collect();
        }

        return $subscription->plan->entitlements;
    }

    /**
     * Check if a user has a specific feature
     */
    public function userHasFeature(User $user, string $featureKey, ?array $requiredAccess = null): bool
    {
        $cacheKey = "user_feature_{$user->id}_{$featureKey}".($requiredAccess ? '_'.md5(json_encode($requiredAccess)) : '');

        return Cache::remember($cacheKey, now()->addMinutes(5), function () use ($user, $featureKey, $requiredAccess) {
            return $user->hasEntitlement($featureKey, $requiredAccess);
        });
    }
}
