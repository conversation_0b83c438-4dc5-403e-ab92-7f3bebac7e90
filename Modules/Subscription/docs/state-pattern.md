# Subscription State Pattern Implementation

This implementation uses the State Design Pattern to manage subscription status transitions. This pattern encapsulates state-specific behavior in separate classes, making the code more maintainable and reducing complex conditional logic.

## Core Components

### Base State Class
- `SubscriptionState` - The abstract base class defining the interface for all concrete state classes.

### Concrete State Classes
- `ActiveState` - Represents an active subscription
- `GracePeriodState` - Represents a subscription in grace period (after payment failure)
- `CanceledState` - Represents a canceled subscription
- `ExpiredState` - Represents an expired subscription

### Factory Class
- `SubscriptionStateFactory` - Creates the appropriate state object based on a subscription's status

### Modified Subscription Model
- The Subscription model now delegates state-specific behavior to the appropriate state class

## State Transitions

All state transitions are now handled by the appropriate state class:

1. **Active → Grace Period**: When payment fails on an active subscription
2. **Active → Canceled**: When a subscription is canceled
3. **Active → Expired**: When a subscription reaches its end date without renewal
4. **Grace Period → Active**: When payment is successful during grace period
5. **Grace Period → Expired**: When grace period ends without payment
6. **Canceled → Active**: Can be reactivated if needed
7. **Expired → Active**: Can be renewed

## Benefits of State Pattern

1. **Cleaner Code**: State-specific behavior is isolated in separate classes
2. **Easier Maintenance**: Adding a new state or modifying existing state behavior is simpler
3. **Reduced Complexity**: Eliminates large conditional blocks in the Subscription model
4. **Better Testability**: State classes can be tested independently
5. **Clear State Transitions**: Makes state transitions explicit and self-documenting

## Usage Example

```php
// The model delegates to the appropriate state class
$subscription->switchPlan($newPlan, $immediately);
$subscription->cancel($immediately);
$subscription->handlePaymentCompleted($payment);
```

Each of these method calls will be handled by the appropriate state class based on the current state of the subscription.
