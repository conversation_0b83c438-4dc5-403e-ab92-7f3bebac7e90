# Using the Subscription State Pattern

This document provides a guide on how to use the new State Pattern implementation for subscription status transitions.

## Basic Usage

The Subscription model now uses a state pattern for managing transitions. You can continue to use the existing methods, and the state pattern will handle the transitions under the hood:

```php
// These methods work the same as before, but use the state pattern internally
$subscription->cancel($immediately);
$subscription->renew($days);
$subscription->switchPlan($newPlan, $immediately);
$subscription->handlePaymentCompleted($payment);
$subscription->handlePaymentFailed($payment, $reason);
```

## Status Checks

Status checks delegate to the current state object:

```php
$subscription->isActive();
$subscription->isInGracePeriod();
$subscription->isCanceled();
$subscription->isExpired();
```

## State Transitions

All state transitions are now managed by the appropriate state class, making the code more maintainable and easier to understand. Here are the supported transitions:

### Active State
- Can transition to **Grace Period** (when payment fails)
- Can transition to **Canceled** (when subscription is canceled)
- Can transition to **Expired** (when switching plans immediately)

### Grace Period State
- Can transition to **Active** (when payment is made)
- Can transition to **Canceled** (when subscription is canceled)
- Can transition to **Expired** (when grace period ends without payment)

### Canceled State
- Can transition to **Active** (through renew method)

### Expired State
- Can transition to **Active** (through renew method)

## Advanced Usage

For more advanced usage, you can access the state object directly:

```php
$stateObject = $subscription->getState();

// Get the current state name
$stateName = $stateObject->getState();

// You can also set a state directly (though this is rarely needed)
$subscription->setSubscriptionState(new ActiveState($subscription));
$subscription->save(); // Don't forget to save after changing state
```

## Implementation Notes

- The state pattern makes it easy to add new states or modify existing state behavior without changing the Subscription model.
- All state-specific logic is encapsulated in the appropriate state class.
- Each state class is responsible for implementing its own behavior and defining valid transitions.
- The model delegates all state-dependent behavior to the current state object.
