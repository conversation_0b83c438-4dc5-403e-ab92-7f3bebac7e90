```mermaid
stateDiagram-v2
    [*] --> Active: Subscribe/Create
    Active --> GracePeriod: Payment Fails
    Active --> Canceled: Cancel
    Active --> Expired: End Date Reached
    Active --> Expired: Switch Plan (immediately)
    
    GracePeriod --> Active: Payment Succeeds
    GracePeriod --> Expired: Grace Period Ends
    GracePeriod --> Canceled: Cancel
    
    Canceled --> Active: Renew/Reactivate
    Canceled --> [*]: Permanent Deletion
    
    Expired --> Active: Renew/Reactivate
    Expired --> [*]: Permanent Deletion
```

# Subscription State Diagram

The diagram above illustrates the possible state transitions in the subscription system:

## States

- **Active**: A currently valid subscription that grants access to features
- **Grace Period**: A temporarily problematic subscription due to payment issues, but still granting access
- **Canceled**: A deliberately ended subscription, either immediately or at period end
- **Expired**: A subscription that has ended due to non-renewal or grace period expiry

## Transitions

### From Active
- **→ GracePeriod**: When a payment fails
- **→ Canceled**: When a user cancels their subscription
- **→ Expired**: When the subscription end date is reached without renewal
- **→ Expired**: When switching plans with immediate effect

### From Grace Period
- **→ Active**: When payment succeeds during the grace period
- **→ Expired**: When the grace period ends without successful payment
- **→ Canceled**: When a user cancels during the grace period

### From Canceled
- **→ Active**: When reactivating a canceled subscription

### From Expired
- **→ Active**: When renewing an expired subscription

## Implementation Note

Each state is implemented as its own class that encapsulates the behavior specific to that state. The Subscription model delegates state-specific behaviors to these classes, making the code more maintainable and reducing conditional complexity.
