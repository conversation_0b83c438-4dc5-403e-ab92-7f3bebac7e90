<?php

namespace Modules\Subscription\Tests\Unit;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Payment\Models\Payment;
use Modules\Subscription\Models\Plan;
use Modules\Subscription\Models\Subscription;
use Modules\Subscription\States\ActiveState;
use Modules\Subscription\States\CanceledState;
use Modules\Subscription\States\GracePeriodState;
use Tests\TestCase;

class SubscriptionStateTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Plan $plan;

    protected Subscription $subscription;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test user
        $this->user = User::factory()->create();

        // Create a test plan
        $this->plan = Plan::create([
            'name' => 'Test Plan',
            'slug' => 'test-plan',
            'price' => 99.99,
            'is_active' => true,
            'invoice_period' => 30,
            'invoice_interval' => 'day',
            'trial_period' => 7,
            'trial_interval' => 'day',
            'grace_period' => 3,
            'grace_interval' => 'day',
        ]);

        // Create a test subscription in active state
        $this->subscription = Subscription::create([
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
            'status' => 'active',
            'payment_status' => 'paid',
            'starts_at' => now(),
            'ends_at' => now()->addDays(30),
        ]);
    }

    /** @test */
    public function it_correctly_initializes_state_from_status()
    {
        $this->assertInstanceOf(ActiveState::class, $this->subscription->getState());
        $this->assertTrue($this->subscription->isActive());

        // Test other states
        $this->subscription->status = 'grace_period';
        $this->subscription->grace_period_ends_at = now()->addDays(3);
        $this->subscription->save();

        // Clear state cache to force reload
        $this->subscription->setSubscriptionState(null);

        $this->assertInstanceOf(GracePeriodState::class, $this->subscription->getState());
        $this->assertTrue($this->subscription->isInGracePeriod());
    }

    /** @test */
    public function it_transitions_from_active_to_canceled_state()
    {
        $this->assertTrue($this->subscription->isActive());

        // Cancel the subscription
        $this->subscription->cancel(true);

        $this->assertTrue($this->subscription->isCanceled());
        $this->assertInstanceOf(CanceledState::class, $this->subscription->getState());
    }

    /** @test */
    public function it_transitions_from_active_to_grace_period_on_payment_failure()
    {
        // Create a failed payment
        $payment = Payment::create([
            'amount' => 99.99,
            'currency' => 'USD',
            'gateway' => 'test',
            'status' => 'failed',
            'customer_email' => $this->user->email,
            'customer_name' => $this->user->name,
        ]);

        // Handle the failed payment
        $this->subscription->handlePaymentFailed($payment, 'Payment declined');

        // Check the state transition
        $this->assertTrue($this->subscription->isInGracePeriod());
        $this->assertInstanceOf(GracePeriodState::class, $this->subscription->getState());
        $this->assertEquals('failed', $this->subscription->payment_status);
        $this->assertNotNull($this->subscription->grace_period_ends_at);
    }

    /** @test */
    public function it_transitions_from_grace_period_to_active_on_payment_success()
    {
        // First put subscription in grace period
        $this->subscription->status = 'grace_period';
        $this->subscription->payment_status = 'failed';
        $this->subscription->grace_period_ends_at = now()->addDays(3);
        $this->subscription->save();

        // Create a successful payment
        $payment = Payment::create([
            'amount' => 99.99,
            'currency' => 'USD',
            'gateway' => 'test',
            'status' => 'completed',
            'customer_email' => $this->user->email,
            'customer_name' => $this->user->name,
        ]);

        // Handle the successful payment
        $this->subscription->handlePaymentCompleted($payment);

        // Check the state transition
        $this->assertTrue($this->subscription->isActive());
        $this->assertInstanceOf(ActiveState::class, $this->subscription->getState());
        $this->assertEquals('paid', $this->subscription->payment_status);
        $this->assertNull($this->subscription->grace_period_ends_at);
    }

    /** @test */
    public function it_transitions_from_expired_to_active_on_renewal()
    {
        // First put subscription in expired state
        $this->subscription->status = 'expired';
        $this->subscription->ends_at = now()->subDay();
        $this->subscription->save();

        // Renew the subscription
        $this->subscription->renew(30);

        // Check the state transition
        $this->assertTrue($this->subscription->isActive());
        $this->assertInstanceOf(ActiveState::class, $this->subscription->getState());
        $this->assertEquals('paid', $this->subscription->payment_status);
        $this->assertTrue(now()->addDays(29)->isBefore($this->subscription->ends_at));
    }

    /** @test */
    public function it_switches_plans_immediately_from_active_state()
    {
        // Create a new plan to switch to
        $newPlan = Plan::create([
            'name' => 'Premium Plan',
            'slug' => 'premium-plan',
            'price' => 199.99,
            'is_active' => true,
            'invoice_period' => 30,
        ]);

        // Switch the plan immediately
        $newSubscription = $this->subscription->switchPlan($newPlan, true);

        // Original subscription should be expired
        $this->subscription->refresh();
        $this->assertTrue($this->subscription->isExpired());

        // New subscription should be active with the new plan
        $this->assertEquals($newPlan->id, $newSubscription->plan_id);
        $this->assertTrue($newSubscription->isActive());
        $this->assertTrue($newSubscription->was_switched);
    }

    /** @test */
    public function it_creates_new_subscription_when_switching_plans_from_canceled_state()
    {
        // First put subscription in canceled state
        $this->subscription->status = 'canceled';
        $this->subscription->save();

        // Create a new plan to switch to
        $newPlan = Plan::create([
            'name' => 'Premium Plan',
            'slug' => 'premium-plan',
            'price' => 199.99,
            'is_active' => true,
        ]);

        // Switch the plan
        $newSubscription = $this->subscription->switchPlan($newPlan);

        // Original subscription remains canceled
        $this->subscription->refresh();
        $this->assertTrue($this->subscription->isCanceled());

        // New subscription should be active with the new plan
        $this->assertEquals($newPlan->id, $newSubscription->plan_id);
        $this->assertTrue($newSubscription->isActive());
    }
}
