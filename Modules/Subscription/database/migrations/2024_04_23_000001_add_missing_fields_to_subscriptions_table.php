<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->timestamp('expires_at')->nullable()->after('ends_at');
            $table->timestamp('grace_period_ends_at')->nullable()->after('expires_at');
            $table->string('payment_status')->nullable()->after('status');
            $table->text('last_payment_error')->nullable()->after('payment_status');
            $table->unsignedInteger('active_subscribers_count')->default(0)->after('last_payment_error');
        });
    }

    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn([
                'expires_at',
                'grace_period_ends_at',
                'payment_status',
                'last_payment_error',
                'active_subscribers_count',
            ]);
        });
    }
};
