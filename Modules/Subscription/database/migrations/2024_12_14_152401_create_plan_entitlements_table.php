<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('plan_entitlements', function (Blueprint $table) {
            $table->foreignId('plan_id')
                ->constrained()
                ->onDelete('cascade');
            $table->foreignId('entitlement_id')
                ->constrained()
                ->onDelete('cascade');
            //            $table->decimal('charges')->nullable();
            $table->json('access_details')->nullable(); // For granular access configuration

            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('plan_entitlements');
    }
};
