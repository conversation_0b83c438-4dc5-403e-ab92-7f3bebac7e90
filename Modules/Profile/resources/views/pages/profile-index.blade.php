
<?php
use function Livewire\Volt\{layout, state};

layout('profile::layouts.master');

?>


<div class="">
        <!-- Header -->
        <div class="max-w-6xl mx-auto flex justify-between items-center dark:text-white">
            <h1 class="text-xl font-semibold">
                @lang('Profile Home')
            </h1>

            <!-- Calendar Button -->
            <a href="{{ url('/') }}" type="button" class="py-2 px-3 inline-flex justify-center items-center text-start bg-white/10 border border-transparent text-sm font-medium rounded-lg shadow-sm align-middle hover:bg-white/20 focus:outline-none focus:ring-1 focus:ring-neutral-600">
                @lang('Back to Home')
            </a>
            <!-- End Calendar Button -->
        </div>
        <!-- End Header -->

    <!-- Card -->
    <div class="max-w-6xl mx-auto p-4 flex my-5 flex-col gap-3 justify-center  bg-white border border-gray-200 shadow-sm rounded-xl dark:bg-neutral-800 dark:border-neutral-700">
        <!-- Tab -->
        <div class=" flex justify-between items-center border-b border-gray-200 dark:border-neutral-700">
            <!-- Nav Tab -->
            <nav class="flex  gap-x-1" aria-label="Tabs" role="tablist">
                <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2.5 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2  hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2.5 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 active " id="hs-pro-tabs-bb-item-open" data-hs-tab="#hs-pro-tabs-bb-open" aria-controls="hs-pro-tabs-bb-open" role="tab" >
                    @lang('Courses')
                </button>
                <button type="button" class="hs-tab-active:after:bg-gray-800 hs-tab-active:text-gray-800 px-2.5 py-1.5 mb-2 relative inline-flex justify-center items-center gap-x-2  hover:bg-gray-100 text-gray-500 hover:text-gray-800 text-sm rounded-lg disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 after:absolute after:-bottom-2 after:inset-x-2.5 after:z-10 after:h-0.5 after:pointer-events-none dark:hs-tab-active:text-neutral-200 dark:hs-tab-active:after:bg-neutral-400 dark:text-neutral-500 dark:hover:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700  " id="hs-pro-tabs-bb-item-archived" data-hs-tab="#hs-pro-tabs-bb-archived" aria-controls="hs-pro-tabs-bb-archived" role="tab" >
                    @lang('Exams')
                </button>
            </nav>
            <!-- End Nav Tab -->

            <!-- Notifications Button Icon -->
            <div class="hs-tooltip relative inline-block mb-3">
                <a class="hs-tooltip-toggle w-7 h-7 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                   href="#">
                    <svg class="flex-shrink-0 w-4 h-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" ><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                </a>
                <span class="hs-tooltip-content hs-tooltip-shown:opacity-100 hs-tooltip-shown:visible opacity-0 inline-block absolute invisible z-20 py-1.5 px-2.5 bg-gray-900 text-xs text-white rounded-lg dark:bg-neutral-700" role="tooltip">
                                    Preferences
                                  </span>
            </div>
            <!-- End Notifications Button Icon -->
        </div>

        <div>
            <!-- Tab Content -->
            <div id="hs-pro-tabs-bb-open" role="tabpanel" aria-labelledby="hs-pro-tabs-bb-item-open">
                <!-- Empty State -->
                <div class="p-5 flex flex-col gap-4">
                    @foreach(\Modules\Course\Models\Course::all() as $course)
                        <x-ui.card-list-item :course="$course"/>

                    @endforeach
{{--                    @dd((new \Modules\Subscription\Services\EntitlementService())->getAccessibleModules(auth()->user(), \Modules\Course\Models\Course::first()))--}}
{{--                    @dd(\Modules\Subscription\Models\Plan::first()->entitlements()->attach(\Modules\Subscription\Models\Entitlement::first(), ['access_details' => json_encode(['module_id' => \Modules\Course\Models\Course::first()->id, 'access' => true])]));--}}
                </div>
                <!-- End Empty State -->
            </div>
            <!-- End Tab Content -->

            <!-- Tab Content -->
            <div id="hs-pro-tabs-bb-archived" class="hidden" role="tabpanel" aria-labelledby="hs-pro-tabs-bb-item-archived">
                <!-- Empty State -->
                <div class="p-5  flex flex-col justify-center items-center text-center">
                    <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/>
                        <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10"/>
                        <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                        <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                        <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/>
                        <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30"/>
                        <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                        <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                        <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                        <g filter="url(#filter8)">
                            <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"/>
                            <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"/>
                            <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 "/>
                            <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/>
                            <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/>
                        </g>
                        <defs>
                            <filter id="filter8" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="6"/>
                                <feGaussianBlur stdDeviation="6"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape"/>
                            </filter>
                        </defs>
                    </svg>

                    <div class="max-w-sm mx-auto">
                        <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                            @lang('Under Construction')
                        </p>
                        <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                            @lang('There are no content available at the moment.')
                        </p>
                    </div>

                    {{--                                    <a class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"--}}
                    {{--                                       href="#">--}}
                    {{--                                        Learn more--}}
                    {{--                                    </a>--}}
                </div>
                <!-- End Empty State -->
            </div>
            <!-- End Tab Content -->
        </div>
        <!-- End Tab -->
    </div>
    <!-- End Card -->

</div>
