<x-layouts.profile-page>

    <!-- ========== MAIN SIDEBAR ========== -->
    <aside id="hs-pro-sidebar" class="hs-overlay [--auto-close:lg]
  hs-overlay-open:translate-x-0
  -translate-x-full transition-all duration-300 transform
  w-[260px]
  hidden
  fixed inset-y-0 start-0 z-[60]
  bg-gray-50
  lg:block lg:translate-x-0 lg:end-auto lg:bottom-0
  dark:bg-neutral-800
 ">
        <div class="flex flex-col h-full max-h-full">
            <!-- Header -->
            <div class="p-5 text-center">
                <div class="flex size-24 rounded-full mx-auto mb-2">
                    <img class="object-cover size-full rounded-full" src="{{ auth()->user()->photo ?? get_avatar(name:auth()->user()->name) }}" alt="Image Description">
                </div>

                <p class="lg:hidden font-semibold text-gray-800 dark:text-neutral-200">
                    {{ auth()->user()->name }}
                </p>

                <div class="hidden lg:block">
                    <!-- More Dropdown -->
                    <div class="hs-dropdown relative inline-flex">
                        <button id="hs-pro-dnsmd" type="button" class="py-1 px-2 inline-flex justify-center items-center gap-x-1 font-semibold rounded-lg text-gray-800 hover:text-purple-600 focus:outline-none focus:text-purple-600 disabled:opacity-50 disabled:pointer-events-none dark:text-neutral-300 dark:hover:text-purple-500 dark:focus:text-purple-500">
                            {{ auth()->user()->name }}
                            <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                        </button>

                        <!-- Account Dropdown -->
                        <div class="hs-dropdown-menu hs-dropdown-open:opacity-100 w-60 transition-[opacity,margin] duration opacity-0 hidden z-10 bg-white rounded-xl shadow-[0_10px_40px_10px_rgba(0,0,0,0.08)] dark:shadow-[0_10px_40px_10px_rgba(0,0,0,0.2)] dark:bg-neutral-800" aria-labelledby="hs-pro-pnnmd">
                            <div class="p-1">
                                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
                                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15.5 2H8.6c-.4 0-.8.2-1.1.5-.3.3-.5.7-.5 1.1v12.8c0 .4.2.8.5 1.1.3.3.7.5 1.1.5h9.8c.4 0 .8-.2 1.1-.5.3-.3.5-.7.5-1.1V6.5L15.5 2z"/><path d="M3 7.6v12.8c0 .4.2.8.5 1.1.3.3.7.5 1.1.5h9.8"/><path d="M15 2v5h5"/></svg>
                                    @lang('Statements and Payments')
                                </a>
                                {{--                            <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">--}}
                                {{--                                <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" ><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>--}}
                                {{--                                Settings--}}
                                {{--                            </a>--}}
                                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
                                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/><path d="M12 17h.01"/></svg>
                                    @lang('Help and Support')
                                </a>
                                <a class="flex items-center gap-x-3 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" href="#">
                                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/><polyline points="16 17 21 12 16 7"/><line x1="21" x2="9" y1="12" y2="12"/></svg>
                                    @lang('Sign out')
                                </a>
                            </div>
                            @if(auth()->user()->institute->count() > 0)
                                <div class="p-1 border-t border-gray-200 dark:border-neutral-700">
                                    <a href="{{ url('/host') }}" type="button" class="flex gap-x-3 py-2 px-3 w-full rounded-lg text-sm text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-100 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                       data-hs-overlay="#hs-pro-dasadam">
                                        <svg class="flex-shrink-0 size-4 mt-0.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>
                                        @lang('Open a Host Dashboard')
                                    </a>
                                </div>
                            @endif
                        </div>
                        <!-- End Account Dropdown -->
                    </div>
                    <!-- End More Dropdown -->
                </div>

                <p class="text-xs text-gray-500 dark:text-neutral-500">
                    {{ __('Member Since') }}: {{ auth()->user()->created_at->translatedFormat('d F Y') }}
                </p>
            </div>
            <!-- End Header -->

            <!-- Content -->
            <div class="h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
                <!-- Nav -->
                <nav class="hs-accordion-group pt-0 w-full flex flex-col flex-wrap" data-hs-accordion-always-open>
                    <ul class="p-4 space-y-1.5 border-y border-gray-200 dark:border-neutral-700">
                        <!-- Link -->
                        <li>
                            <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:bg-gray-200 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 "
                               href="{{ route('profile') }}">
                                <svg class="flex-shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" ><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>
                                @lang('Home')
                            </a>
                        </li>
                        <!-- End Link -->
                        <!-- Link -->
                        <li>
                            <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:bg-gray-200 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 "
                               href="{{ route('profile.edit') }}">
                                <x-heroicon-o-briefcase class="flex-shrink-0 mt-0.5 size-4" />

                                @lang('Edit profile')
                            </a>
                        </li>
                        <!-- End Link -->

                        <!-- Link -->
                        {{--                    <li>--}}
                        {{--                        <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:bg-gray-200 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700"--}}
                        {{--                           href="#">--}}
                        {{--                            <svg class="flex-shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="14" x="2" y="5" rx="2"/><line x1="2" x2="22" y1="10" y2="10"/></svg>--}}
                        {{--                            @lang('Orders')--}}
                        {{--                        </a>--}}
                        {{--                    </li>--}}
                        <!-- End Link -->

                        <!-- Link -->
{{--                        <li>--}}
{{--                            <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:bg-gray-200 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700"--}}
{{--                               href="#">--}}
{{--                                <svg class="flex-shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 16 4 4 4-4"/><path d="M7 20V4"/><path d="m21 8-4-4-4 4"/><path d="M17 4v16"/></svg>--}}
{{--                                @lang('Transactions')--}}
{{--                            </a>--}}
{{--                        </li>--}}
                        <!-- End Link -->

                        <!-- Link -->
                        {{--                    <li>--}}
                        {{--                        <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:bg-gray-200 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700"--}}
                        {{--                           href="#">--}}
                        {{--                            <svg class="flex-shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 12 20 22 4 22 4 12"/><rect width="20" height="5" x="2" y="7"/><line x1="12" x2="12" y1="22" y2="7"/><path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"/><path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"/></svg>--}}
                        {{--                            Earn $25--}}
                        {{--                            <div class="ms-auto">--}}
                        {{--                                <span class="inline-flex items-center gap-1.5 py-px px-1.5 rounded-md text-[10px] leading-4 font-medium bg-purple-100 text-purple-800 dark:bg-purple-800/30 dark:text-purple-500">--}}
                        {{--                                  New--}}
                        {{--                                </span>--}}
                        {{--                            </div>--}}
                        {{--                        </a>--}}
                        {{--                    </li>--}}
                        <!-- End Link -->

                    </ul>

                    <ul class="p-4 space-y-0.5">
                        <!-- Link -->
                        <li class="hs-accordion active" id="projects-accordion">
                            <button type="button" class="hs-accordion-toggle py-1 px-3 flex justify-center items-center gap-x-1 text-xs text-gray-500 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-200 dark:hover:bg-neutral-700 dark:text-neutral-500 dark:focus:bg-neutral-700">
                                <svg class="hs-accordion-active:rotate-90 flex-shrink-0 size-3 transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m9 18 6-6-6-6"/></svg>
                                @lang('Recent Courses')
                            </button>

                            <div id="projects-accordion-sub" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300">
                                <ul class="hs-accordion-group mt-1 space-y-1.5" data-hs-accordion-always-open>

                                    @foreach(\Modules\Course\Models\Course::all() as $course)
                                        <!-- Link -->
                                        <li>
                                            <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-200 focus:outline-none focus:bg-gray-200 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700"
                                               href="{{ route('course.show', ['course' => $course])  }}">
                                                <img class="flex-shrink-0 size-5 rounded-full" src="{{ $course->getMediaUrlWithFallback() }}" alt="Image Description">
                                                {{ $course->title }}
                                            </a>
                                        </li>
                                        <!-- End Link -->

                                    @endforeach

                                </ul>
                            </div>
                        </li>
                        <!-- End Link -->
                    </ul>
                </nav>
                <!-- End Nav -->
            </div>
            <!-- End Content -->

            <!-- Footer -->
            <footer class="hidden lg:block py-2 px-5 border-t border-gray-200 dark:border-neutral-700">
                <div class="flex justify-between items-center">
                    <!-- Logo -->
                    <a href="{{ route('dashboard') }}" wire:navigate>
                        <x-application-logo class="block w-auto fill-current text-gray-800 dark:text-gray-200" />
                    </a>
                    <!-- End Logo -->

                    <!-- Dark Mode -->
                    <div>
                        <button type="button" class="hs-dark-mode-active:hidden flex hs-dark-mode group size-[38px] justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-200 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-theme-click-value="dark">
                            <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z"/></svg>
                        </button>
                        <button type="button" class="hs-dark-mode-active:flex hidden hs-dark-mode group size-[38px] justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none focus:outline-none focus:bg-gray-200 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-theme-click-value="light">
                            <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="4"/><path d="M12 2v2"/><path d="M12 20v2"/><path d="m4.93 4.93 1.41 1.41"/><path d="m17.66 17.66 1.41 1.41"/><path d="M2 12h2"/><path d="M20 12h2"/><path d="m6.34 17.66-1.41 1.41"/><path d="m19.07 4.93-1.41 1.41"/></svg>
                        </button>
                    </div>
                    <!-- End Dark Mode -->
                </div>
            </footer>
            <!-- End Footer -->

            <!-- Sidebar Close -->
            <div class="lg:hidden absolute top-3 -end-3 z-10">
                <button type="button" class="w-6 h-7 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-md border border-gray-200 bg-gray-50 text-gray-500 hover:bg-gray-50 focus:outline-none focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#hs-pro-sidebar" aria-controls="hs-pro-sidebar" aria-label="Toggle navigation">
                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="7 8 3 12 7 16"/><line x1="21" x2="11" y1="12" y2="12"/><line x1="21" x2="11" y1="6" y2="6"/><line x1="21" x2="11" y1="18" y2="18"/></svg>
                </button>
            </div>
            <!-- End Sidebar Close -->
        </div>
    </aside>
    <!-- ========== END MAIN SIDEBAR ========== -->

    <!-- ========== MAIN CONTENT ========== -->
    <main id="content" class="lg:ps-[260px]  ">
        <div class="max-w-7xl mx-auto py-5 sm:py-10 px-5 md:px-8 2xl:px-5 w-full min-h-screen">
            <!-- Card -->
            <div class="relative">

                {{ $slot }}
            </div>
            <!-- End Card -->
        </div>
        <x-ui.footer/>
    </main>
    <!-- ========== END MAIN CONTENT ========== -->
</x-layouts.profile-page>
