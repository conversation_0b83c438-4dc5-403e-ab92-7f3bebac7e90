<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;
use Modules\Profile\Http\Controllers\ProfileController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::group(['prefix' => 'profile'], function () {
    Volt::route('/', 'pages.profile-index')->name('profile');
    Volt::route('/edit', 'pages.profile-edit')->name('profile.edit');

    //    Route::resource('profile', ProfileController::class)->names('profile');
});
