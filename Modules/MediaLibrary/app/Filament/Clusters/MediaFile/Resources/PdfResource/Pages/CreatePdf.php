<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\PdfResource\Pages;

use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Storage;
use Imagick;
use ImagickDraw;
use ImagickPixel;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\PdfResource;
use Modules\MediaLibrary\Jobs\ProccessPdfJob;
use Modules\MediaLibrary\Utils\Pdf;

class CreatePdf extends CreateRecord
{
    protected static string $resource = PdfResource::class;

    protected array $pages = [];

    protected function getHeaderActions(): array
    {
        return [

        ];
    }

    /**
     * Prepare data just before validation.
     */
    protected function mutateFormDataBeforeValidate(array $data): array
    {
        if (empty($data['name']) && ! empty($data['path'])) {
            $data['name'] = pathinfo($data['path'], PATHINFO_FILENAME);
        }

        // Add initial processing status if you have such a field
        //        $data['processing_status'] = 'pending';
        return $data;
    }
    /**
     * @throws \Exception
     */
    //    protected function mutateFormDataBeforeCreate(array $data): array
    //    {
    //        if (!$data['name']) {
    //            $data['name'] = $data['path'];
    //        }
    //
    //        $pages = [];
    //        $pdf = new Pdf(public_path('/storage/'.$data['path']));
    //        $pdf_slug = str($data['name'])->slug();
    //        foreach (range(1, $pdf->getNumberOfPages()) as $pageNumber) {
    //            $img_name = 'page_'.$pageNumber.'.jpg';
    //            $img_path = '/storage/'. $pdf_slug .'/' . $img_name;
    //
    //            is_dir(public_path('/storage/'. $pdf_slug)) || mkdir(public_path('/storage/'. $pdf_slug));
    //
    //            // save the image
    //            $pdf->setPage($pageNumber)->saveImage(public_path($img_path));
    //
    //            if (generalSetting()->watermark_enabled) {
    //                $image = $this->addTiledTextToImage($img_path);
    //                $img_path = '/storage/'. $pdf_slug .'/' . '/tiled_' . $img_name;
    //                file_put_contents(public_path($img_path), $image);
    //            }
    //
    //            // create a pdf file using $img_path
    //            $this->saveAsPdf($img_path, $pdf_slug, $img_name);
    //
    //            $pages[] = ['page_number' => $pageNumber, 'page_path' => $img_path];
    //
    //        }
    //
    // //        $data['pdf_pages'] = $pages;
    //        $this->pages = $pages;
    //
    //
    //        return parent::mutateFormDataBeforeCreate($data); // TODO: Change the autogenerated stub
    //    }

    private function addTiledTextToImage($path): Imagick
    {
        $image = new Imagick(public_path($path));

        $watermark = new Imagick;

        // todo:: get watermark from settings
        $text = generalSetting()->watermark_text;

        // Create a new drawing palette
        $draw = new ImagickDraw;

        $watermark->newImage(140, 80, new ImagickPixel('none'));

        $draw->setFillColor('grey');

        $draw->setFillOpacity(.5);

        $draw->setGravity(Imagick::GRAVITY_NORTHWEST);

        $watermark->annotateImage($draw, 10, 10, 0, $text);

        $draw->setGravity(Imagick::GRAVITY_SOUTHEAST);

        $watermark->annotateImage($draw, 5, 15, 0, $text);

        for ($w = 0; $w < $image->getImageWidth(); $w += 140) {
            for ($h = 0; $h < $image->getImageHeight(); $h += 80) {
                $image->compositeImage($watermark, Imagick::COMPOSITE_OVER, $w, $h);
            }
        }

        $image->setImageFormat('png');

        return $image;

    }

    /**
     * Perform actions after the record (MediaFile) is created.
     */
    protected function afterCreate(): void
    {
        // The $this->record is the newly created MediaFile instance
        if ($this->record && $this->record->path) {
            // Ensure the path exists in storage before dispatching
            if (Storage::disk('public')->exists($this->record->path)) {
                // Dispatch the job to the queue
                ProccessPdfJob::dispatch($this->record);
            } else {
                // Handle error: file not found
                $this->record->update(['processing_status' => 'failed', 'error_message' => 'Uploaded file not found for processing.']);
                // Optionally notify the user via Filament notification
            }
        }
    }

    // Remove mutateFormDataBeforeCreate if it only contained PDF processing logic
    // Remove addTiledTextToImage and saveAsPdf methods as they are now in the Job
    //    private function saveAsPdf(string $img_path, $pdf_slug, string $img_name)
    //    {
    //        $pdf = new Imagick();
    //        $pdf->readImage(public_path($img_path));
    //        $pdf->setImageFormat('pdf');
    //        $pdf->writeImages(public_path('/storage/'. $pdf_slug .'/' . $img_name . '.pdf'), true);
    //    }

}
