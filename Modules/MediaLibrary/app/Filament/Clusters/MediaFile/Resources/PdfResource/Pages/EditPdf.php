<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\PdfResource\Pages;

use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\EditRecord;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\PdfResource;

class EditPdf extends EditRecord
{
    protected static string $resource = PdfResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
        ];
    }
}
