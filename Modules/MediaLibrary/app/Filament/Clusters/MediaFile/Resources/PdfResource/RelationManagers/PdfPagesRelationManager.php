<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\PdfResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\MediaLibrary\Models\PdfPage;

class PdfPagesRelationManager extends RelationManager
{
    protected static string $relationship = 'pdf_pages';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                Forms\Components\Grid::make()->schema([

                    Forms\Components\Group::make([
                        Forms\Components\TextInput::make('page_number')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\FileUpload::make('page_path')
                            ->required(),
                    ]),

                    Forms\Components\ViewField::make('page_path')
                        ->label('Page Path')
                        ->viewData([
                            'path' => \Storage::url($this->cachedMountedTableActionRecord['page_path']),
                        ])
                        ->view('medialibrary::forms.components.view-field-pdf'),
                    //                    Forms\Components\Placeholder::make('page_path')
                    //                        ->content(fn(?PdfPage $record): string => url(\Storage::url($record?->page_path)) ?? '-'),
                ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('page_number')
            ->defaultSort('page_number', 'asc')
            ->columns([
                Tables\Columns\TextColumn::make('page_number'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                //                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
