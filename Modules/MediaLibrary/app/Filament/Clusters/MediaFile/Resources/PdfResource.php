<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Modules\MediaLibrary\Filament\Clusters\MediaFile;
use Modules\MediaLibrary\Models\Pdf;

class PdfResource extends Resource
{
    protected static ?string $model = Pdf::class;

    protected static ?string $slug = 'pdfs';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $cluster = MediaFile::class;

    public static function getLabel(): ?string
    {
        return __('PDF File');
    }

    public static function getPluralLabel(): ?string
    {
        return __('PDF Files');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                TextInput::make('name')
                    ->columnSpan(2)
                    ->live()
                    ->inlineLabel(),

                FileUpload::make('path')
                    ->required()
                    ->acceptedFileTypes(['application/pdf'])
                    ->inlineLabel()
//                    ->afterStateUpdated(fn(Set $set, $state) => $set('name', $state))
                    ->columnSpan(2)
                    ->getUploadedFileNameForStorageUsing(
                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                            ->prepend(date('Y_m_d').'_'),
                    ),

                //                Placeholder::make('created_at')
                //                    ->inlineLabel()
                //                    ->label(__('Created Date'))
                //                    ->content(fn(?Pdf $record): string => $record?->created_at?->diffForHumans() ?? '-'),
                //
                //                Placeholder::make('updated_at')
                //                    ->inlineLabel()
                //                    ->label(__('Last Modified Date'))
                //                    ->content(fn(?Pdf $record): string => $record?->updated_at?->diffForHumans() ?? '-'),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
                Action::make('re-upload')->icon('heroicon-o-arrow-uturn-up')->form(fn (Pdf $record) => [
                    FileUpload::make('path')
                        ->required()
                        ->acceptedFileTypes(['application/pdf'])
                        ->default($record->path)
                        ->inlineLabel()
                        ->getUploadedFileNameForStorageUsing(
                            fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                ->prepend('book-'),
                        ),
                ])->action(function (Pdf $record, array $data) {
                    // This Just replaces the pictures with the new ones, doesn't create new ones or touch the db
                    $pdf = new \Modules\MediaLibrary\Utils\Pdf(public_path('/storage/'.$data['path']));
                    if ($pdf->getNumberOfPages() == $record->pdf_pages()->count()) {
                        foreach (range(1, $pdf->getNumberOfPages()) as $pageNumber) {
                            $img_path = $record->pdf_pages()->firstWhere('page_number', $pageNumber)->page_path;
                            $pdf->setPage($pageNumber)->saveImage(public_path($img_path));
                        }
                        $record->path = $data['path'];
                        $record->save();
                        Notification::make()->title(__('pages re-uploaded'))->success()->send();
                    } else {
                        unlink(public_path('/storage/'.$data['path']));
                        Notification::make()->title(__('Must be same pdf pages counts.'))->warning()->send();
                    }

                }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\PdfResource\Pages\ListPdfs::route('/'),
            'create' => MediaFile\Resources\PdfResource\Pages\CreatePdf::route('/create'),
            'edit' => MediaFile\Resources\PdfResource\Pages\EditPdf::route('/{record}/edit'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            \Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\PdfResource\RelationManagers\PdfPagesRelationManager::class,
        ];
    }

    public function isReadOnly(): bool
    {
        return true;
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name'];
    }
}
