<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\AudioResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\AudioResource;

class EditAudio extends EditRecord
{
    protected static string $resource = AudioResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
