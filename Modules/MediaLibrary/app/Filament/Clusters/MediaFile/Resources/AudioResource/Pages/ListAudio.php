<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\AudioResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources\AudioResource;

class ListAudio extends ListRecords
{
    protected static string $resource = AudioResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
