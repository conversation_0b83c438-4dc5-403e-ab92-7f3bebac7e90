<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Modules\MediaLibrary\Filament\Clusters\MediaFile;
use Modules\MediaLibrary\Models\Audio;

class AudioResource extends Resource
{
    protected static ?string $model = Audio::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $cluster = MediaFile::class;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Group::make([
                    TextInput::make('name')

                        ->required(),

                    TextInput::make('author'),

                    Select::make('playlist_id')

                        ->relationship('playlist', 'name')
                        ->createOptionForm([
                            TextInput::make('name'),
                        ]),

                ])->columns(3)->columnSpanFull(),

                FileUpload::make('path')
                    ->hiddenLabel()
                    ->columnSpan(2)
                    ->acceptedFileTypes(['audio/*'])
                    ->getUploadedFileNameForStorageUsing(
                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                            ->prepend('audio-'),
                    ),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => MediaFile\Resources\AudioResource\Pages\ListAudio::route('/'),
            'create' => MediaFile\Resources\AudioResource\Pages\CreateAudio::route('/create'),
            'edit' => MediaFile\Resources\AudioResource\Pages\EditAudio::route('/{record}/edit'),
        ];
    }
}
