<?php

namespace Modules\MediaLibrary\Filament\Clusters\MediaFile\Resources;

use Filament\Forms;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Modules\MediaLibrary\Filament\Clusters\MediaFile;
use Modules\MediaLibrary\Models\Video;

class VideoResource extends Resource
{
    protected static ?string $model = Video::class;

    protected static ?string $slug = 'videos';

    protected static ?string $navigationIcon = 'heroicon-o-film';

    protected static ?string $cluster = MediaFile::class;

    public static function getLabel(): ?string
    {
        return __('Video');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Videos');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                TextInput::make('name')
                    ->label(__('Name'))
                    ->columnSpanFull()
                    ->inlineLabel()
                    ->required(),

//                TextInput::make('duration')
//                    ->readonly()
//                    ->dehydrated(false)
//                    ->columnSpanFull()
//                    ->inlineLabel(),

                Hidden::make('minutes')
                    ->default(0),
                Hidden::make('seconds')
                    ->default(0),

                FileUpload::make('path')
                    ->label(__('Path'))
                    ->columnSpan(2)
                    ->inlineLabel()
                    ->maxSize(204800) //  200 MB
                    ->acceptedFileTypes(['video/*'])
                    ->uploadProgressIndicatorPosition('left')
                    ->uploadButtonPosition('left')
                    ->removeUploadedFileButtonPosition('right')
                    ->preserveFilenames()
                    ->getUploadedFileNameForStorageUsing(
                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                            ->prepend('video-'),
                    )
                    ->afterStateUpdated(function (Forms\Get $get, $state, Forms\Set $set) {
                        if (! $state) {
                            return;
                        }

                        // For temporary uploaded files
                        if ($state instanceof TemporaryUploadedFile) {
                            $path = $state->getRealPath();
                        } else {
                            // For existing files
                            $path = storage_path('app/public/'.$state);
                        }

                        // Generate thumbnail or other metadata
                        // You could dispatch a job here as well
                        $duration = self::getVideoDuration($path);
                        $minutes = floor($duration / 60);
                        $seconds = $duration % 60;
                        $duration = gmdate('H:i:s', $duration);

                        $set('minutes', $minutes);
                        $set('seconds', $seconds);
//                        $set('duration', $duration);
                    }),

                // preview thumbnail https://customer-tara3gffqr183zil.cloudflarestream.com/3f90e217d452634a6208a51fdb520d5b/thumbnails/thumbnail.jpg
                Placeholder::make('preview')
                    ->hidden(fn($operation) => $operation === 'create')
                    ->content(function (Video $record) {
                        return new HtmlString('
                                    <a href="https://customer-'.config('services.cloudflare.code').'.cloudflarestream.com/'.$record->path.'/watch" target="_blank">
                                        <img src="https://customer-'.config('services.cloudflare.code').'.cloudflarestream.com/'.$record->path.'/thumbnails/thumbnail.jpg"  alt=""/>
                                    </a>
                                ');
                    }),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->sortable(),
                TextColumn::make('created_at')->dateTime()->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                EditAction::make(),
                DeleteAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => MediaFile\Resources\VideoResource\Pages\ListVideos::route('/'),
            'create' => MediaFile\Resources\VideoResource\Pages\CreateVideo::route('/create'),
            'edit' => MediaFile\Resources\VideoResource\Pages\EditVideo::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }

    protected static function getVideoDuration($path): float
    {
        try {
            // Using FFprobe to get video duration
            $command = 'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 '.escapeshellarg($path);
            $duration = (float) shell_exec($command);

            return $duration ?: 0;
        } catch (\Exception $e) {
            report($e);

            return 0;
        }
    }
}
