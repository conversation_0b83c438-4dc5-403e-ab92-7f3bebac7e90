<?php

namespace Modules\MediaLibrary\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class MediaLibraryPlugin implements Plugin
{
    public function getId(): string
    {
        return 'media-library';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverResources(
            in: __DIR__.'/Resources',
            for: 'Modules\\MediaLibrary\\Filament\\Resources'
        );
        $panel->discoverPages(
            in: __DIR__.'/Pages',
            for: 'Modules\\MediaLibrary\\Filament\\Pages'
        );
        $panel->discoverClusters(
            in: __DIR__.'/Clusters',
            for: 'Modules\\MediaLibrary\\Filament\\Clusters'
        );
    }

    public function boot(Panel $panel): void {}
}
