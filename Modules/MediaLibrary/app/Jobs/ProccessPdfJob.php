<?php

namespace Modules\MediaLibrary\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Modules\MediaLibrary\Models\Pdf;
use Modules\MediaLibrary\Models\PdfPage;
use setasign\Fpdi\Fpdi;

class ProccessPdfJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Pdf $pdf) {}

    public function handle()
    {
        Log::info("Processing PDF: {$this->pdf->name}");

        $pdfPath = storage_path('app/public/'.$this->pdf->path);

        $originalFileNameWithoutExt = pathinfo($this->pdf->path, PATHINFO_FILENAME);
        $outputDir = 'pdfs/'.$originalFileNameWithoutExt;

        if (! Storage::disk('public')->exists($outputDir)) {
            Storage::disk('public')->makeDirectory($outputDir);
        }

        // Open the PDF to count pages only
        $fpdi = new Fpdi;
        $pageCount = $fpdi->setSourceFile($pdfPath);
        $templateId = $fpdi->importPage(1);
        // Get the size of the imported page
        $size = $fpdi->getTemplateSize($templateId);
        for ($pageNumber = 1; $pageNumber <= $pageCount; $pageNumber++) {
            // Create a new FPDI instance for each page
            $pagePdf = new Fpdi('P', 'mm', [$size['width'], $size['height']]);
            $pagePdf->setSourceFile($pdfPath);

            // First import the page to get its size
            $templateId = $pagePdf->importPage($pageNumber);

            // Format [width, height] in mm
            $pagePdf->AddPage();

            // Use the imported template
            $pagePdf->useTemplate($templateId);

            $pagePdfName = sprintf('page_%03d.pdf', $pageNumber); // e.g., page_001.pdf
            $outputFilePath = "$outputDir/$pagePdfName";
            $fullOutputPath = storage_path("app/public/$outputFilePath");

            $pagePdf->Output($fullOutputPath, 'F');

            PdfPage::create([
                'pdf_id' => $this->pdf->id,
                'page_number' => $pageNumber,
                'page_path' => $outputFilePath,
            ]);
        }
    }
}
