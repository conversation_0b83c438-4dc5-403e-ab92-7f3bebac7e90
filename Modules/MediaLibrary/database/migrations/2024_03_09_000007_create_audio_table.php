<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('playlists', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });

        Schema::create('audio', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('path');
            $table->string('minutes')->nullable();
            $table->string('seconds')->nullable();
            $table->string('author')->nullable();
            $table->foreignId('playlist_id')->nullable()->constrained('playlists')->nullOnDelete();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('audio');
        Schema::dropIfExists('playlists');
    }
};
