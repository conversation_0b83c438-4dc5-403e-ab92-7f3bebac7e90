<?php

namespace Modules\Affiliate\Filament\Resources\AffiliateResource\RelationManagers;

use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class AffiliateCouponsRelationManager extends RelationManager
{
    protected static string $relationship = 'coupons';

    protected static ?string $recordTitleAttribute = 'code';

    protected static ?string $title = 'Coupons';

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('code')
                    ->searchable()
                    ->sortable(),

            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),

                SelectFilter::make('created_at')

                    ->options([
                        'today' => 'Today',
                        'week' => 'Last 7 Days',
                        'month' => 'Last 30 Days',
                        'year' => 'This Year',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when($data['value'], function (Builder $query, $value) {
                            return match ($value) {
                                'today' => $query->whereDate('created_at', today()),
                                'week' => $query->whereDate('created_at', '>=', now()->subDays(7)),
                                'month' => $query->whereDate('created_at', '>=', now()->subDays(30)),
                                'year' => $query->whereYear('created_at', now()->year),
                            };
                        });
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->bulkActions([])  // Disabled bulk actions for safety
            ->striped()
            ->headerActions([
                // You can add custom actions here if needed
            ])
            ->actions([
            ]);
    }
}
