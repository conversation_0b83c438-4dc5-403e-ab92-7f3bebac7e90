<?php

namespace Modules\Affiliate\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Modules\Affiliate\Filament\Resources\AffiliateResource\Pages;
use Modules\Affiliate\Filament\Resources\AffiliateResource\RelationManagers\AffiliateCouponsRelationManager;
use Modules\Affiliate\Filament\Resources\AffiliateResource\RelationManagers\ReferralsRelationManager;
use Modules\Affiliate\Models\Affiliate;
use Modules\Affiliate\Services\CouponService;

class AffiliateResource extends Resource
{
    protected static ?string $model = Affiliate::class;

    protected static ?string $slug = 'affiliates';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getNavigationGroup(): ?string
    {
        return __('Payments');
    }

    public static function getLabel(): ?string
    {
        return __('Affiliate');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Affiliates');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('commission_rate')
                    ->required()
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(100),
                Forms\Components\TextInput::make('code')
                    ->required()
                    ->unique(ignoreRecord: true),
                Forms\Components\TextInput::make('total_earnings')
                    ->disabled()
                    ->dehydrated(false),
                Forms\Components\TextInput::make('total_referrals')
                    ->disabled()
                    ->dehydrated(false),
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->required(),
                Forms\Components\Toggle::make('is_active')
                    ->required(),

                Forms\Components\Toggle::make('is_active')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('code')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_rate')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_earnings')
                    ->money('usd')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_referrals')
                    ->sortable(),
                Tables\Columns\BooleanColumn::make('is_active')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('is_active')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Action::make('createCoupon')

                    ->icon('heroicon-o-ticket')
                    ->color('success')
                    ->form([
                        Forms\Components\Select::make('type')

                            ->options([
                                'percentage' => 'Percentage',
                                'fixed' => 'Fixed Amount',
                            ])
                            ->required(),
                        Forms\Components\TextInput::make('value')

                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, $get) {
                                if ($get('type') === 'percentage' && $state > 100) {
                                    $set('value', 100);
                                }
                            }),
                        Forms\Components\TextInput::make('max_uses')

                            ->numeric()
                            ->minValue(1)
                            ->nullable(),
                        Forms\Components\TextInput::make('min_order_value')
                            ->numeric()
                            ->required(),
                        Forms\Components\DateTimePicker::make('expires_at')

                            ->nullable(),
                    ])
                    ->action(function (Affiliate $record, array $data): void {
                        $couponService = app(CouponService::class);

                        $couponService->createCoupon(
                            affiliateId: $record->id,
                            type: $data['type'],
                            value: $data['value'],
                            min_order_value: $data['min_order_value'],
                            usageLimit: $data['max_uses'] ?? null,
                            expiresAt: $data['expires_at'] ?? null
                        );

                        Notification::make()
                            ->title('Coupon created successfully')
                            ->success()
                            ->send();
                    }),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ReferralsRelationManager::class,
            AffiliateCouponsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAffiliates::route('/'),
            'create' => Pages\CreateAffiliate::route('/create'),
            'edit' => Pages\EditAffiliate::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
