<?php

namespace Modules\Affiliate\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Affiliate\Filament\Resources\CouponResource\Pages\CreateCoupon;
use Modules\Affiliate\Filament\Resources\CouponResource\Pages\EditCoupon;
use Modules\Affiliate\Filament\Resources\CouponResource\Pages\ListCoupons;
use Modules\Affiliate\Filament\Resources\CouponResource\RelationManagers\OrdersRelationManager;
use Modules\Affiliate\Models\Coupon;

class CouponResource extends Resource
{
    protected static ?string $model = Coupon::class;

    protected static ?string $slug = 'coupons';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function getLabel(): ?string
    {
        return __('Coupon');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Coupons');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('code')
                ->required()
                ->unique(ignoreRecord: true),
            Forms\Components\Select::make('type')
                ->options([
                    'percentage' => 'Percentage',
                    'fixed' => 'Fixed Amount',
                ])
                ->required(),
            Forms\Components\TextInput::make('value')
                ->numeric()
                ->required(),
            Forms\Components\TextInput::make('min_order_value')
                ->numeric()
                ->required(),
            Forms\Components\TextInput::make('max_discount')
                ->numeric()
                ->nullable(),
            Forms\Components\DateTimePicker::make('starts_at')
                ->required(),
            Forms\Components\DateTimePicker::make('expires_at')
                ->required(),
            Forms\Components\TextInput::make('max_uses')
                ->numeric()
                ->required(),
            Forms\Components\Toggle::make('is_active')
                ->default(true),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code'),
                Tables\Columns\TextColumn::make('type'),
                Tables\Columns\TextColumn::make('value'),
                Tables\Columns\TextColumn::make('used_count'),
                Tables\Columns\TextColumn::make('expires_at'),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'percentage' => 'Percentage',
                        'fixed' => 'Fixed Amount',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            OrdersRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListCoupons::route('/'),
            'create' => CreateCoupon::route('/create'),
            'edit' => EditCoupon::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [];
    }
}
