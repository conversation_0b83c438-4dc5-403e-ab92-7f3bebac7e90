<?php

namespace Modules\Affiliate\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class AffiliatePlugin implements Plugin
{
    public function getId(): string
    {
        return 'affiliate';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverResources(
            in: __DIR__.'/Resources',
            for: 'Modules\\Affiliate\\Filament\\Resources'
        );
    }

    public function boot(Panel $panel): void {}
}
