<?php

namespace Modules\Affiliate\Services;

use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Modules\Affiliate\Models\AffiliateReferral;
use Modules\Affiliate\Models\Coupon;

class CouponService
{
    public function createCoupon(
        int $affiliateId,
        string $type,
        float $value,
        ?int $min_order_value = 0,
        ?int $usageLimit = null,
        ?Carbon $expiresAt = null
    ): Coupon {
        return Coupon::create([
            'affiliate_id' => $affiliateId,
            'code' => Coupon::generateUniqueCode(),
            'type' => $type,
            'min_order_value' => 0,
            'value' => $value,
            'max_uses' => $usageLimit,
            'expires_at' => $expiresAt,
        ]);
    }

    /**
     * Process order with coupon and handle affiliate commission
     */
    public function processOrderWithCoupon(string $couponCode, float $orderAmount, int $customerId): ?array
    {
        try {
            DB::beginTransaction();

            // Find and validate coupon
            $coupon = Coupon::where('code', $couponCode)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->with('affiliate')
                ->first();

            if (! $coupon || ! $coupon->isValid()) {
                return null;
            }

            // Calculate discount
            $discountAmount = $this->calculateDiscount($coupon, $orderAmount);
            $finalAmount = $orderAmount - $discountAmount;

            // if affiliate_id is null, then no commission
            if (! $coupon->affiliate_id) {
                DB::commit();

                return [
                    'discount_amount' => $discountAmount,
                    'final_amount' => $finalAmount,
                ];
            }

            // Create or get referral
            $referral = $this->createOrGetReferral($coupon->affiliate_id, $customerId);

            // Process commission based on the final amount (after discount)
            $commission = $this->processCommission($referral, $finalAmount);

            // Update coupon usage
            $coupon->increment('used_count');

            DB::commit();

            return [
                'discount_amount' => $discountAmount,
                'final_amount' => $finalAmount,
                //                'commission_amount' => $commission,
                //                'referral_id' => $referral->id
            ];

        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Calculate discount amount based on coupon type
     */
    protected function calculateDiscount(Coupon $coupon, float $amount): float
    {
        $discount = $coupon->type === 'percentage'
            ? $amount * ($coupon->value / 100)
            : min($coupon->value, $amount); // Don't allow discount larger than amount

        return round($discount, 2);
    }

    /**
     * Create or get existing referral
     */
    protected function createOrGetReferral(int $affiliateId, int $customerId): AffiliateReferral
    {
        // Check for existing referral
        $referral = AffiliateReferral::where('affiliate_id', $affiliateId)
            ->where('referred_user_id', $customerId)
            ->first();

        if (! $referral) {
            // Create new referral if none exists
            $referral = AffiliateReferral::create([
                'affiliate_id' => $affiliateId,
                'referred_user_id' => $customerId,
                'status' => 'pending',
            ]);
        }

        return $referral;
    }

    /**
     * Process commission for the referral
     */
    protected function processCommission(AffiliateReferral $referral, float $amount): float
    {
        // Get commission rate from affiliate
        $commissionRate = $referral->affiliate->commission_rate;

        // Calculate commission
        $commission = ($amount * $commissionRate) / 100;

        // Update referral
        $referral->update([
            'commission_amount' => $commission,
            'status' => 'completed',
            'converted_at' => now(),
        ]);

        // Update affiliate stats
        $referral->affiliate()->increment('total_earnings', $commission);
        $referral->affiliate()->increment('total_referrals');

        return $commission;
    }
}
