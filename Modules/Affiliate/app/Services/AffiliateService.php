<?php

namespace Modules\Affiliate\Services;

use Modules\Affiliate\Models\Affiliate;
use Modules\Affiliate\Models\AffiliateReferral;

class AffiliateService
{
    public function createAffiliate(int $userId): Affiliate
    {
        return Affiliate::create([
            'user_id' => $userId,
            'code' => Affiliate::generateUniqueCode(),
            'commission_rate' => 5.00, // Default commission rate
        ]);
    }

    public function trackReferral(string $code, int $referredUserId): ?AffiliateReferral
    {
        $affiliate = Affiliate::where('code', $code)->first();

        if (! $affiliate || ! $affiliate->is_active) {
            //            dd('Affiliate not found or inactive');
            return null;
        }

        $affiliate->increment('total_referrals');

        return AffiliateReferral::create([
            'affiliate_id' => $affiliate->id,
            'referred_user_id' => $referredUserId,
            'status' => 'pending',
        ]);

    }

    // after payment -> app(AffiliateService::class)->processCommission($referral, $purchaseAmount);
    public function processCommission(AffiliateReferral $referral, float $amount): void
    {
        $commission = ($amount * $referral->affiliate->commission_rate) / 100;

        $referral->update([
            'commission_amount' => $commission,
            'status' => 'completed',
            'converted_at' => now(),
        ]);

        $referral->affiliate()->increment('total_earnings', $commission);
        $referral->affiliate()->increment('total_referrals');
    }
}
