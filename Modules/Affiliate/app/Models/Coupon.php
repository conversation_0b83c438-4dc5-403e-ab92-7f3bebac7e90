<?php

namespace Modules\Affiliate\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Modules\Billing\Models\Order;

class Coupon extends Model
{
    protected $fillable = [
        'code',
        'type', // percentage or fixed
        'value',
        'min_order_value',
        'max_discount',
        'starts_at',
        'expires_at',
        'max_uses',
        'used_count',
        'is_active',
        'affiliate_id',
    ];

    protected $casts = [
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function isValid(): bool
    {
        return $this->is_active &&
            $this->used_count < $this->max_uses &&
            ! $this->expires_at->isPast();
        //            now()->between($this->starts_at, $this->expires_at);
    }

    public function calculateDiscount(float $orderTotal): float
    {
        if (! $this->isValid() || $orderTotal < $this->min_order_value) {
            return 0;
        }

        $discount = $this->type === 'percentage'
            ? $orderTotal * ($this->value / 100)
            : $this->value;

        return $this->max_discount
            ? min($discount, $this->max_discount)
            : $discount;
    }

    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class);
    }

    public static function generateUniqueCode(): string
    {
        do {
            $code = strtoupper(substr(md5(uniqid()), 0, 8));
        } while (static::where('code', $code)->exists());

        return $code;
    }
}
