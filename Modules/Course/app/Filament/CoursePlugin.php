<?php

namespace Modules\Course\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class CoursePlugin implements Plugin
{
    public function getId(): string
    {
        return 'course';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverResources(
            in: __DIR__.'/Resources',
            for: 'Modules\\Course\\Filament\\Resources'
        );
        $panel->discoverPages(
            in: __DIR__.'/Pages',
            for: 'Modules\\Course\\Filament\\Pages'
        );
        $panel->discoverClusters(
            in: __DIR__.'/Clusters',
            for: 'Modules\\Course\\Filament\\Clusters'
        );
    }

    public function boot(Panel $panel): void {}
}
