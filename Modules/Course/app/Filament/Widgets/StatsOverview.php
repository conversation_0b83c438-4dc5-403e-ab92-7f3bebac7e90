<?php

namespace Modules\Course\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('Unique views', '192.1k')
                ->description('32k increase')
                ->chart([7, 2, 10, 3, 15, 4, 33])
                ->descriptionIcon('heroicon-m-arrow-trending-up')
                ->color('success'),
            Stat::make('Bounce rate', '21%')
                ->description('7% increase')
                ->chart([7, 2, 10, 3, 15, 4, 1])
                ->descriptionIcon('heroicon-m-arrow-trending-down')
                ->color('danger'),
            Stat::make('Processed', '192.1k')
                ->color('success'),
            //                ->extraAttributes([
            //                    'class' => 'cursor-pointer',
            //                    'wire:click' => "\$dispatch('setStatusFilter', { filter: 'processed' })",
            //                ]),

        ];
    }
}
