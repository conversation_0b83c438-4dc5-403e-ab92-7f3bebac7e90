<?php

namespace Modules\Course\Filament\Widgets;

use Filament\Widgets\ChartWidget;
use Flowframe\Trend\Trend;
use Flowframe\Trend\TrendValue;
use Modules\Course\Models\Course;

class VisitCoursesChart extends ChartWidget
{
    protected static ?string $heading = 'Visits Chart';

    protected static string $color = 'info';

    //    protected int | string | array $columnSpan = 2;

    protected function getData(): array
    {
        $data = Trend::model(Course::class)
            ->between(
                start: now()->startOfYear(),
                end: now()->endOfYear(),
            )
            ->perMonth()
            ->count();

        return [
            'datasets' => [
                [
                    'label' => 'Course visits',
                    'data' => $data->map(fn (TrendValue $value) => $value->aggregate),
                ],
            ],
            'labels' => $data->map(fn (TrendValue $value) => $value->date),
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
