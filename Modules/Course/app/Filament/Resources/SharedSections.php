<?php

namespace Modules\Course\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\Str;

class SharedSections
{
    public static function getKeyPoints(): Forms\Components\Section
    {
        return Forms\Components\Section::make()
            ->schema([
                Forms\Components\Repeater::make('learn_key_points')
                    ->simple(
                        Forms\Components\TextInput::make('key_point')
                            ->label(__('Key point'))
                            ->required(),
                    )
                    ->default([])
                    ->label(__('Learn key points')),

            ]);
    }

    /**
     * @return array
     */
    public static function getTitleAndSlug(): Forms\Components\Group
    {
        return Forms\Components\Group::make([
            Forms\Components\TextInput::make('title')
                ->inlineLabel()
                ->label(__('Title'))
                ->placeholder(__('Enter a title'))
                ->live(onBlur: true)
                ->afterStateUpdated(function (Get $get, Set $set, string $operation, ?string $old, ?string $state) {
                    if (($get('slug') ?? '') !== Str::slug($old) || $operation !== 'create') {
                        return;
                    }
                    $set('slug', Str::slug($state).rand(111, 999));
                })
                ->afterStateHydrated(function (Forms\Components\TextInput $component, $state) {
                    if (is_array($state)) {
                        // get first value of array
                        $state = array_shift($state);
                    }
                    $component->state(ucwords($state));
                })
                ->required()
                ->maxLength(255)
                ->autofocus(),

            Forms\Components\TextInput::make('slug')
                ->inlineLabel()
                ->label(__('Slug'))
                ->alphaDash()
                ->required()
                ->unique(ignoreRecord: true),
        ]);
    }
}
