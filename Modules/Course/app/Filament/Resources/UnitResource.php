<?php

namespace Modules\Course\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Modules\Course\Models\Module;
use Modules\Course\Models\Unit;

class UnitResource extends Resource
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static ?string $model = Unit::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $recordTitleAttribute = 'title';

    protected static bool $shouldRegisterNavigation = false;

    public static function getLabel(): ?string
    {
        return __('Unit');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Units');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Main Details')
                    ->description(__('The details of the unit.'))
                    ->collapsible()
                    ->collapsed(fn (string $operation): bool => $operation !== 'create')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->inlineLabel()

                            ->required(),

                        // TODO: Add module relationship
                        Forms\Components\Select::make('module_id')
                            ->inlineLabel()
                            ->default(request('record'))
                            ->relationship('module', 'title'),

                        Forms\Components\Textarea::make('description')
                            ->inlineLabel(),

                        Forms\Components\Toggle::make('visible')
                            ->inlineLabel(),

                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->limit(50),
                Tables\Columns\TextColumn::make('module.title')
                    ->limit(50)
                    ->disabledClick()
                    ->extraAttributes(function (Unit $record) {
                        return [
                            'wire:click' => '$set("tableFilters.module_id.value", '.$record->module_id.')',
                            'class' => 'transition hover:text-primary-500 cursor-pointer',
                        ];
                    })
                    ->sortable(),
                Tables\Columns\IconColumn::make('visible')->boolean(),
            ])
            ->filters([
                SelectFilter::make('module_id')->options(fn (): array => Module::query()->pluck('title', 'id')->all()),

            ])
            ->actions([
                Tables\Actions\Action::make('add_lesson')->icon('heroicon-s-plus')->url(fn ($record) => LessonResource::getUrl('create', ['record' => $record])),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            \Modules\Course\Filament\Resources\UnitResource\RelationManagers\LessonsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\Course\Filament\Resources\UnitResource\Pages\ListUnits::route('/'),
            'create' => \Modules\Course\Filament\Resources\UnitResource\Pages\CreateUnit::route('/create'),
            'edit' => \Modules\Course\Filament\Resources\UnitResource\Pages\EditUnit::route('/{record}/edit'),
        ];
    }
}
