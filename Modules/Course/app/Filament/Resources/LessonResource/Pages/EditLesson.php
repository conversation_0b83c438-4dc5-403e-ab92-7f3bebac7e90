<?php

namespace Modules\Course\Filament\Resources\LessonResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Course\Filament\Resources\LessonResource;

class EditLesson extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = LessonResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            // ...
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }

    public function getBreadcrumbs(): array
    {
        return [
            $this->getRecord()->unit->module->course->getFilamentResource()->getUrl('edit', ['record' => $this->getRecord()->unit->module->course]) => $this->getRecord()->unit->module->course->title,
            $this->getRecord()->unit->module->getFilamentResource()->getUrl('edit', ['record' => $this->getRecord()->unit->module]) => $this->getRecord()->unit->module->title,
            $this->getRecord()->unit->getFilamentResource()->getUrl('edit', ['record' => $this->getRecord()->unit]) => $this->getRecord()->unit->title,
            $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]) => $this->getRecordTitle(),
        ];
    }
}
