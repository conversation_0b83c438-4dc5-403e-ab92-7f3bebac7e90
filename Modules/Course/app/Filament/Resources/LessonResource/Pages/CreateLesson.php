<?php

namespace Modules\Course\Filament\Resources\LessonResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Modules\Course\Filament\Resources\LessonResource;

class CreateLesson extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = LessonResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            // ...
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }
}
