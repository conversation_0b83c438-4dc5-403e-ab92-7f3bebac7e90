<?php

namespace Modules\Course\Filament\Resources\ModuleResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Modules\Course\Filament\Resources\ModuleResource;

class EditModule extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = ModuleResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->previousUrl ?? $this->getResource()::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
            // ...
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            $this->getRecord()->course->getFilamentResource()->getUrl('edit', ['record' => $this->getRecord()->course]) => $this->getRecord()->course->title,
            $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]) => $this->getRecordTitle(),
        ];
    }
}
