<?php

namespace Modules\Course\Filament\Resources;

use Filament\Forms\Form;
use Filament\Resources\Resource;
use Modules\Course\Models\Material;

class MaterialResource extends Resource
{
    protected static ?string $model = Material::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $recordTitleAttribute = 'title';

    public static function getLabel(): ?string
    {
        return __('Material');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Materials');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\Course\Filament\Resources\MaterialResource\Pages\ListMaterials::route(
                '/'
            ),
            'create' => \Modules\Course\Filament\Resources\MaterialResource\Pages\CreateMaterial::route(
                '/create'
            ),
            'edit' => \Modules\Course\Filament\Resources\MaterialResource\Pages\EditMaterial::route(
                '/{record}/edit'
            ),
        ];
    }
}
