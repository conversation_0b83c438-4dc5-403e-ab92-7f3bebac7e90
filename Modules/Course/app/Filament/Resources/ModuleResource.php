<?php

namespace Modules\Course\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Modules\Course\Models\Course;
use Modules\Course\Models\Module;

class ModuleResource extends Resource
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static ?string $model = Module::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $recordTitleAttribute = 'title';

    protected static ?int $navigationSort = 2;

    public static function getLabel(): ?string
    {
        return __('Module');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Modules');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Main Details')
                    ->description(__('The details of the module.'))
                    ->collapsible()
                    ->collapsed(fn (string $operation): bool => $operation !== 'create')
                    ->schema([
                        SharedSections::getTitleAndSlug(),
                        // TODO: Add course relationship
                        Forms\Components\Select::make('course_id')
                            ->inlineLabel()
                            ->default(request('record'))
                            ->relationship('course', 'title'),

                        Forms\Components\Textarea::make('description')

                            ->inlineLabel(),

                        Forms\Components\SpatieMediaLibraryFileUpload::make('cover')
                            ->inlineLabel(),

                        Forms\Components\Toggle::make('visible')
                            ->inlineLabel(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->limit(50),
                Tables\Columns\TextColumn::make('course.title')
                    ->disabledClick()
                    ->extraAttributes(function (Module $record) {
                        return [
                            'wire:click' => '$set("tableFilters.course_id.value", '.$record->course_id.')',
                            'class' => 'transition hover:text-primary-500 cursor-pointer',
                        ];
                    })
                    ->sortable()
                    ->limit(50),
                Tables\Columns\IconColumn::make('visible')->boolean(),
            ])
            ->filters([
                SelectFilter::make('course_id')->options(fn (): array => Course::query()->pluck('title', 'id')->all()),

            ])
            ->actions([
                Tables\Actions\Action::make('add_unit')->icon('heroicon-s-plus')->url(fn ($record) => UnitResource::getUrl('create', ['record' => $record])),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            \Modules\Course\Filament\Resources\ModuleResource\RelationManagers\UnitsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\Course\Filament\Resources\ModuleResource\Pages\ListModules::route('/'),
            'create' => \Modules\Course\Filament\Resources\ModuleResource\Pages\CreateModule::route('/create'),
            'edit' => \Modules\Course\Filament\Resources\ModuleResource\Pages\EditModule::route('/{record}/edit'),
        ];
    }
}
