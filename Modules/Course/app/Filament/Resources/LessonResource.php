<?php

namespace Modules\Course\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Modules\Course\Models\Lesson;
use Modules\Course\Models\Unit;

class LessonResource extends Resource
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static ?string $model = Lesson::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $recordTitleAttribute = 'title';

    public static function getLabel(): ?string
    {
        return __('Lesson');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Lessons');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Main Details'))
                    ->description(__('The details of the lesson.'))
                    ->collapsible()
                    ->collapsed(fn (string $operation): bool => $operation !== 'create')
                    ->schema([

                        SharedSections::getTitleAndSlug(),

                        Forms\Components\Select::make('unit_id')
                            ->inlineLabel()
                            ->default(request('record'))
                            ->relationship('unit', 'title')
                            ->label(__('Unit')),

                        Forms\Components\Textarea::make('description')
                            ->inlineLabel()
                            ->label(__('Description')),

                        Forms\Components\Toggle::make('visible')
                            ->inlineLabel()
                            ->default(true)
                            ->label(__('Visible')),

                        SharedSections::getKeyPoints(),

                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->limit(50)
                    ->label(__('Title')),
                Tables\Columns\TextColumn::make('unit.title')
                    ->disabledClick()
                    ->extraAttributes(function (Lesson $record) {
                        return [
                            'wire:click' => '$set("tableFilters.unit_id.value", '.$record->unit_id.')',
                            'class' => 'transition hover:text-primary-500 cursor-pointer',
                        ];
                    })
                    ->sortable()
                    ->limit(50)
                    ->label(__('Unit')),
                Tables\Columns\IconColumn::make('visible')->boolean()
                    ->label(__('Visible')),
            ])
            ->filters([
                SelectFilter::make('unit_id')->options(fn (): array => Unit::query()->pluck('title', 'id')->all()),
            ])
            ->actions([
                Tables\Actions\Action::make('add_material')->icon('heroicon-s-plus')->url(fn ($record) => MaterialResource::getUrl('create', ['record' => $record])),
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Action::make('addExam')
                        ->form([
                            Forms\Components\TextInput::make('min_score')->numeric(),
                            Forms\Components\Toggle::make('visible')
                                ->label(__('Visible')),
                        ])
                        ->action(function (array $data, Lesson $record): void {
                            $record->quizzes()->create($data);
                        }),
                    Tables\Actions\ViewAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                TextEntry::make('title')->label(''),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            \Modules\Course\Filament\Resources\LessonResource\RelationManagers\MaterialsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\Course\Filament\Resources\LessonResource\Pages\ListLessons::route('/'),
            'create' => \Modules\Course\Filament\Resources\LessonResource\Pages\CreateLesson::route('/create'),
            'edit' => \Modules\Course\Filament\Resources\LessonResource\Pages\EditLesson::route('/{record}/edit'),
            'view' => \Modules\Course\Filament\Resources\LessonResource\Pages\ViewLesson::route('/{record}/view'),
        ];
    }

    public static function canView(Model $record): bool
    {
        return false; // TODO: Change the autogenerated stub
    }
}
