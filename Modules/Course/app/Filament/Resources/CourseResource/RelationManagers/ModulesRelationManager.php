<?php

namespace Modules\Course\Filament\Resources\CourseResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\Concerns\Translatable;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Attributes\Reactive;
use Modules\Course\Filament\Resources\ModuleResource;
use Modules\Course\Filament\Resources\SharedSections;
use Modules\Course\Models\Module;
use Modules\Quiz\Filament\Resources\QuizResource;

class ModulesRelationManager extends RelationManager
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static string $relationship = 'modules';

    protected static bool $isLazy = false;

    #[Reactive]
    public ?string $activeLocale = null;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Fieldset::make('Main Details')
                    ->columns(1)
                    ->schema([
                        SharedSections::getTitleAndSlug(),

                        // TODO: Add course relationship
                        Forms\Components\Select::make('course_id')
                            ->inlineLabel()
                            ->default($this->ownerRecord->id)
                            ->hidden()
                            ->relationship('course', 'title'),

                        Forms\Components\Textarea::make('description')
                            ->inlineLabel(),

                        Forms\Components\SpatieMediaLibraryFileUpload::make('cover')
                            ->inlineLabel(),

                        Forms\Components\Toggle::make('visible')
                            ->inlineLabel(),
                    ]),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->limit(50),
                Tables\Columns\ToggleColumn::make('visible')
                    ->alignEnd(),
                Tables\Columns\TextInputColumn::make('sort')->width('80px'),

            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('edit')->icon('heroicon-m-pencil-square')->url(fn ($record) => ModuleResource::getUrl('edit', ['record' => $record])),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('addExam')
                    ->icon('heroicon-o-academic-cap')
                    ->form([
                        Forms\Components\TextInput::make('min_score')->numeric(),
                        Forms\Components\Toggle::make('visible'),
                    ])
                    ->action(function (array $data, Module $record): void {
                        $quiz = $record->quizzes()->create($data);
                        redirect()->to(QuizResource::getUrl('edit', ['record' => $quiz]));
                    }),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->reorderable('sort')->defaultSort('sort');
    }
}
