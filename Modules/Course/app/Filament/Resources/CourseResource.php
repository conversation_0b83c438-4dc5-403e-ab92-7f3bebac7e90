<?php

namespace Modules\Course\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Modules\Course\Enum\CourseLevels;
use Modules\Course\Models\Course;
use Modules\MediaLibrary\Models\Pdf;
use Modules\Quiz\Filament\Resources\QuizResource;

class CourseResource extends Resource
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static ?string $model = Course::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?int $navigationSort = -1;

    protected static ?string $recordTitleAttribute = 'title';

    public static function getLabel(): ?string
    {
        return __('Course');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Courses');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Main Details')
                    ->description(__('The details of the course.'))
                    ->collapsible(fn (string $operation): bool => $operation !== 'create')
                    ->collapsed(fn (string $operation): bool => $operation !== 'create')
                    ->schema([

                        Forms\Components\Section::make([
                            Forms\Components\Group::make()
                                ->columns(1)
                                ->schema([

                                    Forms\Components\Fieldset::make()
                                        ->columns(1)
                                        ->schema([
                                            SharedSections::getTitleAndSlug(),

                                            Forms\Components\TextInput::make('youtube_intro_video_id')
                                                ->inlineLabel()
                                                ->required(),
                                            Forms\Components\MarkdownEditor::make('description'),
                                        ]),

                                ])->columnSpan(9),

                            Forms\Components\Group::make()
                                ->schema([
                                    Forms\Components\SpatieMediaLibraryFileUpload::make('cover')
                                        ->image()
                                        ->imageEditor()
                                        ->columnSpanFull()
                                        ->hiddenLabel(),

                                    Forms\Components\Fieldset::make()
                                        ->columns(1)
                                        ->schema([
                                            Forms\Components\TextInput::make('price')
                                                ->live(onBlur: true)
                                                ->afterStateUpdated(fn ($state, callable $set) => $set('public', false))
                                                ->numeric()
                                                ->hintIcon('heroicon-s-question-mark-circle')
                                                ->hintIconTooltip('Free if zero')
                                                ->default(0)
                                                ->rules('regex:/^\d{1,6}(\.\d{0,2})?$/')
                                                ->prefix(generalSetting()->currency)
                                                ->required(),

                                            Forms\Components\Select::make('level')
                                                ->options(CourseLevels::class)
                                                ->required(),

                                            Forms\Components\Toggle::make('public')
                                                ->reactive()
                                                ->disabled(fn (callable $get) => $get('price') != 0)
                                                ->hintIcon('heroicon-s-question-mark-circle')
                                                ->hintIconTooltip(__('Can anyone view this course even if not logged in')),

                                            Forms\Components\Toggle::make('visible'),
                                            Forms\Components\Toggle::make('is_featured'),
                                        ]),

                                ])->columnSpan(3),

                        ])->columns(12),

                    ]),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->limit(50),
                Tables\Columns\TextColumn::make('price'),
                Tables\Columns\TextColumn::make('level'),
                Tables\Columns\IconColumn::make('visible')->boolean(),
                Tables\Columns\TextInputColumn::make('sort')->width('80px'),

            ])
            ->filters([
                //
            ])
            ->actions([
                //                Tables\Actions\Action::make('add_module')->icon('heroicon-s-plus')->url(fn ($record) => ModuleResource::getUrl('create', ['record' => $record])),
                Tables\Actions\EditAction::make(),
                Action::make('addExam')
                    ->icon('heroicon-o-academic-cap')
                    ->form([
                        Forms\Components\TextInput::make('min_score')->numeric(),
                        Forms\Components\Toggle::make('visible'),
                    ])
                    ->action(function (array $data, Course $record): void {
                        $quiz = $record->quizzes()->create($data);
                        redirect()->to(QuizResource::getUrl('edit', ['record' => $quiz]));
                    }),
                Action::make('replace-book')->icon('heroicon-o-arrow-uturn-up')->form(fn (Course $record) => [
                    Forms\Components\Select::make('old_pdf')->options(Pdf::all()->pluck('name', 'id')->toArray()),
                    Forms\Components\Select::make('new_pdf')->options(Pdf::all()->pluck('name', 'id')->toArray()),
                ])->action(function (Course $record, array $data) {
                    $material = Course::where('id', $record->id)->with('modules.units.lessons.materials')
                        ->first()
                        ->modules
                        ->flatMap(function ($module) {
                            return $module->units->flatMap(function ($unit) {
                                return $unit->lessons->flatMap(function ($lesson) {
                                    return $lesson->materials;
                                });
                            });
                        });
                    $material->where('pdf_id', $data['old_pdf'])->map(function ($pdf) use ($data) {
                        $pdf->update(['pdf_id' => $data['new_pdf']]);
                    });
                }),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->reorderable('sort')->defaultSort('sort');
    }

    public static function getRelations(): array
    {
        return [
            \Modules\Course\Filament\Resources\CourseResource\RelationManagers\ModulesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\Course\Filament\Resources\CourseResource\Pages\ListCourses::route('/'),
            //            'create' => \Modules\Course\Filament\Resources\CourseResource\Pages\CreateCourse::route('/create'),
            'edit' => \Modules\Course\Filament\Resources\CourseResource\Pages\EditCourse::route('/{record}/edit'),
        ];
    }
}
