<?php

namespace Modules\Course\Filament\Resources\UnitResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\Concerns\Translatable;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Livewire\Attributes\Reactive;
use Modules\Course\Filament\Resources\LessonResource;
use Modules\Course\Filament\Resources\SharedSections;
use Modules\Course\Models\Lesson;
use Modules\Quiz\Filament\Resources\QuizResource;

class LessonsRelationManager extends RelationManager
{
    use Translatable;

    public static function getDefaultTranslatableLocale(): string
    {
        return app()->getLocale();
    }

    protected static string $relationship = 'lessons';

    protected static bool $isLazy = false;

    #[Reactive]
    public ?string $activeLocale = null;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Main Details')
                    ->description(__('The details of the lesson.'))
                    ->aside()
                    ->schema([

                        SharedSections::getTitleAndSlug(),

                        Forms\Components\Select::make('unit_id')
                            ->inlineLabel()
                            ->default($this->ownerRecord->id)
                            ->hidden()
                            ->relationship('unit', 'title')
                            ->label(__('Unit')),

                        Forms\Components\Textarea::make('description')
                            ->label(__('Description'))
                            ->inlineLabel(),

                        Forms\Components\Toggle::make('visible')
                            ->inlineLabel()
                            ->default(true)
                            ->label(__('Visible')),

                        SharedSections::getKeyPoints(),

                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->limit(50)
                    ->label(__('Title')),
                Tables\Columns\ToggleColumn::make('visible')
                    ->label(__('Visible')),
                Tables\Columns\TextInputColumn::make('sort')->width('80px')->label(__('Sort')),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('edit')->icon('heroicon-m-pencil-square')->url(fn ($record) => LessonResource::getUrl('edit', ['record' => $record])),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('addExam')
                    ->icon('heroicon-o-academic-cap')
                    ->form([
                        Forms\Components\TextInput::make('min_score')->numeric(),
                        Forms\Components\Toggle::make('visible')
                            ->label(__('Visible')),
                    ])
                    ->action(function (array $data, Lesson $record): void {
                        $quiz = $record->quizzes()->create($data);
                        redirect()->to(QuizResource::getUrl('edit', ['record' => $quiz]));
                    }),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->reorderable('sort')->defaultSort('sort');
    }
}
