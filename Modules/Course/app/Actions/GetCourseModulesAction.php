<?php

namespace Modules\Course\Actions;

use App\Helpers\APIResponse;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\Course\Data\ModuleData;
use Modules\Course\Models\Course;
use Modules\Course\Models\Module;

class GetCourseModulesAction
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return ['auth:sanctum'];
    }

    public function asController(Request $request, $course)
    {
        $course = Course::find($course);

        if (! $course) {
            return APIResponse::error('Course not found', 404);
        }
        $modules = $course->modules;

        if ($request->expectsJson()) {
            $data = $modules->map(function (Module $module) {
                return new ModuleData(
                    $module->id,
                    $module->title,
                    $module->description,
                );
            });

            return APIResponse::success(data: $data);

            //            return response()->json($data, 200, [], JSON_PRETTY_PRINT);
        }

        return $modules;
    }
}
