<?php

namespace Modules\Course\Actions;

use App\Helpers\APIResponse;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\Course\Data\CourseData;
use Modules\Course\Models\Course;

class GetCourseDetails
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return ['auth:sanctum'];
    }

    public function asController(Request $request, $course)
    {
        $course = Course::find($course);
        if (! $course) {
            return APIResponse::error('Course not found', 404);
        }

        if ($request->expectsJson()) {
            return APIResponse::success(data: new CourseData(
                $course->id,
                $course->title,
                $course->slug,
                $course->level,
                $course->description,
            ));
        }

        return $course;
    }
}
