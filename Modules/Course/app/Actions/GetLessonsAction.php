<?php

namespace Modules\Course\Actions;

use App\Helpers\APIResponse;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\Course\Data\LessonData;
use Modules\Course\Models\Lesson;
use Modules\Course\Models\Unit;

class GetLessonsAction
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return ['auth:sanctum'];
    }

    public function asController(Request $request, $unit)
    {
        $unit = Unit::find($unit);

        if (! $unit) {
            return APIResponse::error('Unit not found', 404);
        }
        $lessons = $unit->lessons;

        if ($request->expectsJson()) {
            $data = $lessons->map(function (Lesson $lesson) {
                return new LessonData(
                    $lesson->id,
                    $lesson->title,
                    $lesson->learn_key_points,
                    $lesson->description,
                );
            });

            return APIResponse::success(data: $data);
        }

        return $lessons;
    }
}
