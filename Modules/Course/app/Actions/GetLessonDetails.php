<?php

namespace Modules\Course\Actions;

use App\Helpers\APIResponse;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\Course\Data\LessonData;
use Modules\Course\Models\Lesson;

class GetLessonDetails
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return ['auth:sanctum'];
    }

    public function asController(Request $request, $lesson)
    {
        $lesson = Lesson::find($lesson);
        if (! $lesson) {
            return APIResponse::error('Lesson not found', 404);
        }

        if ($request->expectsJson()) {
            return APIResponse::success(data: new LessonData(
                $lesson->id,
                $lesson->title,
                $lesson->learn_key_points,
                $lesson->description,
            ));
        }

        return $lesson;
    }
}
