<?php

namespace Modules\Course\Actions;

use App\Helpers\APIResponse;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\Course\Data\UnitData;
use Modules\Course\Models\Module;
use Modules\Course\Models\Unit;

class GetUnitsAction
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return ['auth:sanctum'];
    }

    public function asController(Request $request, $module)
    {
        $module = Module::find($module);

        if (! $module) {
            return APIResponse::error('Module not found', 404);
        }
        $units = $module->units;

        if ($request->expectsJson()) {
            $data = $units->map(function (Unit $unit) {
                return new UnitData(
                    $unit->id,
                    $unit->title,
                    $unit->description,
                );
            });

            return APIResponse::success(data: $data);
        }

        return $units;
    }
}
