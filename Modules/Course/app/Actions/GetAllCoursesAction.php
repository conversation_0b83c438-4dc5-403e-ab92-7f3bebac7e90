<?php

namespace Modules\Course\Actions;

use App\Helpers\APIResponse;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\Course\Data\CourseData;
use Modules\Course\Models\Course;

class GetAllCoursesAction
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return ['auth:sanctum'];
    }

    public function asController(Request $request)
    {
        $courses = Course::all();

        if ($request->expectsJson()) {
            $data = $courses->map(function (Course $course) {
                return new CourseData(
                    $course->id,
                    $course->title,
                    $course->slug,
                    $course->level,
                    $course->description,
                );
            });

            return APIResponse::success(data: $data);
        }

        return $courses;
    }
}
