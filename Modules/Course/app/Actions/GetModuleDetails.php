<?php

namespace Modules\Course\Actions;

use App\Helpers\APIResponse;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\Course\Data\ModuleData;
use Modules\Course\Models\Module;

class GetModuleDetails
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return ['auth:sanctum'];
    }

    public function asController(Request $request, $module)
    {
        $module = Module::find($module);
        if (! $module) {
            return APIResponse::error('Module not found', 404);
        }

        if ($request->expectsJson()) {
            return APIResponse::success(data: new ModuleData(
                $module->id,
                $module->title,
                $module->description,
            ));
        }

        return $module;
    }
}
