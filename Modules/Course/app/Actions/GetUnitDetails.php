<?php

namespace Modules\Course\Actions;

use App\Helpers\APIResponse;
use Illuminate\Http\Request;
use Lorisleiva\Actions\Concerns\AsAction;
use Modules\Course\Data\UnitData;
use Modules\Course\Models\Unit;

class GetUnitDetails
{
    use AsAction;

    public function getControllerMiddleware(): array
    {
        return ['auth:sanctum'];
    }

    public function asController(Request $request, $unit)
    {
        $unit = Unit::find($unit);
        if (! $unit) {
            return APIResponse::error('Unit not found', 404);
        }

        if ($request->expectsJson()) {
            return APIResponse::success(data: new UnitData(
                $unit->id,
                $unit->title,
                $unit->description,
            ));
        }

        return $unit;
    }
}
