<?php

namespace Modules\Course\Enum;

use Filament\Support\Contracts\HasLabel;

enum CourseLevels: int implements HasLabel
{
    case BEGINNER = 1;
    case INTERMEDIATE = 2;
    case ADVANCED = 3;

    public function getLabel(): string
    {
        return match ($this) {
            self::BEGINNER => __('Beginner'),
            self::INTERMEDIATE => __('Intermediate'),
            self::ADVANCED => __('Advanced'),
        };
    }
}
