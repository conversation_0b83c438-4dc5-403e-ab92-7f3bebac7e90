<?php

namespace Modules\Course\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Modules\Billing\Concerns\Billable;
use Modules\Core\Models\Scopes\ActiveScope;
use Modules\Course\Enum\CourseLevels;
use Modules\Course\Filament\Resources\CourseResource;
use Modules\Course\Notifications\PaymentCompletedNotification;
use Modules\Institute\Models\Institute;
use Modules\Payment\Models\Payment;
// use Modules\Payment\Traits\HasPayments;
use Modules\Quiz\Concerns\HasQuiz;
use Spatie\Image\Enums\Fit;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\Translatable\HasTranslations;

#[ScopedBy([ActiveScope::class])]
class Course extends Model implements HasMedia
{
    use Billable, /* HasPayments, */ HasQuiz, HasTranslations, InteractsWithMedia;

    protected string $filament_resource = CourseResource::class;

    public function getFilamentResource(): mixed
    {
        return new $this->filament_resource;
    }

    protected $fillable = [
        'title',
        'slug',
        'description',
        'is_featured',
        'youtube_intro_video_id',
        'price',
        'category_id',
        'level',
        'public',
        'visible',
    ];

    public array $translatable = ['title', 'description'];

    //    public function getRouteKeyName(): string
    //    {
    //        return "slug";
    //    }

    protected $casts = [
        'is_featured' => 'boolean',
        'level' => CourseLevels::class,
        'visible' => 'boolean',
    ];

    public function modules(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(Module::class);
    }

    public function units(): hasManyThrough
    {
        return $this->hasManyThrough(Unit::class, Module::class);
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this
            ->addMediaConversion('cover')
            ->width(500)
            ->height(400)
//            ->fit(Fit::Contain, 500, 400)
            ->nonQueued();
    }

    public function manipulate_Price()
    {
        // todo: show price in default currency using MoneyEnum
        return ($this->price != 0) ? '$'.$this->price : __('Free');
    }

    public function handlePaymentCompleted(Payment $payment): void
    {
        // Send an email to the student that their payment was received
        $user = auth()->user();
        Notification::send($user, new PaymentCompletedNotification($payment, $this));

        // Update course enrollment status

        // Log the successful payment
        Log::info("Payment completed for course {$this->title} by user {$payment->customer_email}");
    }

    public function handlePaymentFailed(Payment $payment, string $reason): void
    {
        // Log the failure
        Log::warning("Payment failed for course {$this->title}: {$reason}");

        // Notify the student
        $user = $payment->user;

        // Optionally schedule a follow-up email
        //        FollowUpEmail::dispatch($user, $this)
        //            ->delay(now()->addHours(24));
    }

    public function getMediaUrlWithFallback($collection = 'default', $conversion = '')
    {
        return $this->getFirstMediaUrl($collection, $conversion) ?: asset('logo.jpg');
    }

    // check if purchased by institute
    public function isPurchasedByInstitute(User $user): bool
    {
        // Retrieve the user's institute and its owner
        $institute = $user->institute()->first();
        $owner = $institute?->user;

        if (! $owner) {
            return false;
        }

        // Check if the course has been purchased by the institute owner
        return $this->orders()
            ->where('user_id', $owner->id)
            ->whereIn('status', ['completed', 'processing'])
            ->exists();
    }
}
