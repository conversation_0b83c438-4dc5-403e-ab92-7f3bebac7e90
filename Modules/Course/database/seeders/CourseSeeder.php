<?php

namespace Modules\Course\Database\Seeders;

use Illuminate\Database\Seeder;
use JetBrains\PhpStorm\NoReturn;
use Modules\Course\Models\Course;

class CourseSeeder extends Seeder
{
    #[NoReturn]
    public function run(): void
    {
        $data = importCsv(__DIR__.'/courses.csv');

        for ($i = 0; $i < count($data); $i++) {
            Course::firstOrCreate($data[$i]);
        }

    }
}
