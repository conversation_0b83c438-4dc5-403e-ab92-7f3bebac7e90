<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->id();
            $table->text('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->tinyInteger('is_featured')->default(1);
            $table->string('youtube_intro_video_id')->nullable();
            $table->decimal('price')->default(0.00); // free or paid
            $table->integer('sort')->default(0);
            //            $table->integer('category_id')->nullable();
            $table->integer('level')->nullable();
            $table->integer('public')->default(0);
            $table->boolean('visible')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('courses');
    }
};
