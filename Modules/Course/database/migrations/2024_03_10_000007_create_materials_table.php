<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('materials', function (Blueprint $table) {
            $table->id();
            $table->boolean('main')->default(1); // true = main, false = sub
            $table->text('title', 250);
            $table->string('file')->nullable();
            $table
                ->string('type', 100)
                ->comment(
                    'video, youtube, vimeo, text, image, pdf, embed, audio'
                );
            $table->longText('text')->nullable();
            $table->string('external_link', 250)->nullable();
            $table->longText('embed_code', 250)->nullable();
            $table->string('youtube_id', 250)->nullable();
            $table->string('file_size')->nullable();
            $table->string('file_duration')->nullable();
            $table->double('file_duration_second')->nullable();
            $table->text('pdf_pages')->nullable();
            $table->text('prevent_access')->nullable();
            $table->boolean('visible')->default(true);
            $table
                ->foreignIdFor(\Modules\Course\Models\Lesson::class)
                ->constrained()
                ->cascadeOnDelete();
            $table
                ->foreignIdFor(\Modules\MediaLibrary\Models\Pdf::class)
                ->nullable()
                ->constrained()
                ->nullOnDelete();
            $table
                ->foreignIdFor(\Modules\MediaLibrary\Models\Audio::class)
                ->nullable()
                ->constrained()
                ->nullOnDelete();
            $table
                ->foreignIdFor(\Modules\MediaLibrary\Models\Video::class)
                ->nullable()
                ->constrained()
                ->nullOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('materials');
    }
};
