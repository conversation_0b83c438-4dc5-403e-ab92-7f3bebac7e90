<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {

        $tables = [
            'lessons',
            'materials',
            'modules',
            'courses',
            'units',
        ];

        foreach ($tables as $table) {
            if (! Schema::hasColumn($table, 'sort')) {
                Schema::table($table, function (Blueprint $table) {
                    $table->integer('sort')->default(0);
                });
            }
        }
    }

    public function down(): void
    {

        $tables = [
            'lessons',
            'materials',
            'modules',
            'courses',
            'units',
        ];
        foreach ($tables as $table) {
            if (Schema::hasColumn($table, 'sort')) {
                Schema::table($table, function (Blueprint $table) {
                    $table->dropColumn('sort');
                });
            }
        }

    }
};
