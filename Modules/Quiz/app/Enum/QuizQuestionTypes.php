<?php

namespace Modules\Quiz\Enum;

use Filament\Support\Contracts\HasLabel;

enum QuizQuestionTypes: int implements HasLabel
{
    case SINGLECHOICE = 1;

    case MULTICHOICE = 2;

    case TRUEFALSE = 3;

    case TEXT = 4;

    public function getLabel(): string
    {
        return match ($this) {
            self::SINGLECHOICE => __('Single Choice'),
            self::MULTICHOICE => __('Multiple Choice'),
            self::TRUEFALSE => __('True or False'),
            self::TEXT => __('Text Question'),
        };
    }
}
