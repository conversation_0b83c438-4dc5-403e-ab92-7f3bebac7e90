<?php

namespace Modules\Quiz\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class QuizUserAnswer extends Model
{
    protected $fillable = [
        'user_id',
        'quiz_answer_id',
        'quiz_question_id',
        'quiz_id',
        'text_answer',
        'attachment',
    ];

    /**
     * Get the quiz that this answer belongs to.
     */
    public function quiz(): BelongsTo
    {
        return $this->belongsTo(Quiz::class);
    }

    /**
     * Get the user that submitted this answer.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the question that this answer is for.
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(QuizQuestion::class, 'quiz_question_id');
    }

    /**
     * Get the selected answer.
     */
    public function answer(): BelongsTo
    {
        return $this->belongsTo(QuizAnswer::class, 'quiz_answer_id');
    }

    /**
     * Check if the user's answer is correct.
     */
    public function isCorrect(): bool
    {
        // For text answers, we can't automatically determine correctness
        if ($this->question->type === \Modules\Quiz\Enum\QuizQuestionTypes::TEXT) {
            return false; // Text answers need manual grading
        }

        // For single choice or true/false questions
        if (in_array($this->question->type, [\Modules\Quiz\Enum\QuizQuestionTypes::SINGLECHOICE, \Modules\Quiz\Enum\QuizQuestionTypes::TRUEFALSE])) {
            return $this->answer && $this->answer->correct;
        }

        // For multiple choice questions
        if ($this->question->type === \Modules\Quiz\Enum\QuizQuestionTypes::MULTICHOICE) {
            // This will need to be handled in the controller since we need to check all selected answers
            // Here we just check if this specific answer is correct
            return $this->answer && $this->answer->correct;
        }

        return false;
    }
}
