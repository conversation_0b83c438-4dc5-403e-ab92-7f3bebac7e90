<?php

namespace Modules\Quiz\Models;

use Illuminate\Database\Eloquent\Model;
use Modules\Quiz\Enum\QuizQuestionTypes;

class QuizQuestion extends Model
{
    protected $fillable = [
        'question',
        'type',
        'quiz_id',
        'marks_per_question',
        'duration',
    ];

    public function quiz(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Quiz::class);
    }

    public function quiz_answers(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(QuizAnswer::class);
    }

    protected $casts = [
        'id' => 'integer',
        'question' => 'string',
        'type' => QuizQuestionTypes::class,
    ];
}
