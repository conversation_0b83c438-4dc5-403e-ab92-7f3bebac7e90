<?php

namespace Modules\Quiz\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class QuizScore extends Model
{
    protected $fillable = [
        'attempts',
        'score',
        'user_id',
        'quiz_id',
    ];

    /**
     * Get the quiz that this score belongs to.
     */
    public function quiz(): BelongsTo
    {
        return $this->belongsTo(Quiz::class);
    }

    /**
     * Get the user that this score belongs to.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the user passed the quiz based on the minimum required score.
     */
    public function isPassing(): bool
    {
        return $this->score >= $this->quiz->min_score;
    }

    /**
     * Get the percentage score.
     */
    public function getPercentage(): float
    {
        $totalPossibleScore = $this->quiz->quiz_questions->sum('marks_per_question');

        if ($totalPossibleScore === 0) {
            return 0;
        }

        return round(($this->score / $totalPossibleScore) * 100, 2);
    }
}
