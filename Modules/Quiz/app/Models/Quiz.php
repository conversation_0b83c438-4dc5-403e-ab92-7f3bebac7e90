<?php

namespace Modules\Quiz\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Modules\Quiz\Enum\QuizQuestionTypes;

class Quiz extends Model
{
    protected $fillable = [
        'quizzable_id',
        'quizzable_type',
        'min_score',
        'visible',
    ];

    protected $casts = [
        'visible' => 'boolean',
        'type' => QuizQuestionTypes::class,
    ];

    /**
     * Get all questions for this quiz.
     */
    public function quiz_questions(): HasMany
    {
        return $this->hasMany(QuizQuestion::class);
    }

    /**
     * Get the polymorphic model that this quiz belongs to.
     */
    public function quizzable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get all scores for this quiz.
     */
    public function scores(): HasMany
    {
        return $this->hasMany(QuizScore::class);
    }

    /**
     * Get all user answers for this quiz.
     */
    public function userAnswers(): Has<PERSON><PERSON>
    {
        return $this->hasMany(QuizUserAnswer::class);
    }

    /**
     * Get the latest quiz score for a specific user.
     */
    public function getLatestScoreFor(User $user): ?QuizScore
    {
        return $this->scores()
            ->where('user_id', $user->id)
            ->latest()
            ->first();
    }

    /**
     * Check if a user has passed this quiz.
     */
    public function isPassedBy(User $user): bool
    {
        $latestScore = $this->getLatestScoreFor($user);

        return $latestScore ? $latestScore->isPassing() : false;
    }

    /**
     * Get the user's answers for this quiz.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAnswersFor(User $user)
    {
        return $this->userAnswers()
            ->where('user_id', $user->id)
            ->latest()
            ->get();
    }

    /**
     * Get the total possible score for this quiz.
     */
    public function getTotalPossibleScore(): int
    {
        return $this->quiz_questions->sum('marks_per_question');
    }
}
