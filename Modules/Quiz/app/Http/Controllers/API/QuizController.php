<?php

namespace Modules\Quiz\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Quiz\Models\Quiz;
use Modules\Quiz\Models\QuizAnswer;
use Modules\Quiz\Models\QuizScore;
use Modules\Quiz\Models\QuizUserAnswer;

class QuizController extends Controller
{
    /**
     * Get a list of all visible quizzes.
     */
    public function index(): JsonResponse
    {
        $quizzes = Quiz::where('visible', true)
            ->with(['quizzable:id,title,slug'])
            ->withCount('quiz_questions')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $quizzes,
        ]);
    }

    /**
     * Get a specific quiz with its questions.
     */
    public function show(int $id): JsonResponse
    {
        $quiz = Quiz::with([
            'quiz_questions',
            'quiz_questions.quiz_answers:id,quiz_question_id,answer,correct',
            'quizzable:id,title,slug',
        ])->findOrFail($id);

        // Check if quiz is visible
        if (! $quiz->visible && ! Auth::user()->can('view hidden quizzes')) {
            return response()->json([
                'success' => false,
                'message' => 'This quiz is not available',
            ], 403);
        }

        // Get user's previous attempts
        $previousScore = null;
        if (Auth::check()) {
            $previousScore = $quiz->getLatestScoreFor(Auth::user());
        }

        return response()->json([
            'success' => true,
            'data' => [
                'quiz' => $quiz,
                'previous_score' => $previousScore,
            ],
        ]);
    }

    /**
     * Submit a quiz response.
     */
    public function submit(Request $request, int $id): JsonResponse
    {
        $quiz = Quiz::with(['quiz_questions.quiz_answers'])->findOrFail($id);
        $user = Auth::user();

        // Validate submission
        $request->validate([
            'answers' => 'required|array',
            'answers.*' => 'required',
        ]);

        $totalScore = 0;
        $answers = $request->input('answers');
        $userAnswersCollection = [];

        // Process each question
        foreach ($quiz->quiz_questions as $question) {
            if (isset($answers[$question->id])) {
                $userAnswer = $answers[$question->id];

                // Create user answer record
                $quizUserAnswer = new QuizUserAnswer;
                $quizUserAnswer->user_id = $user->id;
                $quizUserAnswer->quiz_id = $quiz->id;
                $quizUserAnswer->quiz_question_id = $question->id;

                // Process based on question type
                switch ($question->type->value) {
                    case \Modules\Quiz\Enum\QuizQuestionTypes::TEXT->value:
                        $quizUserAnswer->text_answer = $userAnswer;
                        break;

                    case \Modules\Quiz\Enum\QuizQuestionTypes::SINGLECHOICE->value:
                        $quizUserAnswer->quiz_answer_id = $userAnswer;

                        // Check if answer is correct
                        $correctAnswer = QuizAnswer::where('id', $userAnswer)
                            ->where('correct', true)
                            ->first();

                        if ($correctAnswer) {
                            $totalScore += $question->marks_per_question;
                        }
                        break;

                    case \Modules\Quiz\Enum\QuizQuestionTypes::MULTICHOICE->value:
                        // For multiple choice, we receive an array of answers
                        if (is_array($userAnswer)) {
                            // Process each selected answer
                            $allCorrect = true;
                            $selectedAnswers = QuizAnswer::whereIn('id', $userAnswer)->get();

                            // Check if all selected answers are correct
                            foreach ($selectedAnswers as $answer) {
                                if (! $answer->correct) {
                                    $allCorrect = false;
                                    break;
                                }
                            }

                            // Check if all correct answers were selected
                            $correctAnswersCount = QuizAnswer::where('quiz_question_id', $question->id)
                                ->where('correct', true)
                                ->count();

                            if ($allCorrect && count($userAnswer) === $correctAnswersCount) {
                                $totalScore += $question->marks_per_question;
                            }

                            // Store the first answer ID for simplicity
                            if (count($userAnswer) > 0) {
                                $quizUserAnswer->quiz_answer_id = $userAnswer[0];
                            }
                        }
                        break;

                    case \Modules\Quiz\Enum\QuizQuestionTypes::TRUEFALSE->value:
                        $quizUserAnswer->quiz_answer_id = $userAnswer;

                        // Check if answer is correct
                        $correctAnswer = QuizAnswer::where('id', $userAnswer)
                            ->where('correct', true)
                            ->first();

                        if ($correctAnswer) {
                            $totalScore += $question->marks_per_question;
                        }
                        break;
                }

                // Save user answer
                $quizUserAnswer->save();
                $userAnswersCollection[] = $quizUserAnswer;
            }
        }

        // Record score
        $attempts = QuizScore::where('user_id', $user->id)
            ->where('quiz_id', $quiz->id)
            ->count() + 1;

        $quizScore = new QuizScore;
        $quizScore->user_id = $user->id;
        $quizScore->quiz_id = $quiz->id;
        $quizScore->score = $totalScore;
        $quizScore->attempts = $attempts;
        $quizScore->save();

        return response()->json([
            'success' => true,
            'data' => [
                'score' => $quizScore,
                'total_possible' => $quiz->getTotalPossibleScore(),
                'percentage' => $quizScore->getPercentage(),
                'passed' => $quizScore->isPassing(),
                'answers' => $userAnswersCollection,
            ],
            'message' => 'Quiz submitted successfully',
        ]);
    }

    /**
     * Get quiz results.
     */
    public function results(int $id, int $attemptId): JsonResponse
    {
        $quiz = Quiz::with(['quiz_questions.quiz_answers'])->findOrFail($id);
        $score = QuizScore::findOrFail($attemptId);

        // Make sure the score belongs to the current user
        if ($score->user_id != Auth::id() && ! Auth::user()->can('view all quiz results')) {
            return response()->json([
                'success' => false,
                'message' => 'You are not authorized to view these results',
            ], 403);
        }

        // Get user answers for this quiz
        $userAnswers = QuizUserAnswer::where('user_id', $score->user_id)
            ->where('quiz_id', $quiz->id)
            ->get()
            ->keyBy('quiz_question_id');

        return response()->json([
            'success' => true,
            'data' => [
                'quiz' => $quiz,
                'score' => $score,
                'total_possible' => $quiz->getTotalPossibleScore(),
                'percentage' => $score->getPercentage(),
                'passed' => $score->isPassing(),
                'answers' => $userAnswers,
            ],
        ]);
    }

    /**
     * Get user's quiz history.
     */
    public function history(): JsonResponse
    {
        $user = Auth::user();

        $scores = QuizScore::with(['quiz.quizzable:id,title,slug'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($score) {
                return [
                    'id' => $score->id,
                    'quiz_id' => $score->quiz_id,
                    'quiz_title' => $score->quiz->quizzable->title ?? 'Unknown',
                    'score' => $score->score,
                    'total_possible' => $score->quiz->getTotalPossibleScore(),
                    'percentage' => $score->getPercentage(),
                    'passed' => $score->isPassing(),
                    'attempt' => $score->attempts,
                    'date' => $score->created_at->format('Y-m-d H:i:s'),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $scores,
        ]);
    }
}
