<?php

namespace Modules\Quiz\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Quiz\Models\Quiz;
use Modules\Quiz\Models\QuizAnswer;
use Modules\Quiz\Models\QuizScore;
use Modules\Quiz\Models\QuizUserAnswer;

class QuizController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();
        $quizzes = Quiz::where('visible', true)->get();

        return view('quiz::index', compact('quizzes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('quiz::create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        //
    }

    /**
     * Show the specified quiz.
     */
    public function show($id)
    {
        $quiz = Quiz::with(['quiz_questions.quiz_answers'])->findOrFail($id);

        // Check if quiz is visible
        if (! $quiz->visible && ! Auth::user()->can('view hidden quizzes')) {
            abort(403, 'This quiz is not available');
        }

        // Get user's previous attempts
        $previousScore = null;
        if (Auth::check()) {
            $previousScore = $quiz->getLatestScoreFor(Auth::user());
        }

        return view('quiz::show', compact('quiz', 'previousScore'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        return view('quiz::edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id): RedirectResponse
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        //
    }

    /**
     * Process a quiz submission.
     */
    public function submit(Request $request, $id)
    {
        $quiz = Quiz::with(['quiz_questions.quiz_answers'])->findOrFail($id);
        $user = Auth::user();

        // Validate submission
        $request->validate([
            'answers' => 'required|array',
            'answers.*' => 'required',
        ]);

        $totalScore = 0;
        $answers = $request->input('answers');

        // Process each question
        foreach ($quiz->quiz_questions as $question) {
            if (isset($answers[$question->id])) {
                $userAnswer = $answers[$question->id];

                // Create user answer record
                $quizUserAnswer = new QuizUserAnswer;
                $quizUserAnswer->user_id = $user->id;
                $quizUserAnswer->quiz_id = $quiz->id;
                $quizUserAnswer->quiz_question_id = $question->id;

                // Process based on question type
                switch ($question->type->value) {
                    case \Modules\Quiz\Enum\QuizQuestionTypes::TEXT->value:
                        $quizUserAnswer->text_answer = $userAnswer;
                        break;

                    case \Modules\Quiz\Enum\QuizQuestionTypes::SINGLECHOICE->value:
                        $quizUserAnswer->quiz_answer_id = $userAnswer;

                        // Check if answer is correct
                        $correctAnswer = QuizAnswer::where('id', $userAnswer)
                            ->where('correct', true)
                            ->first();

                        if ($correctAnswer) {
                            $totalScore += $question->marks_per_question;
                        }
                        break;

                    case \Modules\Quiz\Enum\QuizQuestionTypes::MULTICHOICE->value:
                        // For multiple choice, we receive an array of answers
                        if (is_array($userAnswer)) {
                            // Process each selected answer
                            $allCorrect = true;
                            $selectedAnswers = QuizAnswer::whereIn('id', $userAnswer)->get();

                            // Check if all selected answers are correct
                            foreach ($selectedAnswers as $answer) {
                                if (! $answer->correct) {
                                    $allCorrect = false;
                                    break;
                                }
                            }

                            // Check if all correct answers were selected
                            $correctAnswersCount = QuizAnswer::where('quiz_question_id', $question->id)
                                ->where('correct', true)
                                ->count();

                            if ($allCorrect && count($userAnswer) === $correctAnswersCount) {
                                $totalScore += $question->marks_per_question;
                            }

                            // Store the first answer ID for simplicity
                            // (we'll need a better way to store multiple answers)
                            if (count($userAnswer) > 0) {
                                $quizUserAnswer->quiz_answer_id = $userAnswer[0];
                            }
                        }
                        break;

                    case \Modules\Quiz\Enum\QuizQuestionTypes::TRUEFALSE->value:
                        $quizUserAnswer->quiz_answer_id = $userAnswer;

                        // Check if answer is correct
                        $correctAnswer = QuizAnswer::where('id', $userAnswer)
                            ->where('correct', true)
                            ->first();

                        if ($correctAnswer) {
                            $totalScore += $question->marks_per_question;
                        }
                        break;
                }

                // Save user answer
                $quizUserAnswer->save();
            }
        }

        // Record score
        $attempts = QuizScore::where('user_id', $user->id)
            ->where('quiz_id', $quiz->id)
            ->count() + 1;

        $quizScore = new QuizScore;
        $quizScore->user_id = $user->id;
        $quizScore->quiz_id = $quiz->id;
        $quizScore->score = $totalScore;
        $quizScore->attempts = $attempts;
        $quizScore->save();

        return redirect()->route('quiz.results', ['id' => $quiz->id, 'attempt' => $quizScore->id])
            ->with('success', 'Quiz completed successfully!');
    }

    /**
     * Display quiz results.
     */
    public function results($id, $attempt)
    {
        $quiz = Quiz::with(['quiz_questions.quiz_answers'])->findOrFail($id);
        $score = QuizScore::findOrFail($attempt);

        // Make sure the score belongs to the current user
        if ($score->user_id != Auth::id() && ! Auth::user()->can('view all quiz results')) {
            abort(403, 'You are not authorized to view these results');
        }

        // Get user answers for this quiz
        $userAnswers = QuizUserAnswer::where('user_id', $score->user_id)
            ->where('quiz_id', $quiz->id)
            ->get()
            ->keyBy('quiz_question_id');

        return view('quiz::results', compact('quiz', 'score', 'userAnswers'));
    }
}
