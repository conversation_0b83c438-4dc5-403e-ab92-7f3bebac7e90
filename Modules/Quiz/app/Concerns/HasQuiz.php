<?php

namespace Modules\Quiz\Concerns {

    use <PERSON><PERSON><PERSON>\Quiz\Models\Quiz;

    trait HasQuiz
    {
        public function resourceUrl($action): string
        {
            return $this->filament_resource::getUrl($action, ['record' => $this->id]);
        }

        public function quizzes()
        {
            return $this->morphMany(Quiz::class, 'quizzable');
        }
    }
}
