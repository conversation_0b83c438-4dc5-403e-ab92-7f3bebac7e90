<?php

namespace Modules\Quiz\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Quiz\Filament\Resources\QuizResource\RelationManagers\QuestionsRelationManager;
use Modules\Quiz\Filament\Resources\QuizResource\RelationManagers\ScoresRelationManager;
use Modules\Quiz\Models\Quiz;

class QuizResource extends Resource
{
    protected static ?string $model = Quiz::class;

    protected static ?string $navigationIcon = 'heroicon-o-academic-cap';

    protected static ?string $navigationGroup = 'Education';

    public static function getLabel(): ?string
    {
        return __('Quiz');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Quizzes');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make(__('Quiz Settings'))
                    ->schema([
                        Forms\Components\TextInput::make('min_score')
                            ->label(__('Minimum Passing Score'))
                            ->helperText(__('The minimum score required to pass this quiz'))
                            ->numeric()
                            ->required()
                            ->minValue(0),

                        Forms\Components\Toggle::make('visible')
                            ->label(__('Visible to Users'))
                            ->helperText(__('When enabled, students can see and take this quiz')),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('quizzable.title')
                    ->label(__('Associated With'))
                    ->url(fn ($record) => ($record->quizzable->resourceUrl('edit')))
                    ->searchable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('quizzable_type')
                    ->label(__('Type'))
                    ->formatStateUsing(fn (string $state): string => class_basename($state))
                    ->sortable(),

                Tables\Columns\TextColumn::make('quiz_questions_count')
                    ->label(__('Questions'))
                    ->counts('quiz_questions'),

                Tables\Columns\TextColumn::make('min_score')
                    ->label(__('Min. Score')),

                Tables\Columns\ToggleColumn::make('visible')
                    ->label(__('Visible')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('quizzable_type')
                    ->label(__('Associated Type'))
                    ->options([
                        'Modules\\Course\\Models\\Course' => __('Course'),
                        'Modules\\Course\\Models\\Module' => __('Module'),
                        'Modules\\Course\\Models\\Unit' => __('Unit'),
                        'Modules\\Course\\Models\\Lesson' => __('Lesson'),
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label(__('Manage')),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            QuestionsRelationManager::class,
            ScoresRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => \Modules\Quiz\Filament\Resources\QuizResource\Pages\ListQuizzes::route('/'),
            'edit' => \Modules\Quiz\Filament\Resources\QuizResource\Pages\EditQuiz::route('/{record}/edit'),
        ];
    }
}
