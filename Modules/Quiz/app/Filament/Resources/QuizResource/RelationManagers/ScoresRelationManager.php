<?php

namespace Modules\Quiz\Filament\Resources\QuizResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ScoresRelationManager extends RelationManager
{
    protected static string $relationship = 'scores';

    protected static ?string $recordTitleAttribute = 'id';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('score')
                    ->required()
                    ->numeric()
                    ->minValue(0),
                Forms\Components\TextInput::make('attempts')
                    ->required()
                    ->numeric()
                    ->minValue(1)
                    ->default(1),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label(__('Student'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('score')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Attempted At'))
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('attempts')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_passing')
                    ->label(__('Passed'))
                    ->boolean()
                    ->getStateUsing(function ($record) {
                        return $record->isPassing();
                    }),
                Tables\Columns\TextColumn::make('percentage')
                    ->label(__('Percentage'))
                    ->getStateUsing(function ($record) {
                        return $record->getPercentage().'%';
                    }),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->label(__('Student')),
                Tables\Filters\Filter::make('passing_status')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->options([
                                'passed' => __('Passed'),
                                'failed' => __('Failed'),
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['status'] === 'passed',
                                fn (Builder $query, $date): Builder => $query->whereHas('quiz', function ($subQuery) {
                                    $subQuery->whereColumn('quiz_scores.score', '>=', 'quizzes.min_score');
                                }),
                                fn (Builder $query, $date): Builder => $query->whereHas('quiz', function ($subQuery) {
                                    $subQuery->whereColumn('quiz_scores.score', '<', 'quizzes.min_score');
                                }),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
