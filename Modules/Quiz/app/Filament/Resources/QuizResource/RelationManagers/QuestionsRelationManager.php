<?php

namespace Modules\Quiz\Filament\Resources\QuizResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Modules\Quiz\Enum\QuizQuestionTypes;

class QuestionsRelationManager extends RelationManager
{
    protected static string $relationship = 'quiz_questions';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('type')
                    ->label(__('Type'))
                    ->live()
                    ->afterStateUpdated(function (callable $set, callable $get, $state): void {
                        // Clear existing answers when type changes
                        $set('answers', []);

                        // For True/False questions, create predefined options
                        if ($state == QuizQuestionTypes::TRUEFALSE->value) {
                            $set('answers', [
                                ['answer' => 'True', 'correct' => false],
                                ['answer' => 'False', 'correct' => false],
                            ]);
                        }
                    })
                    ->required()
                    ->options(QuizQuestionTypes::class),

                Forms\Components\TextInput::make('question')
                    ->label(__('Question'))
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('marks_per_question')
                    ->label(__('Points/Marks'))
                    ->helperText(__('Points awarded for a correct answer'))
                    ->required()
                    ->numeric()
                    ->minValue(1)
                    ->default(1),

                Forms\Components\TextInput::make('duration')
                    ->label(__('Time Limit (seconds)'))
                    ->helperText(__('Set to 0 for no time limit'))
                    ->numeric()
                    ->default(60),

                // Answers repeater
                Forms\Components\Repeater::make('answers')
                    ->relationship('quiz_answers')
                    ->columnSpan(2)
                    ->live(true)
                    ->hidden(fn (callable $get) => $get('type') == QuizQuestionTypes::TEXT->value)
                    ->schema([
                        Forms\Components\TextInput::make('answer')
                            ->required()
                            ->maxLength(255)
                            ->label(__('Answer')),

                        Forms\Components\Toggle::make('correct')
                            ->label(__('Correct Answer'))
                            ->default(false)
                            ->live()
                            ->afterStateUpdated(function (callable $set, callable $get, $state, $livewire) {
                                // For single choice questions, only allow one correct answer
                                if ($state && $get('../../type') == QuizQuestionTypes::SINGLECHOICE->value) {
                                    $answers = $get('../../answers') ?: [];
                                    foreach ($answers as $key => $value) {
                                        if ($key !== $livewire->repeaterItemIDs[$livewire->uuid][$livewire->mountedActions[0] ?? 'repeater']) {
                                            data_set($answers, "{$key}.correct", false);
                                        }
                                    }
                                    $set('../../answers', $answers);
                                }
                            }),
                    ])
                    ->itemLabel(fn (array $state): ?string => $state['answer'] ?? null)
                    ->addable(fn (callable $get) => $get('type') != QuizQuestionTypes::TRUEFALSE->value)
                    ->deletable(fn (callable $get) => $get('type') != QuizQuestionTypes::TRUEFALSE->value)
                    ->reorderable(fn (callable $get) => $get('type') != QuizQuestionTypes::TRUEFALSE->value)
                    ->defaultItems(fn (callable $get) => $get('type') == QuizQuestionTypes::TRUEFALSE->value ? 2 : 2)
                    ->minItems(fn (callable $get) => match ($get('type')) {
                        QuizQuestionTypes::TRUEFALSE->value => 2,
                        QuizQuestionTypes::TEXT->value => 0,
                        default => 2
                    }
                    )
                    ->maxItems(fn (callable $get) => match ($get('type')) {
                        QuizQuestionTypes::TRUEFALSE->value => 2,
                        QuizQuestionTypes::TEXT->value => 0,
                        default => 10
                    }
                    ),

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('question')
            ->columns([
                Tables\Columns\TextColumn::make('question')
                    ->limit(50)
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->formatStateUsing(fn ($state) => $state->getLabel())
                    ->sortable(),
                Tables\Columns\TextColumn::make('marks_per_question')
                    ->label(__('Marks'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('duration')
                    ->label(__('Time (seconds)'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('quiz_answers_count')
                    ->label(__('Answers'))
                    ->counts('quiz_answers')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
