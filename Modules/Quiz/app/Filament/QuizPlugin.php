<?php

namespace Modules\Quiz\Filament;

use Filament\Contracts\Plugin;
use Filament\Panel;

class QuizPlugin implements Plugin
{
    public function getId(): string
    {
        return 'quiz';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverResources(
            in: __DIR__.'/Resources',
            for: 'Modules\\Quiz\\Filament\\Resources'
        );
    }

    public function boot(Panel $panel): void {}
}
