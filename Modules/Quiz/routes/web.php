<?php

use Illuminate\Support\Facades\Route;
use Modules\Quiz\Http\Controllers\QuizController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::middleware(['auth'])->group(function () {
    Route::resource('quiz', QuizController::class)->names('quiz');

    // Quiz taking and results routes
    Route::post('quiz/{id}/submit', [QuizController::class, 'submit'])->name('quiz.submit');
    Route::get('quiz/{id}/results/{attempt}', [QuizController::class, 'results'])->name('quiz.results');
});
