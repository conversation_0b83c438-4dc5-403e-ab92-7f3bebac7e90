# Quiz Module Documentation

## Overview

The Quiz module is a flexible assessment system that can be attached to various educational entities including Courses, Modules, Units, and Lessons. It provides a robust framework for creating quizzes with multiple question types, automatic grading, and detailed reporting.

## Features

- **Polymorphic Relationships**: Attach quizzes to any learning resource (Course, Module, Unit, Lesson)
- **Multiple Question Types**: 
  - Single Choice
  - Multiple Choice
  - True/False
  - Text/Essay Questions
- **Automatic Grading**: Instant scoring for objective questions
- **Score Tracking**: Record and display user attempts and scores
- **Results Review**: Detailed feedback showing correct and incorrect answers
- **Admin Management**: Full Filament-based administrative interface

## Usage

### Creating a Quiz

Quizzes are created and managed through the Filament admin panel:

1. Navigate to the Quiz section in the admin panel
2. Set the minimum passing score and visibility settings
3. Add questions via the "Questions" tab
4. For each question:
   - Select the question type
   - Enter the question text
   - Set the marks/points for the question
   - Add possible answers (except for text questions)
   - Mark the correct answer(s)

### Attaching Quizzes to Educational Entities

The `HasQuiz` trait is used to make any model "quizzable". It's already applied to:
- Course
- Module
- Unit
- Lesson

When editing these items in Filament, you'll find a Quizzes section that allows you to add and manage associated quizzes.

### Taking a Quiz

End users can take quizzes through the front-end interface:

1. Navigate to a course, module, unit, or lesson that contains a quiz
2. Click on the quiz to begin
3. Answer all questions
4. Submit the quiz for immediate results
5. Review correct and incorrect answers
6. Retake the quiz if needed

### Viewing Results

Quiz results are displayed immediately after submission, showing:
- Total score
- Percentage
- Pass/Fail status
- Correct and incorrect answers
- Explanation of correct answers

Administrators can view all quiz attempts and scores in the Filament admin panel.

## Technical Implementation

### Models

- **Quiz**: The main quiz entity with polymorphic relationships to quizzable items
- **QuizQuestion**: Individual questions with type, marks, and duration
- **QuizAnswer**: Possible answers for questions, with a flag for correct answers
- **QuizUserAnswer**: Records of user-submitted answers
- **QuizScore**: Records of quiz attempts and scores

### Relationships

- Quiz → Quizzable: `morphTo()` relationship to the parent entity
- Quiz → Questions: One-to-many relationship
- Question → Answers: One-to-many relationship
- User → UserAnswers: One-to-many relationship
- User → Scores: One-to-many relationship

### Question Types

The `QuizQuestionTypes` enum defines the following question types:

1. `SINGLECHOICE (1)`: One correct answer from multiple options
2. `MULTICHOICE (2)`: Multiple correct answers possible
3. `TRUEFALSE (3)`: Boolean true/false questions
4. `TEXT (4)`: Free-form text responses (requires manual grading)

### Scoring

- For single choice and true/false questions: Full marks if the correct option is selected
- For multiple choice questions: Full marks if all correct options (and only correct options) are selected
- For text questions: Manual grading required

## Front-end Integration

The Quiz module provides Blade templates that can be included in your application:

- `index.blade.php`: Lists available quizzes
- `show.blade.php`: Displays the quiz questions for users to answer
- `results.blade.php`: Shows detailed quiz results after submission

## API Integration

The Quiz module offers REST API endpoints for integration with mobile apps or other systems:

- `GET /api/v1/quiz`: List all quizzes
- `GET /api/v1/quiz/{id}`: Get a specific quiz
- `POST /api/v1/quiz/{id}/submit`: Submit quiz answers

## Customization

### Extending Question Types

To add new question types:

1. Add a new case to the `QuizQuestionTypes` enum
2. Implement the UI for the new question type in the form components
3. Add the scoring logic to the submission controller

### Styling

The Quiz module uses Tailwind CSS for styling and can be customized by modifying the view templates in the `resources/views` directory.

## Troubleshooting

### Common Issues

1. **Quiz Not Displaying**: Check the visibility setting in the admin panel
2. **Scoring Issues**: Verify that correct answers are properly marked in the admin panel
3. **Multiple Choice Scoring**: Ensure all correct answers are selected and no incorrect ones

## Future Enhancements

Planned features for future releases:

- Time limits for quizzes
- Question randomization
- Question pools/banks
- Image and media support in questions
- More detailed analytics and reporting