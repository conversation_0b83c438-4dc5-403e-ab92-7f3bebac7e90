<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-2xl font-bold">{{ $quiz->quizzable->title }} - Quiz Results</h1>
                        <div>
                            <a href="{{ route('quiz.show', $quiz->id) }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                                Retake Quiz
                            </a>
                        </div>
                    </div>
                    
                    <div class="bg-gray-100 p-4 rounded-md mb-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="text-center p-2 bg-white rounded-md shadow-sm">
                                <p class="text-gray-500 text-sm">Score</p>
                                <p class="text-2xl font-bold">{{ $score->score }} / {{ $quiz->getTotalPossibleScore() }}</p>
                            </div>
                            <div class="text-center p-2 bg-white rounded-md shadow-sm">
                                <p class="text-gray-500 text-sm">Percentage</p>
                                <p class="text-2xl font-bold">{{ $score->getPercentage() }}%</p>
                            </div>
                            <div class="text-center p-2 bg-white rounded-md shadow-sm">
                                <p class="text-gray-500 text-sm">Result</p>
                                <p class="text-2xl font-bold {{ $score->isPassing() ? 'text-green-600' : 'text-red-600' }}">
                                    {{ $score->isPassing() ? 'Passed' : 'Failed' }}
                                </p>
                            </div>
                        </div>
                        <div class="mt-4 text-center">
                            <p class="text-gray-500 text-sm">Attempt #{{ $score->attempts }}</p>
                            <p class="text-gray-500 text-sm">Completed on {{ $score->created_at->format('M d, Y \a\t h:i A') }}</p>
                        </div>
                    </div>
                    
                    <div class="space-y-8">
                        @foreach($quiz->quiz_questions as $question)
                        <div class="p-4 border rounded-md" id="question-{{ $question->id }}">
                            <div class="flex justify-between items-start">
                                <h3 class="text-lg font-medium mb-2">{{ $loop->iteration }}. {{ $question->question }}</h3>
                                <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                    {{ $question->marks_per_question }} {{ $question->marks_per_question > 1 ? 'points' : 'point' }}
                                </span>
                            </div>

                            <div class="mt-4">
                                @if($question->type->value == \Modules\Quiz\Enum\QuizQuestionTypes::TEXT->value)
                                    <div class="mt-2">
                                        <p class="text-sm text-gray-500 font-medium">Your Answer:</p>
                                        <div class="mt-1 p-3 bg-gray-50 border rounded-md">
                                            <p>{{ $userAnswers[$question->id]->text_answer ?? 'No answer provided' }}</p>
                                        </div>
                                        <p class="mt-2 text-sm text-gray-500">Text answers require manual grading.</p>
                                    </div>
                                
                                @elseif($question->type->value == \Modules\Quiz\Enum\QuizQuestionTypes::SINGLECHOICE->value || 
                                       $question->type->value == \Modules\Quiz\Enum\QuizQuestionTypes::TRUEFALSE->value)
                                    <fieldset class="space-y-2">
                                        @foreach($question->quiz_answers as $answer)
                                            <div class="flex items-center p-2 rounded-md 
                                                {{ isset($userAnswers[$question->id]) && $userAnswers[$question->id]->quiz_answer_id == $answer->id ? 'bg-blue-50' : '' }}
                                                {{ $answer->correct ? 'bg-green-50 border-green-200 border' : '' }}">
                                                <input type="radio" disabled 
                                                    {{ isset($userAnswers[$question->id]) && $userAnswers[$question->id]->quiz_answer_id == $answer->id ? 'checked' : '' }}
                                                    class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                                <label class="ml-3 block text-sm font-medium 
                                                    {{ isset($userAnswers[$question->id]) && $userAnswers[$question->id]->quiz_answer_id == $answer->id && $answer->correct ? 'text-green-700 font-semibold' : '' }}
                                                    {{ isset($userAnswers[$question->id]) && $userAnswers[$question->id]->quiz_answer_id == $answer->id && !$answer->correct ? 'text-red-700 font-semibold' : '' }}
                                                    {{ $answer->correct && (!isset($userAnswers[$question->id]) || $userAnswers[$question->id]->quiz_answer_id != $answer->id) ? 'text-green-700 font-semibold' : 'text-gray-700' }}">
                                                    {{ $answer->answer }}
                                                    @if($answer->correct)
                                                        <span class="text-green-600 ml-2">(Correct)</span>
                                                    @endif
                                                </label>
                                            </div>
                                        @endforeach
                                    </fieldset>
                                
                                @elseif($question->type->value == \Modules\Quiz\Enum\QuizQuestionTypes::MULTICHOICE->value)
                                    <fieldset class="space-y-2">
                                        @php
                                            $userSelectedAnswers = collect();
                                            // In a real implementation, you'd have a proper way to fetch multiple selected answers
                                            // For now, just using the first one as a placeholder
                                            if (isset($userAnswers[$question->id])) {
                                                $userSelectedAnswers = collect([$userAnswers[$question->id]->quiz_answer_id]);
                                            }
                                        @endphp
                                        
                                        @foreach($question->quiz_answers as $answer)
                                            <div class="flex items-center p-2 rounded-md
                                                {{ $userSelectedAnswers->contains($answer->id) ? 'bg-blue-50' : '' }}
                                                {{ $answer->correct ? 'bg-green-50 border-green-200 border' : '' }}">
                                                <input type="checkbox" disabled 
                                                    {{ $userSelectedAnswers->contains($answer->id) ? 'checked' : '' }}
                                                    class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                                <label class="ml-3 block text-sm font-medium
                                                    {{ $userSelectedAnswers->contains($answer->id) && $answer->correct ? 'text-green-700 font-semibold' : '' }}
                                                    {{ $userSelectedAnswers->contains($answer->id) && !$answer->correct ? 'text-red-700 font-semibold' : '' }}
                                                    {{ $answer->correct && !$userSelectedAnswers->contains($answer->id) ? 'text-green-700 font-semibold' : 'text-gray-700' }}">
                                                    {{ $answer->answer }}
                                                    @if($answer->correct)
                                                        <span class="text-green-600 ml-2">(Correct)</span>
                                                    @endif
                                                </label>
                                            </div>
                                        @endforeach
                                    </fieldset>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    <div class="mt-8 flex justify-between">
                        <a href="{{ route('quiz.show', $quiz->id) }}" class="inline-flex items-center px-4 py-2 bg-gray-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                            Retake Quiz
                        </a>
                        
                        <a href="{{ url()->previous() }}" class="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:text-gray-500 focus:outline-none focus:border-blue-300 focus:ring focus:ring-blue-200 active:text-gray-800 active:bg-gray-50 disabled:opacity-25 transition ease-in-out duration-150">
                            Back
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>