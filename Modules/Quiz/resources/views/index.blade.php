<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h1 class="text-2xl font-bold mb-6">Available Quizzes</h1>
                    
                    @if($quizzes->isEmpty())
                        <div class="text-center py-8">
                            <p class="text-gray-500">No quizzes are available at this time.</p>
                        </div>
                    @else
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($quizzes as $quiz)
                                <div class="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                                    <div class="p-4">
                                        <h2 class="text-lg font-semibold mb-2">{{ $quiz->quizzable->title }}</h2>
                                        <p class="text-sm text-gray-600 mb-2">
                                            <span class="font-medium">Type:</span> {{ class_basename($quiz->quizzable_type) }}
                                        </p>
                                        <p class="text-sm text-gray-600 mb-2">
                                            <span class="font-medium">Questions:</span> {{ $quiz->quiz_questions->count() }}
                                        </p>
                                        
                                        @php
                                            $userScore = $quiz->getLatestScoreFor(auth()->user());
                                        @endphp
                                        
                                        @if($userScore)
                                            <div class="mb-4 mt-2 bg-gray-50 p-2 rounded text-sm">
                                                <p class="font-medium">Your last attempt:</p>
                                                <p>Score: {{ $userScore->score }}/{{ $quiz->getTotalPossibleScore() }} ({{ $userScore->getPercentage() }}%)</p>
                                                <p class="{{ $userScore->isPassing() ? 'text-green-600' : 'text-red-600' }} font-medium">
                                                    {{ $userScore->isPassing() ? 'Passed' : 'Failed' }}
                                                </p>
                                            </div>
                                        @endif
                                        
                                        <a href="{{ route('quiz.show', $quiz->id) }}" class="mt-2 inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                            {{ $userScore ? 'Retake Quiz' : 'Start Quiz' }}
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
