<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-2xl font-bold">{{ $quiz->quizzable->title }} - Quiz</h1>
                        
                        @if($previousScore)
                            <div class="text-sm bg-gray-100 p-3 rounded-md">
                                <p class="font-semibold">Your previous score: {{ $previousScore->score }} / {{ $quiz->getTotalPossibleScore() }}</p>
                                <p>{{ $previousScore->isPassing() ? 'Passed' : 'Failed' }} - {{ $previousScore->getPercentage() }}%</p>
                            </div>
                        @endif
                    </div>
                    
                    <form action="{{ route('quiz.submit', ['id' => $quiz->id]) }}" method="POST">
                        @csrf
                        <div class="space-y-8">
                            @foreach($quiz->quiz_questions as $question)
                            <div class="p-4 border rounded-md" id="question-{{ $question->id }}">
                                <div class="flex justify-between items-start">
                                    <h3 class="text-lg font-medium mb-2">{{ $loop->iteration }}. {{ $question->question }}</h3>
                                    <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                        {{ $question->marks_per_question }} {{ $question->marks_per_question > 1 ? 'points' : 'point' }}
                                    </span>
                                </div>

                                <div class="mt-4">
                                    @if($question->type->value == \Modules\Quiz\Enum\QuizQuestionTypes::TEXT->value)
                                        <div class="mt-2">
                                            <textarea name="answers[{{ $question->id }}]" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" placeholder="Type your answer here..."></textarea>
                                        </div>
                                    
                                    @elseif($question->type->value == \Modules\Quiz\Enum\QuizQuestionTypes::SINGLECHOICE->value)
                                        <fieldset>
                                            <div class="space-y-2">
                                                @foreach($question->quiz_answers as $answer)
                                                <div class="flex items-center">
                                                    <input id="answer-{{ $answer->id }}" name="answers[{{ $question->id }}]" type="radio" value="{{ $answer->id }}" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                                    <label for="answer-{{ $answer->id }}" class="ml-3 block text-sm font-medium text-gray-700">{{ $answer->answer }}</label>
                                                </div>
                                                @endforeach
                                            </div>
                                        </fieldset>
                                    
                                    @elseif($question->type->value == \Modules\Quiz\Enum\QuizQuestionTypes::MULTICHOICE->value)
                                        <fieldset>
                                            <div class="space-y-2">
                                                @foreach($question->quiz_answers as $answer)
                                                <div class="flex items-center">
                                                    <input id="answer-{{ $answer->id }}" name="answers[{{ $question->id }}][]" type="checkbox" value="{{ $answer->id }}" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                                    <label for="answer-{{ $answer->id }}" class="ml-3 block text-sm font-medium text-gray-700">{{ $answer->answer }}</label>
                                                </div>
                                                @endforeach
                                            </div>
                                        </fieldset>
                                    
                                    @elseif($question->type->value == \Modules\Quiz\Enum\QuizQuestionTypes::TRUEFALSE->value)
                                        <fieldset>
                                            <div class="space-y-2">
                                                @foreach($question->quiz_answers as $answer)
                                                <div class="flex items-center">
                                                    <input id="answer-{{ $answer->id }}" name="answers[{{ $question->id }}]" type="radio" value="{{ $answer->id }}" class="h-4 w-4 border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                                    <label for="answer-{{ $answer->id }}" class="ml-3 block text-sm font-medium text-gray-700">{{ $answer->answer }}</label>
                                                </div>
                                                @endforeach
                                            </div>
                                        </fieldset>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                        
                        <div class="mt-8">
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                                Submit Quiz
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>