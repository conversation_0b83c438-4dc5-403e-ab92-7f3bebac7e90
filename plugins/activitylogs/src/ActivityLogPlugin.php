<?php

namespace ArabicForAll\ActivityLog;

use Filament\Contracts\Plugin;
use Filament\Panel;

class ActivityLogPlugin implements Plugin
{
    public function getId(): string
    {
        return 'activitylog';
    }

    public function register(Panel $panel): void
    {
        $panel->discoverPages(
            in: __DIR__.'/Filament/Pages',
            for: 'ArabicForAll\\ActivityLog\\Filament\\Pages'
        );
    }

    public function boot(Panel $panel): void {}
}
