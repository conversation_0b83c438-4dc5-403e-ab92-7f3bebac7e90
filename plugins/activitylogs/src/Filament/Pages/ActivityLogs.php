<?php

namespace ArabicForAll\ActivityLog\Filament\Pages;

use Exception;
use Filament\Forms\Components\Field;
use Filament\Forms\Components\MorphToSelect;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\Page;
use Filament\Tables\Concerns\CanPaginateRecords;
use Illuminate\Contracts\Pagination\CursorPaginator;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Support\Collection;
use Livewire\Features\SupportPagination\HandlesPagination;
use Modules\Institute\Filament\Resources\UserResource;

class ActivityLogs extends Page implements HasForms
{
    use CanPaginateRecords;
    use HandlesPagination;
    use InteractsWithFormActions;
    use InteractsWithRecord;

    protected static string $resource = UserResource::class;

    protected static string $view = 'activitylogs::admin.list-activities';

    protected static Collection $fieldLabelMap;

    public function mount($record)
    {
        $this->record = $this->resolveRecord($record);
    }

    public function getBreadcrumb(): string
    {
        return static::$breadcrumb ?? __('activities');
    }

    //    public function getTitle(): string
    //    {
    //        return __(':record of :name', ['record' => $this->getBreadcrumb(), 'name' => $this->record->name]);
    //    }

    public function getActivities(): Paginator|CursorPaginator
    {
        return $this->paginateTableQuery(
            $this->record->activities()->with('causer')->latest()->getQuery()
        );
    }

    public function getFieldLabel(string $name): string
    {
        static::$fieldLabelMap ??= $this->createFieldLabelMap();

        return static::$fieldLabelMap[$name] ?? $name;
    }

    protected function createFieldLabelMap(): Collection
    {
        $form = static::getResource()::form(new Form($this));

        $components = collect($form->getComponents());
        $extracted = collect();

        while (($component = $components->shift()) !== null) {
            if ($component instanceof Field || $component instanceof MorphToSelect) {
                $extracted->push($component);

                continue;
            }

            $children = $component->getChildComponents();

            if (count($children) > 0) {
                $components = $components->merge($children);

                continue;
            }

            $extracted->push($component);
        }

        return $extracted
            ->filter(fn ($field) => $field instanceof Field)
            ->mapWithKeys(fn (Field $field) => [
                $field->getName() => $field->getLabel(),
            ]);
    }

    public function canRestoreActivity(): bool
    {
        return static::getResource()::canRestore($this->record);
    }

    public function restoreActivity(int|string $key)
    {
        if (! static::getResource()::canRestore($this->record)) {
            abort(403);
        }

        $activity = $this->record->activities()
            ->whereKey($key)
            ->first();

        $oldProperties = data_get($activity, 'properties.old');

        if ($oldProperties === null) {
            Notification::make()
                ->title(__('Restore Failed'))
                ->danger()
                ->send();

            return;
        }

        try {
            $this->record->update($oldProperties);

            Notification::make()
                ->title(__('Restore Successful'))
                ->success()
                ->send();
        } catch (Exception $e) {
            Notification::make()
                ->title(__('Restore Failed'))
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getIdentifiedTableQueryStringPropertyNameFor(string $property): string
    {
        return $property;
    }

    protected function getDefaultTableRecordsPerPageSelectOption(): int
    {
        return 10;
    }

    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [10, 25, 50];
    }
}
