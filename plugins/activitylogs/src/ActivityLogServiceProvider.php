<?php

namespace ArabicForAll\ActivityLog;

use Filament\Panel;
use Illuminate\Support\ServiceProvider;

class ActivityLogServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        Panel::configureUsing(fn (Panel $panel) => ($panel->getId() !== 'admin') || $panel->plugin(new ActivityLogPlugin));
    }

    public function boot(): void
    {
        $this->loadViewsFrom(__DIR__.'/../resources/views', 'activitylogs');
        $this->loadMigrationsFrom(__DIR__.'/../database/migrations');

    }
}
