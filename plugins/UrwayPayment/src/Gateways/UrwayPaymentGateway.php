<?php

namespace Plugins\UrwayPayment\Gateways;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\Core\Classes\CurrencyExchangeHelper;
use Modules\Payment\Gateways\AbstractPaymentGateway;

class UrwayPaymentGateway extends AbstractPaymentGateway
{
    /**
     * Constructor.
     */
    public function __construct()
    {
        parent::__construct(config('payment.gateways.urway', []));
    }

    /**
     * Get the display name of the payment gateway
     */
    public function getName(): string
    {
        return 'UrWay';
    }

    /**
     * Get the unique identifier of the payment gateway
     */
    public function getIdentifier(): string
    {
        return 'urway';
    }

    /**
     * Get the description of the payment gateway
     */
    public function getDescription(): string
    {
        return 'Pay securely using UrWay';
    }

    /**
     * Get the icon/logo of the payment gateway
     */
    public function getIcon(): ?string
    {
        return asset('images/payment-gateways/urway.png');
    }

    /**
     * Process the payment
     */
    public function processPayment(array $paymentData): array
    {
        Log::info('URWAY Payment Gateway Request', ['request' => $paymentData]);

//        try {

            if ($paymentData['currency'] != 'SAR') {
                // exchange rate to SAR
                $paymentData['amount'] = CurrencyExchangeHelper::convertCurrency($paymentData['amount'], $paymentData['currency'], 'SAR') * 1.175;
            }


            $orderId = $paymentData[''] ?? uniqid();

            $terminalId = $this->config['terminal_id']; // Will be provided by URWAY
            $password = $this->config['password']; // Will be provided by URWAY
            $merchant_key = $this->config['secret_key']; // Will be provided by URWAY
            $currency_code = $paymentData['currency'] ?? 'SAR';
            $paymentData['amount'] = sprintf('%.2f', $paymentData['amount']);
            $txn_details = $orderId.'|'.$terminalId.'|'.$password.'|'.$merchant_key.'|'.$paymentData['amount'].'|'.$currency_code;
            $hash = hash('sha256', $txn_details);

            $fields = [
                'trackid' => $orderId,
                'terminalId' => $terminalId,
                'customerEmail' => $paymentData['customer_email'] ?? '<EMAIL>',
                'action' => '1',  // Action is always 1
                'merchantIp' => $paymentData['ip'] ?? '***************',
                'password' => $password,
                'currency' => $currency_code,
                'country' => 'SA',
                'amount' => $paymentData['amount'],
                'udf1' => 'Test1',
                'udf2' => $paymentData['return_url'], // Response page URL
                'udf3' => 'ar',
                'udf4' => '',
                'udf5' => '',
                'requestHash' => $hash,  // Generated hash
            ];


            // Example payment flow for UrWay:
            // 1. Create a UrWay order
            // 2. Get the approval URL
            // 3. Redirect the customer to UrWay for payment approval
            // 4. Customer is redirected back after approval
            // 5. Capture the payment

            $response = Http::timeout(10)
                ->connectTimeout(10)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post('https://payments-dev.urway-tech.com/URWAYPGService/transaction/jsonProcess/JSONrequest', $fields);
            //        ->post('https://payments.urway-tech.com/URWAYPGService/transaction/jsonProcess/JSONrequest', $fields);

            // Get the response body as an object
            $result = $response->object();


            // In a real implementation, this would integrate with UrWay's SDK
            // to create an order and return a checkout URL

            if (! empty($result->payid) && ! empty($result->targetUrl)) {
                $approvalUrl = $result->targetUrl.'?paymentid='.$result->payid;
            } else {
                throw new \Exception('Payment creation failed: '.$result);
            }

            return [
                'success' => true,
                'message' => 'Redirecting to UrWay for payment approval',
                'transaction_id' => $orderId,
                'redirect_url' => $approvalUrl,
                'data' => [
                    'order_id' => $orderId,
                    'amount' => $paymentData['amount'],
                    'currency' => $paymentData['currency'] ?? 'SAR',
                ],
                'status' => 'processing',
            ];
//        } catch (\Exception $e) {
//            Log::error('UrWay Payment Error: '.$e->getMessage());
//
//            return [
//                'success' => false,
//                'message' => $e->getMessage(),
//                'status' => 'failed',
//            ];
//        }
    }

    /**
     * Handle webhook notifications from the payment gateway
     */
    public function handleWebhook(Request $request): array
    {
        return [
            'success' => true,
            'message' => 'Webhook received',
            'data' => $request->all(),
            'status' => 'processing',
        ];
    }

    /**
     * Verify the payment status
     */
    public function verifyPayment(string $paymentId): array
    {
        try {
            // In a real implementation, this would check the payment status
            // with UrWay's API

            // Simulate a successful payment verification
            return [
                'success' => true,
                'message' => 'Payment verified',
                'transaction_id' => $paymentId,
                'data' => [
                    'order_id' => $paymentId,
                    'status' => 'COMPLETED',
                ],
                'status' => 'completed',
            ];
        } catch (\Exception $e) {
            Log::error('UrWay Verification Error: '.$e->getMessage());

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'status' => 'failed',
            ];
        }
    }

    /**
     * Get frontend scripts required by this gateway
     */
    public function getScripts(): array
    {
        return [];
    }
}
