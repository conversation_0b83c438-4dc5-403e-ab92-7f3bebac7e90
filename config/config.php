<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Payment Module Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for the Payment module.
    |
    */

    // Default currency for payments
    'default_currency' => env('PAYMENT_DEFAULT_CURRENCY', 'USD'),

    // Payment gateway configurations
    'gateways' => [

        // 2Checkout configuration
        '2checkout' => [
            'api_url' => env('TWOCHECKOUT_API_URL', 'https://api.2checkout.com/rest/6.0/'),
            'enabled' => env('TWOCHECKOUT_ENABLED', true),
            'sandbox' => env('TWOCHECKOUT_SANDBOX', true),
            'seller_id' => env('TWOCHECKOUT_SELLER_ID', ''),
            'private_key' => env('TWOCHECKOUT_PRIVATE_KEY', ''),
            'publishable_key' => env('TWOCHECKOUT_PUBLISHABLE_KEY', ''),
        ],

    ],

    // Payment logging configuration
    'logging' => [
        'channel' => env('PAYMENT_LOG_CHANNEL', 'stack'),
    ],
];
